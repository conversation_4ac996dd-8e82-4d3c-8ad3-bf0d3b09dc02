from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash import BashOperator

PATH_TO_SCRIPT_FOLDER = "/usr/local/airflow/include/scripts"

default_args  = {
     'owner' : 'Oluwatomisin Soetan',
     'retries' : 5,
     'retry_dalay' : timedelta(minutes=2)
}
with DAG(
    dag_id = 'refresh_mgt_manual_entry',
    default_args = default_args,
    description =  'auto refresh manual entry for management report',
    start_date =  datetime(2024, 5, 3),
    catchup=False,
    schedule_interval = timedelta(weeks=1)
) as dag:
    task1 =  BashOperator(
        task_id = 'first_task',
        bash_command = 'python3 manual_entry_autorefresh.py',
        cwd=PATH_TO_SCRIPT_FOLDER,
    )

# Set the downstream dependencies (none in this case)
task1
