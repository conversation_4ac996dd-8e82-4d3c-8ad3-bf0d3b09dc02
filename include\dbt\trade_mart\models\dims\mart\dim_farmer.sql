{{ config(materialized='table') }}

with farm_location as (
select   row_number() over (partition by farmer_id order by created desc)  dupli , *
from {{source('workbench','crm_farmlocation')}} 
),

clean_location as (
select *
from farm_location
where dupli = 1),

a as (
    select farmer.* ,
            clean_location.farm_coordinates farm_coordinates_polygon,
            lga.name lga_of_origin,
            lga2.name lga_of_residence,
            state1.name state_of_origin,
            state2.name state_of_residence

    from {{source('workbench','crm_farmer')}} farmer
    left join  clean_location
    on farmer.id = clean_location.farmer_id
    left join {{source('workbench','location_lga')}} lga
    on farmer.lga_of_origin_id = lga.id
    left join {{source('workbench','location_lga')}} lga2
    on farmer.lga_of_residence_id = lga2.id
    left join {{source('workbench','location_state')}} state1
    on farmer.state_of_origin_id = state1.id
    left join {{source('workbench','location_state')}} state2
    on farmer.state_of_residence_id = state2.id
),
q as (
    select cell.*,user_.phone_number feo_phone_number
    from {{source('workbench','workbench_cell')}} cell
    left join {{source('workbench','account_user')}} user_
    on cell.outreach_officer_id = user_.id
),
r as (
    select coop.id,coop.name,coop.code,ward.name ward
    from {{source('workbench','workbench_cooperative')}} coop
    left join {{source('workbench','location_ward')}} ward
    on coop.ward_id = ward.id


),
bank_details as
(
    select bank_info.client_id ,string_agg(bank.name || ' : ' || bank_info.account_number ,', ') account_numbers
    from  {{source('workbench','crm_clientbankinformation')}} bank_info
    INNER JOIN   {{source('workbench','bank_bank')}}   bank
    ON bank_info.bank_id = bank.id
    where not bank_info.account_number  = ''
    group by bank_info.client_id
),
z as (
    SELECT DISTINCT a.farmer_id
        ,string_agg(c.code, ', ') AS crop_code
        ,string_agg(c.name, ', ') AS crop_name
    FROM {{source('workbench','crm_farmer_crop_type')}} a
    INNER JOIN {{source('workbench','inventory_item')}} b ON a.item_id = b.id
    INNER JOIN {{source('workbench','workbench_product')}} c ON b.product_id = c.id
    GROUP BY a.farmer_id  
),
final as (
    SELECT a.id, 
    a.created,a.updated,
    a.is_deleted, 
    a.folio_id, 
    a.title, 
    a.first_name,  
    a.last_name,  
    a.middle_name,
    a.address, 
    a.gender, 
    a.marital_status, 
    a.dob, a.phone, 
    a.village,
    a.farm_size, 
    a.passport_type, 
    a.passport_number, 
    a.nok_phone,
    a.nok_name, 
    a.nok_relationship, 
    a.bvn, 
    a.farm_coordinates, 
    a.farm_coordinates_polygon,
    a.is_blacklist, 
    a.languages, 
    a.client_id,
    a.warehouse_id,
    a.phone_invalid,
    a.phone_number_status,
    a.coordinate_status,
    a.id_status,
    a.is_id_verified,
    a.matched_name,
    a.name_matches_verified_id,
    z.crop_name,
    z.crop_code,
    q.name as feo_name,
    q.code as feo_code,
    q.feo_phone_number,
    a.cooperative_id ,
    r.name as cooperative_name ,
    r.code as cooperative_code,
    r.ward ,
    bank_details.account_numbers,
    a.lga_of_origin,
    a.lga_of_residence,
    a.state_of_origin,
    state_of_residence,
    q.tenant_id

FROM a
left join bank_details on  a.client_id  = bank_details.client_id
left join  q on a.cell_id = q.id
left join  r on a.cooperative_id = r.id
LEFT JOIN z ON a.id = z.farmer_id
)

select * from final
