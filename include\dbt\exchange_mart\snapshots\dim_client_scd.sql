{% snapshot dim_client_scd %}

{{
    config(
        target_schema='exchange_mart_snapshots',
        strategy='timestamp',
        unique_key='cid',
        updated_at='updated::timestamp',
        incremental_predicates=['dbt_valid_to is null']
        )
}}

SELECT created, updated, cid, rnb, email, phone, address,
		last_name, first_name, account_type, user_account_type,
        country_code, bvn_verification_message 

FROM {{ ref('dim_client') }}

{% endsnapshot %}