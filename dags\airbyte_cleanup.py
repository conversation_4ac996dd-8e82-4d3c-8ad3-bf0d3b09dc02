from datetime import datetime,timedelta
from airflow import DAG
from airflow.operators.bash  import Ba<PERSON><PERSON>perator
from custom_functions.airflow_email_util import failure_email

PATH_TO_SCRIPT_FOLDER = "/usr/local/airflow/include/scripts"

default_args  = {
     'owner' : 'Oluwatomisin Soetan',
     'retries' : 0,
     'retry_delay' : timedelta(minutes=2),
     'on_failure_callback': failure_email
}
with DAG(
    dag_id = 'airbyte_cleanup',
    default_args = default_args,
    description =  'Delete airbyte temp tables and truncate the raw_json table to 100 records',
    start_date =  datetime(2023, 4, 18,7),
    catchup=False,
    schedule_interval =  '0 0 * * 1'
) as dag:
    task1 =  BashOperator(
        task_id = 'first_task',
        bash_command = 'python3 airbyte_cleanup.py' ,
        cwd=PATH_TO_SCRIPT_FOLDER,
)


task1
