{{ config (materialized = 'table') }}

-- MOVE THIS DESCRIPTION TO A DESCRIPTION DOCUMENT: This is necessary, because it was discovered that some securities were wrongly matched with security types in ovs.crm_security
-- In an attempt to correct for this, this dim table has been created and will be further upgraded as dimmed necessary.
with
dim_security as (
	select sec.id, sec.code security_code, sec.name security_name,
			sec.security_type sec_security_type, board.name board_type_sec,
			case when board.name = 'SCIT' or board.name = 'Virtual'
					then board.name
				when sec.code like 'ABCP%'
					or sec.code like '%AF%'
					or sec.code like '%AI%'
					or sec.code like '%ATF%'
					or sec.code like 'C%'
					or sec.code like '%FETC%'
					or sec.code like 'NPRL%'
					or sec.code like 'D%'
					then sec.security_type
				when sec.code like 'O%'
                  	or sec.code like 'S%'
					then board.name
				else null
				end as security_type
			   		
	from  {{ source('ovs', 'crm_security') }} sec
	left join {{ source('ovs', 'crm_board') }} board
	 on sec.board_id = board.id
		)

select *

from dim_security