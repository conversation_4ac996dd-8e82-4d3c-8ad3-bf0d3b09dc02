{{ config (materialized = 'ephemeral') }}

with 
matched_exchange_transactions as (
	select distinct matched_id, deal_created_at, adjusted_deal_created_at
            , matched_price matched_price_per_unit, matched_units
            , matched_price/calc_volume_per_unit matched_price_per_kg, matched_units*calc_volume_per_unit matched_volume_kg, security_type, security_code
            , commodity_code, commodity, consolidated_commodity_name
            , security_location, contract_seller_location_code, contract_buyer_location_code
            , case when contract_buyer_location_code is null then security_location else contract_buyer_location_code end destination_location
	
	from {{ ref('fact_trade_individual_transactions') }}

	where matched_id is not null
			and order_type = 'Buy'
            and currency = 'Naira'
            and (oms_name = 'COMX' or oms_name is null)
            and security_type in ('Spot', 'Dawa', 'OTC')
            and consolidated_commodity_name in ('Maize', 'Soyabean', 'Sorghum', 'Cashew Nuts', 'Cocoa', 'Ginger',
                                                    'Paddy Rice', 'Sesame', 'Wheat')
	),


workbench_transactions as (
	select date(grn.created) date_, grn.item_code, grn.item_name, concat('W', grn.item_code) board_code, grn.grade, grn.net_weight
            , grn.price_per_tonne, grn.price_per_tonne/1000 price_per_kg, grn.transaction_type,	grn.grn_id
            , wh.location destination_location
            , CASE WHEN grn.item_code LIKE '%CSN%' THEN 'Cashew Nuts'
					WHEN (grn.item_code LIKE '%COC%' OR grn.item_code LIKE '%CCO%') THEN 'Cocoa'
					WHEN grn.item_code LIKE '%GNG%' THEN 'Ginger'
					WHEN grn.item_code LIKE '%MAZ%' THEN 'Maize'
					WHEN grn.item_code LIKE '%PRL%' THEN 'Paddy Rice'
					WHEN (grn.item_code LIKE '%SSM%' OR  grn.item_code LIKE '%SSC%') THEN 'Sesame'
					WHEN grn.item_code LIKE '%SGM%' THEN 'Sorghum'
					WHEN grn.item_code LIKE '%SBS%' THEN 'Soyabean'
					WHEN grn.item_code LIKE '%WHT%' THEN 'Wheat'
			ELSE grn.item_code
			END consolidated_commodity_name
			
	from {{ source ('trade_mart', 'fact_grn') }} grn
	left join {{ source ('trade_mart', 'dim_warehouse') }} wh
		on grn.warehouse_code = wh.warehouse_code
		
	where item_type = 'Commodity'
			and grn.tenant_id = '7'
			and grn.is_approved is true
			and grn.is_approval_completed is true
			and grn.is_reverted is false
			and grn.transaction_type in ('Trade', '') ---
			and (grn.item_code like '%CSN%' or grn.item_code like '%COC%' or grn.item_code like '%CCO%' or 
					grn.item_code like '%GNG%' or grn.item_code like '%MAZ%' or grn.item_code like '%PRL%' or 
					grn.item_code like '%SSM%' or grn.item_code like '%SSC%' or grn.item_code like '%SGM%' or 
					grn.item_code like '%SBS%' or grn.item_code like '%WHT%')
	),
	

unionized_ex_matched_wb_trans as (
	select date_, consolidated_commodity_name, item_name, item_code, board_code, 'GRN' security_type, grn_id tid, net_weight volume_kg, price_per_kg, destination_location

	from workbench_transactions
	
	UNION
	
	select adjusted_deal_created_at, consolidated_commodity_name, commodity, commodity_code, security_code, security_type, matched_id tid, matched_volume_kg, matched_price_per_kg, destination_location

	from matched_exchange_transactions
	),

normalized_unionized_ex_matched_wb_trans_price as (
	select price.*, log_."Logistics Differential per kg" log_diff_per_kg, (price.price_per_kg + coalesce(log_."Logistics Differential per kg",0)) landed_price, 
			price.volume_kg * (price.price_per_kg + coalesce(log_."Logistics Differential per kg",0)) landed_price_x_volume
			
	from unionized_ex_matched_wb_trans price
	left join {{ ref ('stg_logistics_grid') }} log_
	on price.consolidated_commodity_name = log_."Commodity"
		and price.destination_location = log_."Destination Location"
	),

avg_std_unionized_matched_trans as (
	select *, 
			avg(landed_price) over (partition by board_code, date_ order by date_) average_price_per_kg,
			coalesce(stddev(landed_price) over (partition by board_code, date_ order by date_), 0) stddev_price_per_kg,
			(avg(landed_price) over (partition by board_code, date_ order by date_) - coalesce(stddev(landed_price) over (partition by board_code, date_ order by date_), 0)) lower_band,
			(avg(landed_price) over (partition by board_code, date_ order by date_) + coalesce(stddev(landed_price) over (partition by board_code, date_ order by date_), 0)) upper_band
	
	from normalized_unionized_ex_matched_wb_trans_price
	),

without_outlier_unionized_matched_trans as (
	select *
	from avg_std_unionized_matched_trans
	where landed_price between lower_band and upper_band
	)

select *

from without_outlier_unionized_matched_trans