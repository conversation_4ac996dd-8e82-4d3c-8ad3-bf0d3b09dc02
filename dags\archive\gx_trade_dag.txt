from datetime import datetime,timedelta
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from gxoperator import GX<PERSON>perator
from credentials import db_conn

#define great expectations operator
gx = GXOperator()
source_engine, conn_source = db_conn()
# Define your Python function to run
def fact_matchedorder():    
    json_result  = gx.run_expectations(data_asset_name='fact_matchedorder',
                        expectation_suite_name='fact_matchedorder_expectations')
    gx.expectations_to_db(checkpoint_jsonresult=json_result,source_engine=source_engine)
    

def fact_closingprice():    
    json_result  = gx.run_expectations(data_asset_name='fact_closingprice',
                        expectation_suite_name='fact_closingprice_expectations')
    gx.expectations_to_db(checkpoint_jsonresult=json_result,source_engine=source_engine)
    

def fact_clientwalletlog():    
    json_result  = gx.run_expectations(data_asset_name='fact_clientwalletlog',
                        expectation_suite_name='fact_clientwalletlog_expectations')
    gx.expectations_to_db(checkpoint_jsonresult=json_result,source_engine=source_engine)
    

def fact_clientwallet():    
    json_result  = gx.run_expectations(data_asset_name='fact_clientwallet',
                        expectation_suite_name='fact_clientwallet_expectations')
    gx.expectations_to_db(checkpoint_jsonresult=json_result,source_engine=source_engine)
        
def fact_transactions():    
    json_result  = gx.run_expectations(data_asset_name='fact_transactions',
                        expectation_suite_name='fact_transactions_expectations')
    gx.expectations_to_db(checkpoint_jsonresult=json_result,source_engine=source_engine)
        
# Define your DAG
dag = DAG(
    'gx_trade_dag',
    description='Run a Python script in a DAG',
    start_date=datetime(2023, 10, 11),  # Specify the start date
    schedule_interval='0 0 * * 3',
    tags =  ['great_expectations','trade_mart']
)

# Create a PythonOperator that runs your Python function
fact_matchedorder_script = PythonOperator(
    task_id='fact_matchedorder_expectations',
    python_callable=fact_matchedorder,
    dag=dag,
)


fact_closingprice_script = PythonOperator(
    task_id='fact_closingprice_expectations',
    python_callable=fact_closingprice,
    dag=dag,
)

fact_clientwalletlog_script = PythonOperator(
    task_id='fact_clientwalletlog_expectations',
    python_callable=fact_clientwalletlog,
    dag=dag,
)

fact_transactions_script = PythonOperator(
    task_id='fact_transactions_expectations',
    python_callable=fact_transactions,
    dag=dag,
)

fact_clientwallet_script = PythonOperator(
    task_id='fact_clientwallet_expectations',
    python_callable=fact_clientwallet,
    dag=dag,
)
# Set up the task dependency, if necessary
#fact_matchedorder_script >> 
fact_matchedorder_script >> fact_closingprice_script >> fact_clientwalletlog_script  >> fact_clientwallet_script >> fact_transactions_script


