version: 2

models:
  - name: dim_clients_segments
    description: "This a dimension table that stores the segments of each clients and their trading attributes"
    columns:
      - name: cid
        data_type: text
        description: "Unique cid of client"
        tests:
          - not_null
          - unique
      - name: last_seen
        data_type: datetime
        description: "Datetime showing the last time a client was last active on the exchange"

      - name: time_since_last_seen
        data_type: interval
        description: "Time that has passed since the client was last seen online"

      - name: last_action_time
        data_type: datetime
        description: "Datetime showing the last time the client made an action e.g trade, fund wallet"

      - name: time_since_last_action
        data_type: interval
        description: "Time that passed since the client made an action on the exchange"

      - name: active_status
        data_type: text
        description: "The engagement level of client. This expected to be ('Active', 'Passive', 'Dormant')"

      - name: best_notification_hour
        data_type: integer
        description: "The hour of the day (24-hour) the client is most active. This taken as the best time to send the client notification"

      - name: activity_hour_frequency
        data_type: integer
        description: "The number of times the user has been seen online during their best_notification_hour"

      - name: avg_online_duration_within_hour
        data_type: float
        description: "The average duration seconds the client stayed online at their best_notification time"

      - name: boards
        data_type: json
        description: "This is a json aggregate of the boards the user have traded and the number of times they traded each board"
        
      - name: commodities
        data_type: json
        description:  This is a json aggregate of the commodities the user have ever traded and the number of times they traded each commodity"

  - name: dim_test_non_exist_model
    description: "This a dimension table that does not exists"
    columns:
      - name: id
        description: "This column does not exist"
        data_type: int
