{{ config (materialized = 'ephemeral') }}

with
pre_exchange_trans_vol as(
	-- Pulling in for only concerned commodities only
	
	select execution_date
			, case when commodity_name = 'Cleaned Sorghum' then 'Sorghum'
					when commodity_name = 'Sesame Seed Cleaned' then 'Sesame Seed'
				else commodity_name
				end commodity_name
			, case when commodity_code = 'C_SGM' then 'SGM'
					when commodity_code = 'SSC' then 'SSM'
				else commodity_code
				end commodity_code
			, executed_volume_kg

	from {{ ref ('fact_trade_individual_transactions') }}
	where commodity_name in ('Maize Feed Grade - White'
							, 'Paddy Rice Long Grain'
							, 'Soybean'
							, 'Sorghum', 'Cleaned Sorghum'
							, 'Cocoa'
							, 'Ginger Dried Split'
							, 'Sesame Seed', 'Sesame Seed Cleaned'
							, 'Raw Cashew Nuts')
			and execution_date is not null
			and order_type = 'Buy'
			and (oms_name = 'COMX' or oms_name is null)
			and security_type in ('Dawa', 'OTC')
			
),

exchange_trans_vol as(
	-- Goal for this intermediate model is to derive the volume per commodity in connection to a date_dim table,
    -- to ensure a value is got for everyday, whether there was a transaction or not

	select extract('year' from execution_date) execution_year
			, concat((extract('year' from execution_date)), '-12-31') :: date year_end_date
			, concat((extract('year' from execution_date)) + 2, '-10-01') :: date effective_date
			, commodity_name commodity
			, commodity_code
			, sum(executed_volume_kg/1000) total_volume_mt
	
	from pre_exchange_trans_vol
		
	group by extract('year' from execution_date), commodity, commodity_code
	order by commodity, extract('year' from execution_date)
	),

running_avg_exchange_vol as(
	select *
			, avg(total_volume_mt) over (partition by commodity order by effective_date rows 2 preceding) AS three_yr_moving_avg_ex_vol_mt
			, avg(total_volume_mt) over (partition by commodity order by effective_date rows between 4 preceding and current row) AS five_yr_moving_avg_ex_vol_mt
	
	from exchange_trans_vol
	),

date_and_com as(
	select dim_date.date_actual date_
			, comm.commodity commodity_
			, comm.commodity_code commodity_code_
	
	from {{ ref ('stg_date') }} dim_date
	cross join (select distinct commodity, commodity_code from running_avg_exchange_vol) comm
	
	where dim_date.date_actual between '2024-10-01' and current_date
	),

price_and_vol as (
	-- The goal here is to derive the weights that would be used in estimating the index values
	-- Join the volumes and prices and get proper commodity code labels.
	select date_and_com.date_
		, date_and_com.commodity_
		, date_and_com.commodity_code_
		, price.commodity_closing_price closing_price_commodity_kg
		, running_avg_exchange_vol.*
		, national_vol.volume national_vol_mt
		, national_vol.three_yr_moving_avg_nat_vol_mt

	from date_and_com
	left join {{ ref ('stg_commodities_closing_prices') }} price
		on date(date_and_com.date_) = date(price.date)
		and date_and_com.commodity_code_ = price.commodity_code
	left join {{ ref ('stg_national_vol') }} national_vol
		on date(date_and_com.date_) = date(national_vol.effective_date)
		and date_and_com.commodity_code_ = national_vol.commodity_code
	left join running_avg_exchange_vol
		on date(date_and_com.date_) = date(running_avg_exchange_vol.effective_date)
		and date_and_com.commodity_code_ = running_avg_exchange_vol.commodity_code
	),
	
adj_vol as (
	select case when extract(month from date_) >= 10 
					then concat(extract(year from date_)::text, '/', extract(year from date_ + INTERVAL '1 year')::text)
				else concat(extract(year from date_ - INTERVAL '1 year')::text, '/', extract(year from date_)::text)
				end season
			, date_ date
			, commodity_ commodity
			, commodity_code_ commodity_code
			, closing_price_commodity_kg
			, closing_price_commodity_kg * 1000 closing_price_commodity_mt
			, coalesce(total_volume_mt, 0) exchange_executed_vol_mt
			, coalesce(three_yr_moving_avg_ex_vol_mt, 0) three_yr_moving_avg_ex_vol_mt
			, coalesce(national_vol_mt, 0) national_vol_mt
			, coalesce(three_yr_moving_avg_nat_vol_mt, 0) three_yr_moving_avg_nat_vol_mt
	
	from price_and_vol
	)

select *
from adj_vol