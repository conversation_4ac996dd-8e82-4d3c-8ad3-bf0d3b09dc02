/*
-- WAREHOUSE DAILY INVENTORY BALANCE
-- *** NOTE This query is to recreate the historic table only and is not meant to update the table. There is another script to achieve this incremental updates***

-- -- -- Create fact_warehouse_dailyinventorybalance Table
DROP TABLE sherlock.wb_fact_warehouse_dailyinventorybalance;

CREATE TABLE sherlock.wb_fact_warehouse_dailyinventorybalance (
				inventory_date DATE, 
				warehouse_id VARCHAR(25),
				warehouse_name TEXT,
				item_id VARCHAR(25),
				item_name TEXT,
				item_code TEXT,
				item_type VARCHAR(25),
				grade VARCHAR(25),
				tenant_id VARCHAR(25), 
				latest_lien_bags INT,
				latest_available_bags INT,
				latest_total_bags INT,
				latest_lien_net_weight DOUBLE PRECISION,
				latest_available_net_weight DOUBLE PRECISION,
				latest_total_net_weight DOUBLE PRECISION,
				latest_lien_gross_weight DOUBLE PRECISION,
				latest_available_gross_weight DOUBLE PRECISION,
				latest_total_gross_weight DOUBLE PRECISION,
				warehouse_inventory_account_id VARCHAR(25),
				trans_created_at TIMESTAMP,
				trans_updated_at TIMESTAMP
	);


-- -- -- Insert Historic Data Into The Table
INSERT INTO sherlock.wb_fact_warehouse_dailyinventorybalance (inventory_date, warehouse_id, warehouse_name, item_id, item_name, item_code, item_type, grade, tenant_id, 
																	latest_lien_bags, latest_available_bags, latest_total_bags, 
																	latest_lien_net_weight, latest_available_net_weight, latest_total_net_weight, 
																	latest_lien_gross_weight, latest_available_gross_weight, latest_total_gross_weight, warehouse_inventory_account_id,
																	trans_created_at, trans_updated_at)

-- Create an index to identify each warehouse's commodity and commodity grade
with base_table as (

	select concat(acc.warehouse_id,acc.item_id,acc.grade) id_index,
				acc.warehouse_id,
				wh.name as warehouse_name,
				acc.item_id,
				prod_item.name as item_name,
				prod_item.code as item_code,
				prod_item.product_type as item_type,
				acc.grade,
				trans.created created_date,
				trans.*
	
	from workbench.inventory_warehouseinventorytransaction trans
	left join workbench.inventory_warehouseinventoryaccount acc
	on trans.warehouse_inventory_account_id = acc.id
	left join workbench.workbench_warehouse wh
	on acc.warehouse_id = wh.id
	left join workbench_mart.dim_item prod_item
	on acc.item_id = prod_item.id
	
	where trans.is_deleted is false
	
	),
	
-- Create another index to identify daily transactions for the indices in the prior cte
transaction_index as (
	select row_number() over (partition by created_date, id_index order by created desc) daily_index, *
	from base_table
	),

	
-- Because we are only concerned with the inventory balance per day,
	-- we need to retrieve only the last transaction of each warehouse per day per commodity and per commodity grade
indexed_table as (
	select *
	from transaction_index
	where daily_index = 1
	),

	
-- We need the balance per day, so we need a date field that caters to every single day, that would be the portfolio_date
all_dates_fields as ( 
	select date(date_dim.date_actual) as inventory_date,
			inventory_fields.warehouse_id, inventory_fields.warehouse_name, inventory_fields.warehouse_inventory_account_id, 
			inventory_fields.item_id, inventory_fields.item_name, inventory_fields.item_code, inventory_fields.item_type, inventory_fields.grade, inventory_fields.tenant_id

	from trade_mart.dim_date date_dim
	cross join (select distinct warehouse_id, warehouse_name, warehouse_inventory_account_id, item_id, item_name, item_code, item_type, grade, tenant_id from indexed_table) inventory_fields

	where date_dim.date_actual between '2013-01-01' and (current_date - 1)
	),

	
-- Join all dates cte with the indexed_table for each warehouse. This should result in days with some null values for that warehouse's item and item location
	-- Create a identifier in the form of running total, to identify the latest balance for each day including days without any transaction
everyday_and_transaction_day_only as (
	select adf.inventory_date, adf.warehouse_id, adf.warehouse_name, adf.warehouse_inventory_account_id, adf.item_id, adf.item_name, adf.item_code, adf.item_type, adf.grade,
			indexed_table.created_date, indexed_table.source, adf.tenant_id,   
			indexed_table.bags, indexed_table.lien_bags_before, indexed_table.lien_bags_after, indexed_table.bags_before, indexed_table.bags_after,
			indexed_table.total_bags_before, indexed_table.total_bags_after,
			indexed_table.net_weight, indexed_table.lien_net_weight_before, indexed_table.lien_net_weight_after, indexed_table.net_weight_before, indexed_table.net_weight_after,
			indexed_table.total_net_weight_before, indexed_table.total_net_weight_after,
			indexed_table.gross_weight, indexed_table.lien_gross_weight_before, indexed_table.lien_gross_weight_after, indexed_table.gross_weight_before, indexed_table.gross_weight_after,
			indexed_table.total_gross_weight_before, indexed_table.total_gross_weight_after, indexed_table.updated,

			case when net_weight is null then 0 else 1 end transaction_boolean_identifier,
			sum(case when net_weight is null then 0 else 1 end) 
				over (partition by adf.warehouse_id, adf.item_id, adf.grade order by adf.inventory_date) running_total_trans_day

	from all_dates_fields adf
	left join indexed_table 
	on adf.inventory_date = date(indexed_table.created_date)
		and adf.warehouse_id = indexed_table.warehouse_id
		and adf.item_id = indexed_table.item_id
		and adf.grade = indexed_table.grade
	),


-- Carry forward the last known balance
final_data as (
	select inventory_date, warehouse_id, warehouse_name, item_id, item_name, item_code, item_type, grade, tenant_id, 
			first_value(lien_bags_after) over (partition by warehouse_id, item_id, grade, running_total_trans_day order by created_date) latest_lien_bags,
			first_value(bags_after) over (partition by warehouse_id, item_id, grade, running_total_trans_day order by created_date) latest_available_bags,
			first_value(total_bags_after) over (partition by warehouse_id, item_id, grade, running_total_trans_day order by created_date) latest_total_bags,
			first_value(lien_net_weight_after) over (partition by warehouse_id, item_id, grade, running_total_trans_day order by created_date) latest_lien_net_weight,
			first_value(net_weight_after) over (partition by warehouse_id, item_id, grade, running_total_trans_day order by created_date) latest_available_net_weight,
			first_value(total_net_weight_after) over (partition by warehouse_id, item_id, grade, running_total_trans_day order by created_date) latest_total_net_weight,
			first_value(lien_gross_weight_after) over (partition by warehouse_id, item_id, grade, running_total_trans_day order by created_date) latest_lien_gross_weight,
			first_value(gross_weight_after) over (partition by warehouse_id, item_id, grade, running_total_trans_day order by created_date) latest_available_gross_weight,
			first_value(total_gross_weight_after) over (partition by warehouse_id, item_id, grade, running_total_trans_day order by created_date) latest_total_gross_weight,
			warehouse_inventory_account_id,
			coalesce(created_date, (first_value(created_date) over (partition by warehouse_id, item_id, grade, running_total_trans_day order by created_date))) trans_created_at,
			coalesce(updated, (first_value(updated) over (partition by warehouse_id, item_id, grade, running_total_trans_day order by created_date))) trans_updated_at


	from everyday_and_transaction_day_only
	
	)

select * 
from final_data
order by inventory_date desc, warehouse_name, item_code, grade
*/