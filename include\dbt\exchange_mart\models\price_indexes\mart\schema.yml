version: 

models:

  - name: fact_prices_aci
    description: "Aggregated Commodity Index (ACI) capturing commodity price movements on the Africa Exchange."
    columns:
      - name: date
        data_type: date
        description: "The date of the price index record."
        tests:
          - not_null
      - name: commodity_code
        data_type: text
        description: "Unique code representing the commodity that makes up the index"
      - name: commodity
        data_type: text
        description: "Name of the commodity that makes up the index"
      - name: commodity_weight
        data_type: double precision
        description: "Percentage contribution of the commodity to the index"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error

      - name: closing_price_index_mt
        data_type: double precision
        description: "Closing index price of the commodity"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: points
        data_type: double precision
        description: "The actual index value. For the commodity, index point of the commodity or sub_index value."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
          - custom_expect_column_difference_not_greater_than:
              # column_a: points
              column_b: prev_day_point
              threshold: 0.15 # 15 %

      - name: prev_day_point
        data_type: double precision
        description: "Index points value from the previous day."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: dod_change
        data_type: double precision
        description: "Day-over-day percentage change in the commodity index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: prev_week_point
        data_type: double precision
        description: "Index points value from the previous week."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: wow_change
        data_type: double precision
        description: "Week-over-week percentage change in the commodity index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: week_start
        data_type: double precision
        description: "Index value at the start of the current week."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: week_end
        data_type: double precision
        description: "Index value at the end of the current week."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: month_start
        data_type: double precision
        description: "Index value at the start of the current month."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: month_end
        data_type: double precision
        description: "Index value at the end of the current month."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: mtd_change
        data_type: double precision
        description: "Month-to-date percentage change in the commodity index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: previous_month_end
        data_type: double precision
        description: "Index value at the end of the previous month."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: mom_change
        data_type: double precision
        description: "Month-over-month percentage change in the commodity index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: quarter_start
        data_type: double precision
        description: "Index value at the start of the current quarter."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: quarter_end
        data_type: double precision
        description: "Index value at the end of the current quarter."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: qtd_change
        data_type: double precision
        description: "Quarter-to-date percentage change in the commodity index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: previous_quarter_end
        data_type: double precision
        description: "Index value at the end of the previous quarter."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: qoq_change
        data_type: double precision
        description: "Quarter-over-quarter percentage change in the commodity index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: season_start
        data_type: double precision
        description: "Index value at the start of the seasonal period."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: std_change
        data_type: double precision
        description: "Season-to-date percentage change in the commodity index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error

      - name: year_start
        data_type: double precision
        description: "Index value at the start of the current year."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: ytd_change
        data_type: double precision
        description: "Year-to-date percentage change in the commodity index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error

  - name: fact_prices_aei
    description: "Aggregated Exchange Index (AEI) capturing commodity price movements on the Africa Exchange."
    columns:
      - name: date
        data_type: date
        description: "The trading date."
        tests:
          - not_null
      - name: commodity_code
        data_type: text
        description: "Unique code representing the traded commodity that makes up the index"
      - name: commodity
        data_type: text
        description: "Name of the traded commodity that makes up the index"
      - name: commodity_weight
        data_type: double precision
        description: "Percentage contribution of the commodity to the index"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error

      - name: closing_price_index_mt
        data_type: double precision
        description: "Closing index price of the commodity"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error

      - name: points
        data_type: double precision
        description: "Index points for the commodity."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
          - custom_expect_column_difference_not_greater_than:
              # column_a: points
              column_b: prev_day_point
              threshold: 0.15 # 15 %

      - name: prev_day_point
        data_type: double precision
        description: "Previous day's index points."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error

      - name: dod_change
        data_type: double precision
        description: "Day-over-day percentage change in the index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: prev_week_point
        data_type: double precision
        description: "Previous week's index points."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: wow_change
        data_type: double precision
        description: "Week-over-week percentage change in the index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: week_start
        data_type: double precision
        description: "Index at the start of the current week."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: week_end
        data_type: double precision
        description: "Index at the end of the current week."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: month_start
        data_type: double precision
        description: "Index at the start of the current month."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: month_end
        data_type: double precision
        description: "Index at the end of the current month."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: mtd_change
        data_type: double precision
        description: "Month-to-date percentage change in the index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: previous_month_end
        data_type: double precision
        description: "Index at the end of the previous month."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: mom_change
        data_type: double precision
        description: "Month-over-month percentage change in the index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: quarter_start
        data_type: double precision
        description: "Index at the start of the current quarter."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: quarter_end
        data_type: double precision
        description: "Index at the end of the current quarter."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: qtd_change
        data_type: double precision
        description: "Quarter-to-date percentage change in the index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: previous_quarter_end
        data_type: double precision
        description: "Index at the end of the previous quarter."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: qoq_change
        data_type: double precision
        description: "Quarter-over-quarter percentage change in the index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: season_start
        data_type: double precision
        description: "Index at the start of the seasonal period."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: std_change
        data_type: double precision
        description: "Season-to-date percentage change in the index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: year_start
        data_type: double precision
        description: "Index at the start of the current year."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
      - name: ytd_change
        data_type: double precision
        description: "Year-to-date percentage change in the index."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: error
