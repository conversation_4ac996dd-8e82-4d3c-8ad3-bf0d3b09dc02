models:
  - name: dim_client
    description: 'contains records on clients personal details like registeration and contact details for the comx platform and not the workbench platform'
    columns:
      - name: id
        data_type: bigint
        description: ' The unique identifier of the client.'
        tests:
          - not_null
          - unique
      - name: cid
        data_type: text
        description: ' The unique identifier associated with the client that ref comx_mart.dim_client.cid'
        tests:
          - not_null
          - unique
      - name: rnb
        data_type: text
        description: ' The rnb unique identifier associated with the client.'
        tests:
          - not_null
      - name: email
        data_type: text
        description: ' The email address of the client.'
      - name: phone
        data_type: text
        description: ' The phone number of the client.'
      - name: address
        data_type: text
        description: ' The address of the client.'
      - name: created
        data_type: timestamp with time zone
        description: ' Timestamp indicating when the client record was created.'
        tests:
          - not_null
      - name: updated
        data_type: timestamp with time zone
        description: ' Timestamp indicating the last update time for this client record.'
        tests:
          - not_null
      - name: last_name
        data_type: text
        description: ' The last name of the client.'
        tests:
          - not_null
      - name: first_name
        data_type: text
        description: ' The first name of the client.'
        tests:
          - not_null
      - name: share_code
        data_type: text
        description: ' The share code associated with the client.'
      - name: is_approved
        data_type: boolean
        description: ' A flag indicating whether the client is approved.'
        tests:
          - not_null
      - name: account_type
        data_type: text
        description: ' The type of account associated with the client(Broker-Dealer, Financier, Promoter, Broker, Trader, Logistic Partner, Client, Investor).'
        tests:
          - not_null
      - name: is_certified
        data_type: boolean
        description: ' A flag indicating whether the client is certified.'
        tests:
          - not_null
      - name: is_synced_wb
        data_type: boolean
        description: ' A flag indicating whether the client is synced with WB (World Bank).'
        tests:
          - not_null
      - name: referral_code
        data_type: text
        description: ' The referral code associated with the client.'
      - name: verify_me_dob
        data_type: text
        description: ' The date of birth returned by VerifyMe during verification '
      - name: is_afex_broker
        data_type: boolean
        description: ' A flag indicating whether the client is an AFEX broker.'
        tests:
          - not_null'
      - name: is_id_verified
        data_type: boolean
        description: ' A flag indicating whether the client''s identity is verified.'
        tests:
          - not_null
      - name: is_bvn_verified
        data_type: boolean
        description: ' A flag indicating whether the client''s BVN (Bank Verification Number) is verified.'
        tests:
          - not_null
      - name: is_kyc_complete
        data_type: boolean
        description: ' A flag indicating whether the client''s KYC (Know Your Customer) process is complete.'
        tests:
          - not_null
      - name: is_kyc_rejected
        data_type: boolean
        description: ' A flag indicating whether the client''s KYC is rejected.'
        tests:
          - not_null
      - name: is_kyc_verified
        data_type: boolean
        description: ' A flag indicating whether the client''s KYC is verified.'
        tests:
          - not_null
      - name: client_broker_id
        data_type: bigint
        description: ' The unique identifier of the broker associated with the client.'
        tests:
          - not_null
      - name: is_kyc_submitted
        data_type: boolean
        description: ' A flag indicating whether the client''s KYC is submitted.'
        tests:
          - not_null
      - name: is_update_pending
        data_type: boolean
        description: ' A flag indicating whether there are pending updates for the client.'
        tests:
          - not_null
      - name: user_account_type
        data_type: text
        description: 'The type of user account associated with the client (Individual, Corporate)'
        tests:
          - not_null
      - name: bvn_error_response
        data_type: text
        description: ' The error response associated with BVN verification.'
      - name: used_referral_code
        data_type: text
        description: 'The referral code used by the client.'
      - name: verify_me_lastname
        data_type: text
        description: ' The last name  returned by VerifyMe during verification'
      - name: verify_me_firstname
        data_type: text
        description: ' First name returned by VerifyMe during verification'
      - name: verify_me_middlename
        data_type: text
        description: ' Middle name used for verification purposes.'
      - name: id_verification_message
        data_type: text
        description: ' Message related to the ID verification process.'
      - name: is_kyc_pending_approval
        data_type: boolean
        description: ' A flag indicating whether Know Your Customer (KYC) verification is pending approval.'
      - name: bvn_verification_message
        data_type: text
        description: ' Message related to Bank Verification Number (BVN) verification.'
      - name: bank_verification_message
        data_type: text
        description: ' Message related to bank account verification.'
      - name: is_brokerage_bank_account_verified
        data_type: boolean
        description: ' A flag indicating whether the brokerage bank account is verified.'
      - name: country
        data_type: text
        description: ' The name of the country where the client is located.'
      - name: region
        data_type: text
        description: ' The region or state where the client is located.'
      - name: subregion
        data_type: text
        description: ' The subregion or district where the client is located.'
        tests:
          - not_null

  - name: dim_security
    description: "Dimension table for all securities traded on the exchange"
    columns:
      - name: id
        data_type: integer
        description: "Table primary key"
        tests:
          - not_null
      - name: security_code
        data_type: text
        description: "Unique identifier of the security"
        tests:
          - not_null
          - unique
      - name: security_name
        data_type: text
        description: "Security name"
        tests:
          - not_null
          - unique
      - name: sec_security_type
        data_type: text
        description: "Security type based on SEC (Security Exchange Commission)"
      - name: security_type
        data_type: text
        description: "Security type"
        tests:
          - not_null
      - name: board_name
        data_type: text
        description: "Board name of the security"
      - name: volume_per_unit
        data_type: bigint
        description: "Amount of the security in one unit"
        tests:
        - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: bigint
      - name: commodity_code
        data_type: text
        description: "Unique identifier of the unsecuritized commodity"
        tests:
          - not_null
      - name: commodity_name
        data_type: text
        description: "Name of the commodity"
        tests:
          - not_null
      - name: currency_code
        data_type: text
        description: "Currency code"
        tests:
        - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['NGN', 'USD']
              row_condition: "id is not null"
      - name: currency_name
        data_type: text
        description: "Currency name"
      - name: is_virtual_security
        data_type: boolean
        description: "A flag to indicate if the security is virtual security on the exchange"