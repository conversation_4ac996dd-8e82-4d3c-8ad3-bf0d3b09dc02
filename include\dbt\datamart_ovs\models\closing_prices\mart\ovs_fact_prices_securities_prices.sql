{{ config (materialized = 'table') }}

with	
pre_changes_values as(
	select nnm.*
			, case when nnm.dow = 0 then 
						lead(nnm.woy, 1) over (partition by nnm.security_code order by nnm.date)
					when nnm.dow = 6 then 
						lead(nnm.woy, 2) over (partition by nnm.security_code order by nnm.date)
				else nnm.woy
				end adj_woy
			, case when extract('year' from date) < 2022 and extract('month' from date) >= 12 
					then concat(extract('year' from date)::text, '/', extract('year' from date + INTERVAL '1 year')::text)
				when extract('year' from date) >= 2022 and extract('month' from date) >= 10 
					then concat(extract('year' from date)::text, '/', extract('year' from date + INTERVAL '1 year')::text)
				else concat(extract('year' from date - INTERVAL '1 year')::text, '/', extract('year' from date)::text)
				end season
	
	from {{ ref ('ovs_int_prices_securities_prices') }} nnm
	),
	
changes_values as(
	select *
			, (lag(closing_price, 7) over (partition by security_code, location order by date)) previous_week_price
			, first_value(closing_price) over (partition by security_code, location, adj_woy order by date) week_start_price
			, first_value(closing_price) over (partition by security_code, location, adj_woy order by date desc) week_end_price
	
			, (first_value(closing_price) over (partition by season, security_code, location order by date)) season_start_price
			, (first_value(closing_price) over (partition by extract('year' from date), security_code, location order by date)) year_start_price
	
	from pre_changes_values
	),
	
changes as(
	select *
			, (closing_price - opening_price) dod_price_diff
			, ((closing_price / nullif(opening_price, 0)) - 1) dod_price_change
			, ((closing_price / nullif(previous_week_price, 0)) - 1 ) wow_price_change
			, case when extract('year' from date) < 2022 and extract('month' from date) = 12 and extract('day' from date) = 1 -- December 1 (Pre Oct 2022)
					then ((closing_price / (nullif((lag(season_start_price, 1) over (partition by security_code, location order by date)), 0))) - 1 )
					when extract('year' from date) >= 2022 and extract('month' from date) = 10 and extract('day' from date) = 1 -- October 1
					then ((closing_price / (nullif((lag(season_start_price, 1) over (partition by security_code, location order by date)), 0))) - 1 )
				else ((closing_price / (nullif(season_start_price, 0))) - 1 )
				end std_price_change
			, case when extract('month' from date) = 1 and extract('day' from date) = 1 -- January 1
					then ((closing_price / (nullif((lag(year_start_price, 1) over (partition by security_code, location order by date)), 0))) - 1 )
				else ((closing_price / (nullif(year_start_price, 0))) - 1 )
				end ytd_price_change
	
	from changes_values
	),
	
final as(
	select date, dow, adj_woy woy, season
			, security_code, security_name, security_type
			, "location"
			, closing_price closing_price_kg, closing_price_per_unit
			, opening_price opening_price_kg, opening_price_per_unit
			, max_price_kg, max_price_per_unit
			, min_price_kg, min_price_per_unit
			, dod_price_diff, dod_price_change
			, week_start_price, week_end_price, previous_week_price, wow_price_change
			, season_start_price, std_price_change
			, year_start_price, ytd_price_change
	
	from changes
	)
	
select *
from final
order by date desc, security_code desc, "location"