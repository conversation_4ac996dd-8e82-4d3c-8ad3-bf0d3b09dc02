{{ config (materialized = 'ephemeral') }}

with 
board_summary_ex_matched_wb_trans as ( 
	select *, sum(landed_price_x_volume) over (partition by board_code, date_ order by date_) board_value
			, sum(volume_kg) over (partition by board_code, date_ order by date_) board_volume
		
	from {{ ref ('int_normalized_executed_prices')}}
	),

summarized_exchange_matched_wb_trans as (
	select distinct date_, consolidated_commodity_name, item_name, item_code, board_code, board_value, board_volume, (board_value / board_volume) board_price
	
	from board_summary_ex_matched_wb_trans
	),

for_crossjoining as (
	select distinct consolidated_commodity_name, item_name commodity, item_code commodity_code, board_code
	
    from summarized_exchange_matched_wb_trans
    ),

distinct_day_executed_order as (
	select dim_date.date_actual, board_code.*, trans.board_value, trans.board_volume, trans.board_price

	from {{ ref ('stg_date') }} dim_date
	cross join (select distinct consolidated_commodity_name, commodity, commodity_code, board_code from for_crossjoining) board_code
	left join summarized_exchange_matched_wb_trans trans
	on dim_date.date_actual = trans.date_
		and board_code.board_code = trans.board_code

	where (dim_date.date_actual between '2024-10-01' and current_date) ----*****************-----
	
	),


open_sell as (
	select adjusted_trade_created_at, order_type, commodity, commodity_code, security_code, order_landed_price, order_volume_kg,
			min(order_landed_price) over (partition by adjusted_trade_created_at, order_type, security_code) best_sell,
			sum(order_volume_kg) over (partition by adjusted_trade_created_at, order_type, security_code) board_volume
	
	from {{ ref ('int_normalized_open_order_prices')}}

	where order_type = 'Sell'
	order by adjusted_trade_created_at, security_code, order_type
),

best_sell as (
	select distinct adjusted_trade_created_at, order_type, commodity, commodity_code, security_code, best_sell, board_volume
	from open_sell
),

open_buy as (
	select adjusted_trade_created_at, order_type, commodity, commodity_code, security_code, order_landed_price, order_volume_kg,
			max(order_landed_price) over (partition by adjusted_trade_created_at, order_type, security_code) best_buy,
			sum(order_volume_kg) over (partition by adjusted_trade_created_at, order_type, security_code) board_volume
	
	from {{ ref ('int_normalized_open_order_prices')}}

	where order_type = 'Buy'
	order by adjusted_trade_created_at, security_code, order_type
),

best_buy as (
	select distinct adjusted_trade_created_at, order_type, commodity, commodity_code, security_code, best_buy, board_volume
	from open_buy	
),



open_order_prices_midpoint as ( -- aDJUST THIS, SUCH THAT WHERE EITHER BEST BUY OR BEST SELL IS MISSING I DON'T PULL IT IN
	select dim_date.date_actual, all_open.board_code, all_open.commodity_code, all_open.commodity, best_buy.best_buy, best_sell.best_sell,
			case when best_buy.best_buy is null or best_sell.best_sell is null then (coalesce(best_buy.best_buy,0) + coalesce(best_sell.best_sell,0)) / 1
					else (best_buy.best_buy + best_sell.best_sell) / 2
					end avg_open_price,
			case when best_buy.board_volume is null or best_sell.board_volume is null then (coalesce(best_buy.board_volume,0) + coalesce(best_sell.board_volume,0))
					else (best_buy.board_volume + best_sell.board_volume) / 2
					end total_board_order_volume
	
	from {{ ref ('stg_date') }} dim_date
	cross join (select distinct consolidated_commodity_name, commodity, commodity_code, board_code from for_crossjoining) all_open
	left join best_buy
	on dim_date.date_actual = best_buy.adjusted_trade_created_at
		and all_open.board_code = best_buy.security_code
	left join best_sell
	on dim_date.date_actual = best_sell.adjusted_trade_created_at
		and all_open.board_code = best_sell.security_code

	where (dim_date.date_actual between '2024-10-01' and current_date) ----*****************-----
			and best_buy.best_buy is not null
			and best_sell.best_sell is not null
	
	order by date_actual
	),


adj_open_order_prices_midpoint as (
	select date_actual, board_code, commodity_code, commodity, best_buy, best_sell,
			case when avg_open_price = 0 then null
					else avg_open_price
					end avg_open_price,
			case when total_board_order_volume = 0 then null
					else total_board_order_volume
					end total_board_order_volume
	
	from open_order_prices_midpoint	
	),


all_prices as (
	select ddeo.*, adj_average_open_order_prices.best_buy, adj_average_open_order_prices.best_sell, 
			adj_average_open_order_prices.avg_open_price, adj_average_open_order_prices.total_board_order_volume,
			case when ddeo.board_price is null then adj_average_open_order_prices.avg_open_price else ddeo.board_price end final_board_price,
			case when ddeo.board_volume is null then adj_average_open_order_prices.total_board_order_volume else ddeo.board_volume end final_board_volume,
			case when ddeo.board_code like 'S%' then 0.05
				when ddeo.board_code like 'D%' then 0.10
				when ddeo.board_code like 'WH%' then 0.10
				when ddeo.board_code like 'O%' then 0.75
			end as fixed_board_weight
	
	from distinct_day_executed_order ddeo
	left join adj_open_order_prices_midpoint
	on ddeo.date_actual = adj_open_order_prices_midpoint.date_actual
		and ddeo.board_code = adj_open_order_prices_midpoint.board_code
	)

select *

from all_prices