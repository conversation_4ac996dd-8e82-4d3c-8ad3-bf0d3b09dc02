{% docs cx_dim_security_id %}
The unique identifier of the security.
{% enddocs %}

{% docs cx_dim_security_created %}
Timestamp indicating when the security record was created.
{% enddocs %}

{% docs cx_dim_security_updated %}
Timestamp indicating the last update time for this security record.
{% enddocs %}

{% docs cx_dim_security_security_code %}
The code representing the security.
{% enddocs %}

{% docs cx_dim_security_security_name %}
The name of the security.
{% enddocs %}

{% docs cx_dim_security_board_code %}
The code representing the board associated with the security.
{% enddocs %}

{% docs cx_dim_security_board_name %}
The name of the board associated with the security.
{% enddocs %}

{% docs cx_dim_security_can_be_sold %}
A flag indicating whether the security can be sold.
{% enddocs %}

{% docs cx_dim_security_commodity_code %}
The code representing the commodity associated with the security.
{% enddocs %}

{% docs cx_dim_security_commodity_name %}
The name of the commodity associated with the security.
{% enddocs %}

{% docs cx_dim_security_security_type %}
The type of the security.
{% enddocs %}

{% docs cx_dim_security_volume_per_unit %}
The volume per unit of the security.
{% enddocs %}

{% docs cx_dim_security_currency_code %}
The code representing the currency used for the security.
{% enddocs %}

{% docs cx_dim_security_currency_name %}
The name of the currency used for the security.
{% enddocs %}

{% docs cx_dim_security_lower_price_band %}
The lower price band associated with the security.
{% enddocs %}

{% docs cx_dim_security_upper_price_band %}
The upper price band associated with the security.
{% enddocs %}

{% docs cx_dim_security_is_virtual_security %}
A flag indicating whether the security is virtual.
{% enddocs %}

{% docs cx_dim_security_show_on_live_market %}
A flag indicating whether the security should be shown on the live market.
{% enddocs %}

{% docs cx_dim_security_show_on_ticker_tape %}
A flag indicating whether the security should be shown on the ticker tape.
{% enddocs %}

{% docs cx_dim_security_price_bound_percentage %}
The price bound percentage associated with the security.
{% enddocs %}
