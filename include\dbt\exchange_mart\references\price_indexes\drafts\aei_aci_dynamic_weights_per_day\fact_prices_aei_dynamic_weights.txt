/*
{{ config (materialized = 'table') }}

with
index as(	
	
	select *
		, case when dow = 0 then -- Saturday
					lead(woy, 1) over (partition by commodity order by date)
				when dow = 6 then -- Sunday
					lead(woy, 2) over (partition by commodity order by date)
			else woy
			end adj_woy -- To ensure that the saturday and sunday belong to the new week
		, case when extract(dow from date::timestamp) = 6 then 0 
				when extract(dow from date::timestamp) = 0 then 1
				when extract(dow from date::timestamp) = 1 then 2
				when extract(dow from date::timestamp) = 2 then 3
				when extract(dow from date::timestamp) = 3 then 4
				when extract(dow from date::timestamp) = 4 then 5
				when extract(dow from date::timestamp) = 5 then 6 
				end adj_dow -- Week starts on Sat & ends on Fri and by default dow is labeled from Mon - Sun (1 - 0)
		, sum(closing_price_commodity_mt * commodity_weight) over (partition by date order by date) index
	
	from {{ ref ('int_prices_aei_commodity_weights') }}
),

new_aei as (
	select *
			, ((index / 686810.4) * 100) aei --Base period = Dec 1, 2019 (68681.04 per bag) for AEI
			, case when commodity = 'Cocoa' then ((closing_price_commodity_mt / 836085.0) * 100) 
					when commodity = 'Ginger Dried Split' then ((closing_price_commodity_mt / 441283.0) * 100)
					end sub_index
			, concat(adj_woy, '_', adj_dow) wow_identifier
	
	from index
		),

all_aei as (
    -- Chose to use old_aei up till the beginning of 2023/2024 season which started on 01/10/2023

	select season, date, commodity, commodity_code, price_kg, price_mt, sub_index, aei_index, aei, adj_woy
	
	from {{ ref ('int_prices_old_aei_indices') }}
	where date < '2023-10-01'
	
	UNION
	
	select season, date, commodity, commodity_code, closing_price_commodity_kg price_kg, closing_price_commodity_mt price_mt
			, sub_index, index aei_index, aei, adj_woy
	from new_aei 
	where date >= '2023-10-01'
	),

change_values as (
	select *
			, (lag(aei, 1) over (partition by commodity order by date)) previous_day_aei
			, (lag(sub_index, 1) over (partition by commodity order by date)) previous_day_sub_index
			
			, (lag(aei, 7) over (partition by commodity order by date)) previous_week_aei
			, (lag(sub_index, 7) over (partition by commodity order by date)) previous_week_sub_index

			, first_value(aei) over (partition by commodity, adj_woy order by date) week_start_aei
			, first_value(aei) over (partition by commodity, adj_woy order by date desc) week_end_aei
			, first_value(sub_index) over (partition by commodity, adj_woy order by date) week_start_sub_index
			, first_value(sub_index) over (partition by commodity, adj_woy order by date desc) week_end_sub_index

			, (first_value(aei) over (partition by season, commodity order by date)) season_start_aei
			, (first_value(sub_index) over (partition by season, commodity order by date)) season_start_sub_index
			, (first_value(aei) over (partition by extract('year' from date), commodity order by date)) year_start_aei
			, (first_value(sub_index) over (partition by extract('year' from date), commodity order by date)) year_start_sub_index
	
	from all_aei
	),
	
changes as (
	select *
			, ((aei / previous_day_aei) - 1 ) dod_aei_change
			, ((sub_index / previous_day_sub_index) - 1 ) dod_sub_index_change
			, ((aei / previous_week_aei) - 1 ) wow_aei_change
			, ((sub_index / previous_week_sub_index) - 1 ) wow_sub_index_change
	
			, case when extract('month' from date) = 10 and extract('day' from date) = 1 -- October 1
					then ((aei / (lag(season_start_aei, 1) over (partition by commodity order by date))) - 1 )
				else ((aei / season_start_aei) - 1 )
				end std_aei_change
			, case when extract('month' from date) = 10 and extract('day' from date) = 1 -- October 1
					then ((sub_index / (lag(season_start_sub_index, 1) over (partition by commodity order by date))) - 1 )
				else ((sub_index / season_start_sub_index) - 1 )
				end std_sub_index_change
	
			, case when extract('month' from date) = 1 and extract('day' from date) = 1 -- January 1
					then ((aei / (lag(year_start_aei, 1) over (partition by commodity order by date))) - 1 )
				else ((aei / year_start_aei) - 1 )
				end ytd_aei_change
			, case when extract('month' from date) = 1 and extract('day' from date) = 1 -- January 1
					then ((sub_index / (lag(year_start_sub_index, 1) over (partition by commodity order by date))) - 1 )
				else ((sub_index / year_start_sub_index) - 1 )
				end ytd_sub_index_change
	
	from change_values
	),
	
final_aei as (

	select
		distinct date
			, 'AEI' commodity_code
			, 'AFEX Export Index' commodity
			, aei_index closing_price_index_mt
			, aei points
			, previous_day_aei prev_day_point
			, dod_aei_change dod_change
			, previous_week_aei prev_week_point
			, wow_aei_change wow_change
			, week_start_aei week_start
			, week_end_aei week_end
			, season_start_aei season_start
			, std_aei_change std_change
			, year_start_aei year_start
			, ytd_aei_change ytd_change
	
	from changes
	
	UNION
	
	select date
			, commodity_code
			, commodity
			, price_mt closing_price_index_mt
			, sub_index points
			, previous_day_sub_index prev_day_point
			, dod_sub_index_change dod_change
			, previous_week_sub_index prev_week_point
			, wow_sub_index_change wow_change
			, week_start_sub_index week_start
			, week_end_sub_index week_end
			, season_start_sub_index season_start
			, std_sub_index_change std_change
			, year_start_sub_index year_start
			, ytd_sub_index_change ytd_change


	from changes
	)

select *

from final_aei
*/