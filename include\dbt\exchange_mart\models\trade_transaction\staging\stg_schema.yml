version: 2

models:
  - name: stg_matchedorder
    description: "One row per matched buy and sell client order"
    columns:
      - name: id
        data_type: bigint
        description: "This is the table's unique identifier and primary key"
        tests:
          - not_null
          - unique

      - name: created
        data_type: timestamp with time zone
        description: ""
        tests:
          - not_null

      - name: updated
        data_type: timestamp with time zone
        description: ""
        tests:
          - not_null

      - name: processed_on
        data_type: timestamp with time zone
        description: ""
        tests:
          - not_null

      - name: tid
        data_type: text
        description: ""
        tests:
          - not_null

      - name: matched_id
        data_type: text
        description: ""
        tests:
          - not_null
          - unique

      - name: buy_tid
        data_type: text
        description: ""
        tests:
          - not_null

      - name: sell_tid
        data_type: text
        description: ""
        tests:
          - not_null

      - name: buyer_cid
        data_type: text
        description: ""
        tests:
          - not_null

      - name: seller_cid
        data_type: text
        description: ""
        tests:
          - not_null

      - name: order_units
        data_type: bigint
        description: ""
        tests:
          - not_null

      - name: order_price
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: matched_units
        data_type: bigint
        description: ""
        tests:
          - not_null

      - name: matched_price
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: invoiced_units
        data_type: bigint
        description: ""
        tests:
          - not_null

      - name: volume_per_unit
        data_type: bigint
        description: ""
        tests:
          - not_null

      - name: calc_volume_per_unit
        data_type: bigint
        description: ""
        tests:
          - not_null

      - name: security_location_code
        data_type: text
        description: ""

      - name: order_type
        data_type: text
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['Buy', 'Sell']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)

      - name: board_type
        data_type: text
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['OTC', 'Spot']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)

      - name: security_name
        data_type: text
        description: ""
        tests:
          - not_null

      - name: security_code
        data_type: text
        description: ""
        tests:
          - not_null

      - name: security_type
        data_type: text
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['OTC', 'Spot', 'Dawa', 'FI']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)

      - name: community
        data_type: text
        description: ""

      - name: brokerage_community_name
        data_type: text
        description: ""

      - name: brokerage_community_code
        data_type: text
        description: ""

      - name: promoter_community_name
        data_type: text
        description: ""

      - name: promoter_community_code
        data_type: text
        description: ""

      - name: oms_name
        data_type: text
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['ComX']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)

      - name: oms_code
        data_type: text
        description: ""
        tests:
          - not_null

      - name: is_on_behalf
        data_type: boolean
        description: ""
        tests:
          - not_null

      - name: comx_created_by_id
        data_type: bigint
        description: ""
        tests:
          - not_null

      - name: total_order_price
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: total_order_price_with_fees
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: total_afex_fees
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: total_oms_fees
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: sec_fee
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: exchange_fee
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: cm_fee
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: brokerage_fee
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: vat_value
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: location_breakdown
        data_type: text
        description: ""
        tests:
          - not_null

      - name: is_contract_note_sent
        data_type: boolean
        description: ""
        tests:
          - not_null

      - name: is_audit_cancelled
        data_type: boolean
        description: ""
        tests:
          - not_null

      - name: processed_for_inventory
        data_type: boolean
        description: ""
        tests:
          - not_null

      - name: is_deleted
        data_type: boolean
        description: ""
        tests:
          - not_null

      - name: discount
        data_type: double precision
        description: ""
        tests:
          - not_null

