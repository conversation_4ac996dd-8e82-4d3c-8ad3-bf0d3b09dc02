version: 3.9

models:
  - name: int_client_bnt
    description: "Intermediate model that determines client best notification hour on the exchange"
    columns:
      - name: cid
        description: "Client cid"
        tests:
          - unique
          - not_null
      - name: best_notification_hour
        description: "Best notification hour of the user"
        tests:
          - not_null
      - name: activity_hour_frequency
        description: "Activity frequency of the user at that hour. This is the overall count of the number of times the user have been active at that hour"
      - name: avg_online_duration_within_hour
        description: "The overall average time duration the client have spent being online at that hour"

  - name: int_client_last_action
    description: "Intermediate table that pulls each client most recent action on the exchange"
    columns:
      - name: cid
        description: "Client's cid"
        tests:
          - unique
          - not_null
      - name: last_action_time
        description: "Timestamp for the last time the client had any activity on the exchange"
      - name: last_action_done
        description: "The last action done by the client on the exchange as of date"
      - name: time_since_last_acion
        description: "Interval that shows the time that has passed since the client made any action on the exhcange"

  - name: int_client_last_login
    description: "Intermediate model that pulls the last time the client logs in to the system. The last login is based on the activity stream table."
    columns:
      - name: cid
        description: "<PERSON><PERSON>'s cid"
      - name: last_seen
        description: "Timestamp for the last time the client was seen online"
      - name: time_since_last_seen
        description: "Interval that shows the time that has passed since the client was seen online"

  - name: int_client_traded_commodities
    description: "Intermediate model for the aggragated summary of the commodities and boards that a client has ever traded"
    columns:
      - name: trans_cid
        description: "Client cid"
        tests:
          - unique
          - not_null
      - name: securities
        description: "ARRAY[JSON] holding the sell, buy count and volume of securities (boards) the client has transacted. Each json in the array is for a single board e.g [{'board':'OTC', 'sell_count':45, 'buy_count':32, 'sell vol':345, 'buy vol':123}]"
      - name: commodities
        description: "ARRAY[JSON] holding the sell, buy count and volume of commodities the client has transacted. Each json in the array is for a single board e.g [{'board':'OTC', 'sell_count':35, 'buy_count':22, 'sell vol':235, 'buy vol':43}]"

  - name: int_engagement_level
    description: "Intermediate model for getting client engagement level as Active, Passive, Dormant"
    columns:
      - name: cid
        description: "Client's cid"
        tests:
          - unique
          - not_null
      - name: last_seen
        description: "Timestamp for the last time the client was seen online"
      - name: time_since_last_seen
        description: "Interval that shows the time that has passed since the client was seen online"
      - name: last_action_time
        description: "Timestamp for the last time the client had any activity on the exchange"
      - name: last_action_done
        description: "The last action done by the client on the exchange as of date"
      - name: time_since_last_acion
        description: "Interval that shows the time that has passed since the client made any action on the exhcange"
      - name: active_status
        description: "Flag that shows the client activenes level as ('Active', 'Passive', 'Dormant')"



