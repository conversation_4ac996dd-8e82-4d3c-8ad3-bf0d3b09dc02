/*
{{ config (materialized = 'table') }}

with
index as(	
	
	select *
		, case when dow = 0 then -- Saturday
					lead(woy, 1) over (partition by commodity order by date)
				when dow = 6 then -- Sunday
					lead(woy, 2) over (partition by commodity order by date)
			else woy
			end adj_woy -- To ensure that the saturday and sunday belong to the new week
		, case when extract(dow from date::timestamp) = 6 then 0 
				when extract(dow from date::timestamp) = 0 then 1
				when extract(dow from date::timestamp) = 1 then 2
				when extract(dow from date::timestamp) = 2 then 3
				when extract(dow from date::timestamp) = 3 then 4
				when extract(dow from date::timestamp) = 4 then 5
				when extract(dow from date::timestamp) = 5 then 6 
				end adj_dow -- Week starts on Sat & ends on Fri and by default dow is labeled from Mon - Sun (1 - 0)
		, sum(closing_price_commodity_mt * commodity_weight) over (partition by date order by date) index
	
	from {{ ref ('int_prices_aci_commodity_weights') }}
),

new_aci as (
	select *
			, ((index / 144357.1) * 100) aci --Base period = Dec 1, 2016 (14435.71 per bag) for ACI
			, case when commodity = 'Maize Feed Grade - White' then ((closing_price_commodity_mt / 123803.5) * 100) 
					when commodity = 'Paddy Rice Long Grain' then ((closing_price_commodity_mt / 130000.0) * 100)
					when commodity = 'Soybean' then ((closing_price_commodity_mt / 151249.3) * 100)
					when commodity = 'Sorghum' then ((closing_price_commodity_mt / 80150.0) * 100) 
					end sub_index
			, concat(adj_woy, '_', adj_dow) wow_identifier
	
	from index
		),

all_aci as (
    -- Chose to use old_aci up till the beginning of 2023/2024 season which started on 01/10/2023

	select season, date, commodity, commodity_code, price_kg, price_mt, sub_index, aci_index, aci, adj_woy
	
	from {{ ref ('int_prices_old_aci_indices') }}
	where date < '2023-10-01'
	
	UNION
	
	select season, date, commodity, commodity_code, closing_price_commodity_kg price_kg, closing_price_commodity_mt price_mt
			, sub_index, index aci_index, aci, adj_woy
	from new_aci 
	where date >= '2023-10-01'
	),

change_values as (
	select *
			, (lag(aci, 1) over (partition by commodity order by date)) previous_day_aci
			, (lag(sub_index, 1) over (partition by commodity order by date)) previous_day_sub_index
			
			, (lag(aci, 7) over (partition by commodity order by date)) previous_week_aci
			, (lag(sub_index, 7) over (partition by commodity order by date)) previous_week_sub_index

			, first_value(aci) over (partition by commodity, adj_woy order by date) week_start_aci
			, first_value(aci) over (partition by commodity, adj_woy order by date desc) week_end_aci
			, first_value(sub_index) over (partition by commodity, adj_woy order by date) week_start_sub_index
			, first_value(sub_index) over (partition by commodity, adj_woy order by date desc) week_end_sub_index
	
			, (first_value(aci) over (partition by season, commodity order by date)) season_start_aci
			, (first_value(sub_index) over (partition by season, commodity order by date)) season_start_sub_index
			, (first_value(aci) over (partition by extract('year' from date), commodity order by date)) year_start_aci
			, (first_value(sub_index) over (partition by extract('year' from date), commodity order by date)) year_start_sub_index
	
	from all_aci
	),
	
changes as (
	select *
			, ((aci / previous_day_aci) - 1 ) dod_aci_change
			, ((sub_index / previous_day_sub_index) - 1 ) dod_sub_index_change
			, ((aci / previous_week_aci) - 1 ) wow_aci_change
			, ((sub_index / previous_week_sub_index) - 1 ) wow_sub_index_change
	
			, case when extract('month' from date) = 10 and extract('day' from date) = 1 -- October 1
					then ((aci / (lag(season_start_aci, 1) over (partition by commodity order by date))) - 1 )
				else ((aci / season_start_aci) - 1 )
				end std_aci_change
			, case when extract('month' from date) = 10 and extract('day' from date) = 1 -- October 1
					then ((sub_index / (lag(season_start_sub_index, 1) over (partition by commodity order by date))) - 1 )
				else ((sub_index / season_start_sub_index) - 1 )
				end std_sub_index_change
	
			, case when extract('month' from date) = 1 and extract('day' from date) = 1 -- January 1
					then ((aci / (lag(year_start_aci, 1) over (partition by commodity order by date))) - 1 )
				else ((aci / year_start_aci) - 1 )
				end ytd_aci_change
			, case when extract('month' from date) = 1 and extract('day' from date) = 1 -- January 1
					then ((sub_index / (lag(year_start_sub_index, 1) over (partition by commodity order by date))) - 1 )
				else ((sub_index / year_start_sub_index) - 1 )
				end ytd_sub_index_change
	
	from change_values
	),
	
final_aci as (

	select
		distinct date
			, 'ACI' commodity_code
			, 'AFEX Commodities Index' commodity
			, aci_index closing_price_index_mt
			, aci points
			, previous_day_aci prev_day_point
			, dod_aci_change dod_change
			, previous_week_aci prev_week_point
			, wow_aci_change wow_change
			, week_start_aci week_start
			, week_end_aci week_end
			, season_start_aci season_start
			, std_aci_change std_change
			, year_start_aci year_start
			, ytd_aci_change ytd_change
	
	from changes
	
	UNION
	
	select date
			, commodity_code
			, commodity
			, price_mt closing_price_index_mt
			, sub_index points
			, previous_day_sub_index prev_day_point
			, dod_sub_index_change dod_change
			, previous_week_sub_index prev_week_point
			, wow_sub_index_change wow_change
			, week_start_sub_index week_start
			, week_end_sub_index week_end
			, season_start_sub_index season_start
			, std_sub_index_change std_change
			, year_start_sub_index year_start
			, ytd_sub_index_change ytd_change


	from changes
	)

select *

from final_aci
*/