/*
-- CLIENT DAILY DEPOSITORY BALANCE
-- *** NOTE This query is to recreate the historic table only and is not meant to update the table. There is another script to achieve this incremental updates***

-- -- Create fact_client_dailydepositorybalance Table
DROP TABLE workbench_mart.fact_client_dailydepositorybalance;

CREATE TABLE workbench_mart.fact_client_dailydepositorybalance (
				depository_date DATE, 
				cid VARCHAR(25), 
				product_id VARCHAR(25), 
				product TEXT, 
				product_code VARCHAR(25), 
				product_type TEXT, 
				location_id VARCHAR(25), 
				location_name TEXT, 
				location_state TEXT,
				latest_lien_volume DOUBLE PRECISION,
				latest_total_volume DOUBLE PRECISION,
				latest_available_volume DOUBLE PRECISION,
				trans_created_at TIMESTAMP,
				trans_updated_at TIMESTAMP
	);


-- -- Insert Historic Data Into The Table
INSERT INTO workbench_mart.fact_client_dailydepositorybalance (depository_date, cid, product_id, product, product_code, 
															product_type, location_id, location_name, location_state,
														 	latest_lien_volume, latest_total_volume, latest_available_volume,
															trans_created_at, trans_updated_at)

-- Adjust the location_id, so that we have id = 0 where the id is null, to enable us join properly as joins will not work on null
with base_table as (

	select DATE(logg.created) transaction_date, logg.client_id,
			logg.product_id, coalesce(logg.location_id, 0) as adj_location_id,
			logg.volume, logg.lien_volume_before, logg.lien_volume_after, logg.available_volume_before, logg.available_volume_after,
			logg.total_volume_before, logg.total_volume_after, logg.created, logg.updated
	from csd.depository_csdoperationlogging logg
	
	where logg.is_deleted is false
	
	),

	
-- Create a index to identify each transaction of a client per day and per security and per security location
transaction_index as (
	select row_number() over (partition by transaction_date, client_id, product_id, adj_location_id order by created desc) daily_index, *
	from base_table
	),

	
-- Because we are only concerned with the portfolio balance per day,
	-- we need to retrieve only the last transaction of a client per day and per security and per security location
indexed_table as (
	select *
	from transaction_index
	where daily_index = 1
	),

	
-- We need the balance per day, so we need a date field that caters to every single day, that would be the portfolio_date
	-- Generate a table with all dates for each client and joined to all the fields that differentiate each portfolio value (dlient_id, security, location)
all_dates_fields as ( 
	select date(date_dim.date_actual) as depository_date,
			depository_fields.client_id, depository_fields.product_id, depository_fields.adj_location_id

	from trade_mart.dim_date date_dim
	cross join (select distinct client_id, product_id, adj_location_id from indexed_table) depository_fields

	where date_dim.date_actual between '2013-01-01' and (current_date - 1)
	),

	
-- Join all dates cte with the indexed_table for each client. This should result in days with some null values for that client's security and security location
	-- Create a identifier in the form of running total, to identify the latest balance for each day including days without any transaction
everyday_and_transaction_day_only as (
	select adf.depository_date, adf.client_id, adf.product_id, pdt.name product, pdt.code product_code, pdt.product_type, adf.adj_location_id,
			client.cid, loc.code location_code, loc.name location_name, loc.state location_state, indexed_table.transaction_date,  
			indexed_table.volume, indexed_table.lien_volume_before, indexed_table.lien_volume_after,
			indexed_table.available_volume_before, indexed_table.available_volume_after,
			indexed_table.total_volume_before, indexed_table.total_volume_after, indexed_table.created, indexed_table.updated,

			case when volume is null then 0 else 1 end transaction_boolean_identifier,
			sum(case when volume is null then 0 else 1 end) 
				over (partition by adf.client_id, adf.product_id, adf.adj_location_id order by adf.depository_date) running_total_trans_day

	from all_dates_fields adf
	left join indexed_table 
	on adf.depository_date = indexed_table.transaction_date
		and adf.client_id = indexed_table.client_id
		and adf.product_id = indexed_table.product_id
		and adf.adj_location_id = indexed_table.adj_location_id
	left join csd.crm_client client 
		on adf.client_id = client.id
	left join csd.crm_product pdt
		on adf.product_id = pdt.id
	left join csd.depository_location loc
		on adf.adj_location_id = loc.id
	),


-- Carry forward the last known balance
final_data as (
	select *,
			first_value(lien_volume_after) over (partition by cid, product_id, adj_location_id, running_total_trans_day order by cid, depository_date) latest_lien_volume,
			first_value(available_volume_after) over (partition by cid, product_id, adj_location_id, running_total_trans_day order by cid, depository_date) latest_available_volume,
			first_value(total_volume_after) over (partition by cid, product_id, adj_location_id, running_total_trans_day order by cid, depository_date) latest_total_volume,
			coalesce(created, (first_value(created) over (partition by cid, product_id, adj_location_id, running_total_trans_day order by cid, depository_date))) latest_trans_at

	from everyday_and_transaction_day_only
	
	)

select depository_date, cid, product_id, product, product_code, product_type, adj_location_id location_id, location_name, location_state,
	 	latest_lien_volume, latest_total_volume, latest_available_volume, latest_trans_at
	
from final_data
	order by depository_date desc, client_id, product_id, adj_location_id

*/