{% docs cx_dim_client_id %}
The unique identifier of the client.
{% enddocs %}

{% docs cx_dim_client_cid %}
The unique identifier associated with the client that references comx_mart.cx_dim_client.cid.
{% enddocs %}

{% docs cx_dim_client_rnb %}
The RNB (Retailer Name Brand) associated with the client.
{% enddocs %}

{% docs cx_dim_client_email %}
The email address of the client.
{% enddocs %}

{% docs cx_dim_client_phone %}
The phone number of the client.
{% enddocs %}

{% docs cx_dim_client_address %}
The address of the client.
{% enddocs %}

{% docs cx_dim_client_created %}
Timestamp indicating when the client record was created.
{% enddocs %}

{% docs cx_dim_client_updated %}
Timestamp indicating the last update time for this client record.
{% enddocs %}

{% docs cx_dim_client_last_name %}
The last name of the client.
{% enddocs %}

{% docs cx_dim_client_first_name %}
The first name of the client.
{% enddocs %}

{% docs cx_dim_client_share_code %}
The share code associated with the client.
{% enddocs %}

{% docs cx_dim_client_is_approved %}
A flag indicating whether the client is approved.
{% enddocs %}

{% docs cx_dim_client_account_type %}
The type of account associated with the client ('Broker-Dealer', 'Financier', 'Promoter', 'Broker', 'Trader', 'Logistic Partner', 'Client', 'Investor').
{% enddocs %}

{% docs cx_dim_client_country %}
The name of the country where the client is located.
{% enddocs %}

{% docs cx_dim_client_is_certified %}
A flag indicating whether the client is certified.
{% enddocs %}

{% docs cx_dim_client_is_synced_wb %}
A flag indicating whether the client is synced with WB (World Bank).
{% enddocs %}

{% docs cx_dim_client_referral_code %}
The referral code associated with the client.
{% enddocs %}

{% docs cx_dim_client_verify_me_dob %}
The date of birth returned by VerifyMe during verification.
{% enddocs %}

{% docs cx_dim_client_is_afex_broker %}
A flag indicating whether the client is an AFEX broker.
{% enddocs %}

{% docs cx_dim_client_is_id_verified %}
A flag indicating whether the client's identity is verified.
{% enddocs %}

{% docs cx_dim_client_is_bvn_verified %}
A flag indicating whether the client's BVN (Bank Verification Number) is verified.
{% enddocs %}

{% docs cx_dim_client_is_kyc_complete %}
A flag indicating whether the client's KYC (Know Your Customer) process is complete.
{% enddocs %}

{% docs cx_dim_client_is_kyc_rejected %}
A flag indicating whether the client's KYC is rejected.
{% enddocs %}

{% docs cx_dim_client_is_kyc_verified %}
A flag indicating whether the client's KYC is verified.
{% enddocs %}

{% docs cx_dim_client_client_broker_id %}
The unique identifier of the broker associated with the client.
{% enddocs %}

{% docs cx_dim_client_is_kyc_submitted %}
A flag indicating whether the client's KYC is submitted.
{% enddocs %}

{% docs cx_dim_client_is_update_pending %}
A flag indicating whether there are pending updates for the client.
{% enddocs %}

{% docs cx_dim_client_user_account_type %}
The type of user account associated with the client ('Individual', 'Corporate').
{% enddocs %}

{% docs cx_dim_client_bvn_error_response %}
The error response associated with BVN verification.
{% enddocs %}

{% docs cx_dim_client_used_referral_code %}
The referral code used by the client.
{% enddocs %}

{% docs cx_dim_client_verify_me_lastname %}
The last name returned by VerifyMe during verification.
{% enddocs %}

{% docs cx_dim_client_verify_me_firstname %}
First name returned by VerifyMe during verification.
{% enddocs %}

{% docs cx_dim_client_verify_me_middlename %}
Middle name used for verification purposes.
{% enddocs %}

{% docs cx_dim_client_id_verification_message %}
Message related to the ID verification process.
{% enddocs %}

{% docs cx_dim_client_is_kyc_pending_approval %}
A flag indicating whether Know Your Customer (KYC) verification is pending approval.
{% enddocs %}

{% docs cx_dim_client_bvn_verification_message %}
Message related to Bank Verification Number (BVN) verification.
{% enddocs %}

{% docs cx_dim_client_bank_verification_message %}
Message related to bank account verification.
{% enddocs %}

{% docs cx_dim_client_is_brokerage_bank_account_verified %}
A flag indicating whether the brokerage bank account is verified.
{% enddocs %}

{% docs cx_dim_client_region %}
The region or state where the client is located.
{% enddocs %}

{% docs cx_dim_client_subregion %}
The subregion or district where the client is located.
{% enddocs %}
