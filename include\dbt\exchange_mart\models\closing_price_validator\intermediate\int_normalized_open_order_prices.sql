{{ config (materialized = 'ephemeral') }}

with
exchange_open_order as (
	select distinct tid, trade_created_at,
			CASE WHEN ((trade_created_at at time zone 'WAT') :: time) >= '15:00:00'
				THEN DATE((trade_created_at at time zone 'WAT') + INTERVAL '1 day')
			ELSE DATE((trade_created_at at time zone 'WAT'))
			END AS adjusted_trade_created_at,
			order_type,
			order_price order_price_per_unit, order_units,
			order_price/volume_per_unit order_price_per_kg, order_units*volume_per_unit order_volume_kg, security_type, security_code, 
			commodity_code, commodity, consolidated_commodity_name,
			security_location, contract_seller_location_code, contract_buyer_location_code, 
			case when contract_buyer_location_code is null then security_location else contract_buyer_location_code end destination_location
	
	from {{ ref ('fact_trade_individual_transactions') }}
	where matched_id is null
	),

normalized_exchange_open_order_price as (
	select price.*, log_."Logistics Differential per kg" log_diff_per_kg, (price.order_price_per_kg + coalesce(log_."Logistics Differential per kg",0)) order_landed_price, 
			price.volume_kg * (price.order_price_per_kg + coalesce(log_."Logistics Differential per kg",0)) order_landed_price_x_volume
			
	from exchange_open_order price
	left join {{ ref ('stg_logistics_grid') }} log_
	on price.consolidated_commodity_name = log_."Commodity"
		and price.destination_location = log_."Destination Location"
	),	
	
avg_std_exchange_open_order as (
	select *, 
			avg(order_landed_price) over (partition by order_type, security_code, adjusted_trade_created_at order by adjusted_trade_created_at) average_order_price_per_kg,
			coalesce(stddev(order_landed_price) over (partition by order_type, security_code, adjusted_trade_created_at order by adjusted_trade_created_at) , 0) stddev_order_price_per_kg,
			(avg(order_landed_price) over (partition by order_type, security_code, adjusted_trade_created_at order by adjusted_trade_created_at)  - coalesce(stddev(order_landed_price) over (partition by order_type, security_code, adjusted_trade_created_at order by adjusted_trade_created_at) , 0)) lower_band,
			(avg(order_landed_price) over (partition by order_type, security_code, adjusted_trade_created_at order by adjusted_trade_created_at)  + coalesce(stddev(order_landed_price) over (partition by order_type, security_code, adjusted_trade_created_at order by adjusted_trade_created_at) , 0)) upper_band
	
	from normalized_exchange_open_order_price
	),

without_outlier_unionized_open_trans as (
	select *
	from avg_std_exchange_open_order
	where order_landed_price between lower_band and upper_band
	)

select *

from without_outlier_unionized_open_trans