{{ config(materialized='ephemeral') }}

WITH commodity_transactions AS (
    SELECT
        trans_cid,
        trade_created_at,
        consolidated_commodity_name,
        security_type,
        security_code,
        order_type,
        executed_volume_kg

    FROM
        {{ ref ('fact_trade_individual_transactions') }}

        WHERE (security_type IN ('Dawa','OTC') 
            OR (security_type ='Spot' AND security_code ILIKE '%S'))
            AND trans_cid IS NOT NULL
)

SELECT * FROM commodity_transactions