from sqlalchemy import MetaData, Table
from datetime import datetime, timedelta

class Cleanup:
    """
    A class to cleanup unused space in the database as a result of airbyte's  json load process and historical loads"""

    def __init__(self,engine):
        self.engine = engine
        pass
    

    def vacuum_table(self, schema_name: str, table_name: str):
        """Performs a VACUUM operation on a specific table, to reclaim database space after trucation or deletion"""
        with self.engine.begin() as conn:
            conn.execute(f"VACUUM {schema_name}.{table_name};")


    def fetch_schema(self):
        """Fetches all schemas except system schemas."""
        with self.engine.connect() as conn:
            schema_names = conn.execute(f"""
                SELECT schema_name
                FROM information_schema.schemata
                WHERE  schema_name != 'information_schema';
            """).fetchall()

            return [schema[0] for schema in schema_names if not schema[0].startswith('pg_')]


    def fetch_table(self,schema_name:str):
        """Fetches all tables in a given schema."""
        with self.engine.connect() as conn:
            table_names = conn.execute(f"""
                SELECT tablename
                FROM pg_tables
                WHERE schemaname = '{schema_name}';
            """).fetchall()

            return [table[0] for table in table_names ]


    def truncate(self,schema_name:str,tables:list):
        """Truncates tables and vacuums them afterward."""
        with self.engine.begin() as conn:
            for table_name in tables:
                conn.execute(f"TRUNCATE TABLE {schema_name}.{table_name} CASCADE;")
                self.vacuum_table(schema_name, table_name)


    # def truncate_with_date(self,schema_name:str,tables:list,date_filter:str):
    #     """Deletes older records in raw tables but keeps recent ones, then vacuums."""
    #     with self.engine.begin() as conn:
    #         for table_name in tables:
    #             conn.execute(f"""
    #                         DELETE FROM {schema_name}.{table_name} 
    #                         WHERE _airbyte_ab_id NOT IN (
    #                                                      SELECT _airbyte_ab_id FROM {schema_name}.{table_name}
    #                                                      ORDER BY _airbyte_emitted_at DESC
    #                                                      LIMIT 100
    #                                                      )""")
    #             self.vacuum_table(schema_name, table_name)


    def truncate_with_date(self, schema_name:str, tables:list, days_to_keep:int):
        """Deletes records older than 'days_to_keep' days, then vacuums the table."""
        # Calculate the cutoff date (today minus days_to_keep)
        cutoff_date = (datetime.now().date() - timedelta(days=days_to_keep)).isoformat()

        with self.engine.begin() as conn:
            for table_name in tables:
                conn.execute(f"DELETE FROM {schema_name}.{table_name} WHERE _airbyte_emitted_at < '{cutoff_date}'")
                self.vacuum_table(schema_name, table_name)


    def delete(self,schema_name:str,tables:list):
        with self.engine.begin() as conn:
            for table_name in tables:
                conn.execute(f"DROP TABLE IF EXISTS {schema_name}.{table_name} ;")


    def close_engine(self):
        # Close the SQLAlchemy engine
        self.engine.dispose()

