# %%
import configparser
import ast
from datetime import datetime, date

import pandas as pd
import os
from credentials import  db_conn
dir_path = os.path.dirname(os.path.realpath('__file__'))


# %%
# Connect to DataSource
config = configparser.ConfigParser()
config.read(f"{dir_path}/config.ini")

config_source = 'SOURCE_DB'

# connection
source_engine, conn_source =  db_conn(conn_param=config_source)


# %%
# Load the Excel workbook
excel_file = pd.ExcelFile(r"C:\Users\<USER>\OneDrive - AFEX Commodities Exchange Limited\Documents\Dashboards\Management\Management Dashboard - Manual Entry.xlsx")

# %%
# Loop through each sheet in the workbook
for sheet_name in excel_file.sheet_names:
    # Load the sheet into a DataFrame
    table = pd.read_excel(excel_file, sheet_name=sheet_name)
    
    # Save the DataFrame in the PostgreSQL database
    table.to_sql(sheet_name, source_engine, schema='afex_gen_mart', if_exists='replace', index=False)

# %%
