
{{ config(materialized='table') }}

WITH farmer_crop_data AS (
    SELECT
        far.folio_id,
        CASE
			WHEN position(',' IN far.crop_name) > 0 THEN string_to_array(far.crop_name, ', ') 
			ELSE ARRAY[far.crop_name]
		END	AS crop_names,
		CASE
			WHEN position(',' IN far.crop_code) > 0 THEN string_to_array(far.crop_code, ', ')
			ELSE ARRAY[far.crop_code]
		END AS crop_codes
    FROM
        {{ ref( 'dim_farmer' ) }} far
		
)

SELECT
    farmer_crop_data.folio_id,
    unnest(farmer_crop_data.crop_names) AS crop_name,
    unnest(farmer_crop_data.crop_codes) AS crop_code
FROM
    farmer_crop_data