version: 2

sources:
  - name: workbench2
    description : workbench2 data
    tables:
      - name: inventory_goodsreceiptline
      - name: inventory_goodsreceiptlineclient
      - name: inventory_item
      - name: crm_client
      - name: workbench_farmer
  - name: workbench
    description : workbench data
    tables:
      - name: crm_farmer
        description: contains farmer records
      - name: crm_client
        description: contains client records
      - name: workbench_cooperative
        description: contains tweet records
      - name: workbench_warehouse
      - name: workbench_cell
        description: contrains user records
      - name: crm_farmer_crop_type
        description: contains tweet records
      - name: inventory_item
        description: contrains user records
      - name: workbench_product
        description: contains tweet records
      - name: tenant_tenant
        description: contrains user records
      - name: location_location
        description: contains tweet records
      - name: location_baselocation
        description: contrains user records
      - name: location_state
        description: contains tweet records
      - name: location_region
        description: contrains user records
      - name: location_lga
      - name: location_country
      - name: loan_loanline
      - name: loan_loan
      - name: loan_loanbundle
      - name: project_project
      - name: goods_receipt_goodsreceiptline
      - name: crm_clientbankinformation
      - name: bank_bank
      - name: account_user
      - name: crm_farmlocation
      - name: crm_client_client_type
      - name: crm_clienttype
      - name: location_ward

      

models:
  - name: wb_dim_warehouse
    description: "a table containing all information about a warehouse"
    columns:
      - name: id
        description: "{{doc('dim_warehouse_id')}}"
      - name: location_id
        description: "location_id ||FK|| from workbench.location_location"
      - name: tenant_id
        description: "{{doc('dim_warehouse_tenant_id')}}"
      - name: tenant_name
        description: "{{doc('dim_warehouse_tenant_name')}}"
      - name: warehouse_name
        description: "{{doc('dim_warehouse_warehouse_name')}}"
      - name: warehouse_code
        description: "{{doc('dim_warehouse_warehouse_code')}}"
      - name: address
        description: "{{doc('dim_warehouse_address')}}"
      - name: longitude
        description: "{{doc('dim_warehouse_longitude')}}"
      - name: latitude
        description: "{{doc('dim_warehouse_latitude')}}"
      - name: warehouse_email
        description: "{{doc('dim_warehouse_warehouse_email')}}"
      - name: location
        description: "{{doc('dim_warehouse_location')}}"
      - name: state
        description: "{{doc('dim_warehouse_state')}}"
      - name: capital
        description: "{{doc('dim_warehouse_capital')}}"
      - name: region_code
        description: "{{doc('dim_warehouse_region_code')}}"
      - name: region_name
        description: "{{doc('dim_warehouse_region_name')}}"
      - name: country
        description: "{{doc('dim_warehouse_country')}}"
      - name: country_capital
        description: "{{doc('dim_warehouse_country_capital')}}"
      - name: continent
        description: "{{doc('dim_warehouse_continent')}}"
      - name: continent_subregion
        description: "{{doc('dim_warehouse_continent_subregion')}}"

  - name: wb_fact_loan
    description: "a table containing all information about a loan"
    columns:
      - name: id
        description: "{{doc('wb_fact_loan_id')}}"
      - name: created
        description: "{{doc('wb_fact_loan_created')}}"
      - name: updated
        description: "{{doc('wb_fact_loan_updated')}}"
      - name: loan_bundlename
        description: "{{doc('wb_fact_loan_loan_bundlename')}}"
      - name: created_offline
        description: "{{doc('wb_fact_loan_created_offline')}}"
      - name: approval_date
        description: "{{doc('wb_fact_loan_approval_date')}}"
      - name: farmer_id
        description: "{{doc('wb_fact_loan_farmer_id')}}"
      - name: project_id
        description: "{{doc('wb_fact_loan_project_id')}}"
      - name: project_start_date
        description: "{{doc('wb_fact_loan_project_start_date')}}"
      - name: project_name
        description: "{{doc('wb_fact_loan_project_name')}}"
      - name: project_code
        description: "{{doc('wb_fact_loan_project_code')}}"
      - name: maturity_date
        description: "{{doc('wb_fact_loan_maturity_date')}}"
      - name: warehouse_id
        description: "{{doc('wb_fact_loan_warehouse_id')}}"
      - name: warehouse_name
        description: "{{doc('wb_fact_loan_warehouse_name')}}"
      - name: tenant_id
        description: "{{doc('wb_fact_loan_tenant_id')}}"
      - name: ln_id
        description: "{{doc('wb_fact_loan_ln_id')}}"
      - name: hectare
        description: "{{doc('wb_fact_loan_hectare')}}"
      - name: total_loan_value
        description: "{{doc('wb_fact_loan_total_loan_value')}}"
      - name: repayment_value
        description: "{{doc('wb_fact_loan_repayment_value')}}"
      - name: amount_repaid
        description: "{{doc('wb_fact_loan_amount_repaid')}}"
      - name: insurance
        description: "{{doc('wb_fact_loan_insurance')}}"
      - name: crg
        description: "{{doc('wb_fact_loan_crg')}}"
      - name: interest
        description: "{{doc('wb_fact_loan_interest')}}"
      - name: admin_fee
        description: "{{doc('wb_fact_loan_admin_fee')}}"
      - name: equity
        description: "{{doc('wb_fact_loan_equity')}}"
      - name: to_balance
        description: "{{doc('wb_fact_loan_to_balance')}}"
      - name: is_repaid
        description: "{{doc('wb_fact_loan_is_repaid')}}"
      - name: is_approved
        description: "{{doc('wb_fact_loan_is_approved')}}"
      - name: is_approval_completed
        description: "{{doc('wb_fact_loan_is_approval_completed')}}"
      - name: is_rejected
        description: "{{doc('wb_fact_loan_is_rejected')}}"
      - name: is_reverted
        description: "{{doc('wb_fact_loan_is_reverted')}}"


  - name: wb_fact_loan_breakdown
    description: "This table contains a breakdown of loan components that are part of the fact_loan table"
    columns:
      - name: id
        description: "{{doc('wb_fact_loan_breakdown_id')}}"
      - name: created
        description: "{{doc('wb_fact_loan_breakdown_created')}}"
      - name: updated
        description: "{{doc('wb_fact_loan_breakdown_updated')}}"
      - name: maturity_date
        description: "{{doc('wb_fact_loan_breakdown_maturity_date')}}"
      - name: farmer_id
        description: "{{doc('wb_fact_loan_breakdown_farmer_id')}}"
      - name: project_id
        description: "{{doc('wb_fact_loan_breakdown_project_id')}}"
      - name: warehouse_id
        description: "{{doc('wb_fact_loan_breakdown_warehouse_id')}}"
      - name: item_id
        description: "{{doc('wb_fact_loan_breakdown_item_id')}}"
      - name: tenant_id
        description: "{{doc('wb_fact_loan_breakdown_tenant_id')}}"
      - name: ln_id
        description: "{{doc('wb_fact_loan_breakdown_ln_id')}}"
      - name: line_id
        description: "{{doc('wb_fact_loan_breakdown_line_id')}}"
      - name: hectare
        description: "{{doc('wb_fact_loan_breakdown_hectare')}}"
      - name: units
        description: "{{doc('wb_fact_loan_breakdown_units')}}"
      - name: unit_price
        description: "{{doc('wb_fact_loan_breakdown_unit_price')}}"
      - name: total_price
        description: "{{doc('wb_fact_loan_breakdown_total_price')}}"
      - name: total_loan_value
        description: "{{doc('wb_fact_loan_breakdown_total_loan_value')}}"
      - name: repayment_value
        description: "{{doc('wb_fact_loan_breakdown_repayment_value')}}"
      - name: amount_repaid
        description: "{{doc('wb_fact_loan_breakdown_amount_repaid')}}"
      - name: insurance
        description: "{{doc('wb_fact_loan_breakdown_insurance')}}"
      - name: crg
        description: "{{doc('wb_fact_loan_breakdown_crg')}}"
      - name: interest
        description: "{{doc('wb_fact_loan_breakdown_interest')}}"
      - name: admin_fee
        description: "{{doc('wb_fact_loan_breakdown_admin_fee')}}"
      - name: equity
        description: "{{doc('wb_fact_loan_breakdown_equity')}}"
      - name: to_balance
        description: "{{doc('wb_fact_loan_breakdown_to_balance')}}"
      - name: loan_status
        description: "{{doc('wb_fact_loan_breakdown_loan_status')}}"
      - name: data_identification_verification
        description: "{{doc('wb_fact_loan_breakdown_data_identification_verification')}}"
      - name: value_chain_management
        description: "{{doc('wb_fact_loan_breakdown_value_chain_management')}}"
      - name: is_repaid
        description: "{{doc('wb_fact_loan_breakdown_is_repaid')}}"
      - name: is_approved
        description: "{{doc('wb_fact_loan_breakdown_is_approved')}}"
      - name: is_approval_completed
        description: "{{doc('wb_fact_loan_breakdown_is_approval_completed')}}"
      - name: is_rejected
        description: "{{doc('wb_fact_loan_breakdown_is_rejected')}}"
      - name: is_reverted
        description: "{{doc('wb_fact_loan_breakdown_is_reverted')}}"

  - name: wb_fact_grn
    description: "This table contains records of Goods Receipt Note (GRN) transactions"
    columns:
      - name: cid
        description: "{{doc('wb_fact_grn_cid')}}"
      - name: bags
        description: "{{doc('wb_fact_grn_bags')}}"
      - name: grade
        description: "{{doc('wb_fact_grn_grade')}}"
      - name: grn_id
        description: "{{doc('wb_fact_grn_grn_id')}}"
      - name: receipt_id
        description: "{{doc('wb_fact_grn_receipt_id')}}"
      - name: created
        description: "{{doc('wb_fact_grn_created')}}"
      - name: updated
        description: "{{doc('wb_fact_grn_updated')}}"
      - name: gross_weight
        description: "{{doc('wb_fact_grn_gross_weight')}}"
      - name: net_weight
        description: "{{doc('wb_fact_grn_net_weight')}}"
      - name: deduction
        description: "{{doc('wb_fact_grn_deduction')}}"
      - name: total_deduction
        description: "{{doc('wb_fact_grn_total_deduction')}}"
      - name: moisture
        description: "{{doc('wb_fact_grn_moisture')}}"
      - name: total_commodity_price
        description: "{{doc('wb_fact_grn_total_commodity_price')}}"
      - name: price_per_tonne
        description: "{{doc('wb_fact_grn_price_per_tonne')}}"
      - name: transaction_type
        description: "{{doc('wb_fact_grn_transaction_type')}}"
      - name: approval_permissions
        description: "{{doc('wb_fact_grn_approval_permissions')}}"
      - name: approval_done
        description: "{{doc('wb_fact_grn_approval_done')}}"
      - name: is_approved
        description: "{{doc('wb_fact_grn_is_approved')}}"
      - name: is_approval_completed
        description: "{{doc('wb_fact_grn_is_approval_completed')}}"
      - name: approval_date
        description: "{{doc('wb_fact_grn_approval_date')}}"
      - name: is_received_at_warehouse
        description: "{{doc('wb_fact_grn_is_received_at_warehouse')}}"
      - name: is_reverted
        description: "{{doc('wb_fact_grn_is_reverted')}}"
      - name: rejection_reason
        description: "{{doc('wb_fact_grn_rejection_reason')}}"
      - name: total_payable_price
        description: "{{doc('wb_fact_grn_total_payable_price')}}"
      - name: transaction_fees
        description: "{{doc('wb_fact_grn_transaction_fees')}}"
      - name: is_processed
        description: "{{doc('wb_fact_grn_is_processed')}}"
      - name: is_disabled_for_listing
        description: "{{doc('wb_fact_grn_is_disabled_for_listing')}}"
      - name: spot_payment
        description: "{{doc('wb_fact_grn_spot_payment')}}"
      - name: employee_id
        description: "{{doc('wb_fact_grn_employee_id')}}"
      - name: cash_advance_account_pk
        description: "{{doc('wb_fact_grn_cash_advance_account_pk')}}"
      - name: item_name
        description: "{{doc('wb_fact_grn_item_name')}}"
      - name: item_code
        description: "{{doc('wb_fact_grn_item_code')}}"
      - name: item_type
        description: "{{doc('wb_fact_grn_item_type')}}"
      - name: warehouse_code
        description: "{{doc('wb_fact_grn_warehouse_code')}}"
      - name: is_deleted
        description: "{{doc('wb_fact_grn_is_deleted')}}"
      - name: next_approval
        description: "{{doc('wb_fact_grn_next_approval')}}"
      - name: is_rejected
        description: "{{doc('wb_fact_grn_is_rejected')}}"
      - name: rejected_date
        description: "{{doc('wb_fact_grn_rejected_date')}}"
      - name: created_offline
        description: "{{doc('wb_fact_grn_created_offline')}}"
      - name: is_traded
        description: "{{doc('wb_fact_grn_is_traded')}}"
      - name: raised_for_farmer
        description: "{{doc('wb_fact_grn_raised_for_farmer')}}"
      - name: cash_advance_account_paid
        description: "{{doc('wb_fact_grn_cash_advance_account_paid')}}"
      - name: truck_no
        description: "{{doc('wb_fact_grn_truck_no')}}"
      - name: created_by_id
        description: "{{doc('wb_fact_grn_created_by_id')}}"
      - name: goods_receipt_id
        description: "{{doc('wb_fact_grn_goods_receipt_id')}}"
      - name: rejected_by_id
        description: "{{doc('wb_fact_grn_rejected_by_id')}}"
      - name: cash_advance_account_id
        description: "{{doc('wb_fact_grn_cash_advance_account_id')}}"
      - name: additional_fees
        description: "{{doc('wb_fact_grn_additional_fees')}}"
      - name: wm_updated
        description: "{{doc('wb_fact_grn_wm_updated')}}"
      - name: is_clean
        description: "{{doc('wb_fact_grn_is_clean')}}"
      - name: revert_reason
        description: "{{doc('wb_fact_grn_revert_reason')}}"
      - name: is_accounting_posted
        description: "{{doc('wb_fact_grn_is_accounting_posted')}}"
      - name: tenant_id
        description: "{{doc('wb_fact_grn_tenant_id')}}"
      - name: is_uploaded
        description: "{{doc('wb_fact_grn_is_uploaded')}}"
      - name: certified
        description: "{{doc('wb_fact_grn_certified')}}"
      - name: is_edited
        description: "{{doc('wb_fact_grn_is_edited')}}"
      - name: payment_option
        description: "{{doc('wb_fact_grn_payment_option')}}"
      - name: preferred_bank_account_id
        description: "{{doc('wb_fact_grn_preferred_bank_account_id')}}"
      - name: discount
        description: "{{doc('wb_fact_grn_discount')}}"
      - name: source
        description: "{{doc('wb_fact_grn_source')}}"

  - name: wb_dim_item
    description: "This table provides information about items for which Goods Receipt Notes (GRN) and loans are raised"
    columns:
      - name: id
        description: "{{doc('wb_dim_item_id')}}"
      - name: tenant_id
        description: "{{doc('wb_dim_item_tenant_id')}}"
      - name: created
        description: "{{doc('wb_dim_item_created')}}"
      - name: updated
        description: "{{doc('wb_dim_item_updated')}}"
      - name: grade_one_deduction
        description: "{{doc('wb_dim_item_grade_one_deduction')}}"
      - name: grade_two_deduction
        description: "{{doc('wb_dim_item_grade_two_deduction')}}"
      - name: grade_three_deduction
        description: "{{doc('wb_dim_item_grade_three_deduction')}}"
      - name: name
        description: "{{doc('wb_dim_item_name')}}"
      - name: code
        description: "{{doc('wb_dim_item_code')}}"
      - name: product_type
        description: "{{doc('wb_dim_item_product_type')}}"

  - name: wb_dim_farmer
    description: "This table contains records of farmer details, including personal information, farm-related details, and associations with other entities."
    columns:
      - name: id
        description: "{{doc('wb_dim_farmer_id')}}"
      - name: created
        description: "{{doc('wb_dim_farmer_created')}}"
      - name: updated
        description: "{{doc('wb_dim_farmer_updated')}}"
      - name: is_deleted
        description: "{{doc('wb_dim_farmer_is_deleted')}}"
      - name: folio_id
        description: "{{doc('wb_dim_farmer_folio_id')}}"
      - name: title
        description: "{{doc('wb_dim_farmer_title')}}"
      - name: first_name
        description: "{{doc('wb_dim_farmer_first_name')}}"
      - name: last_name
        description: "{{doc('wb_dim_farmer_last_name')}}"
      - name: middle_name
        description: "{{doc('wb_dim_farmer_middle_name')}}"
      - name: address
        description: "{{doc('wb_dim_farmer_address')}}"
      - name: gender
        description: "{{doc('wb_dim_farmer_gender')}}"
      - name: marital_status
        description: "{{doc('wb_dim_farmer_marital_status')}}"
      - name: dob
        description: "{{doc('wb_dim_farmer_dob')}}"
      - name: phone
        description: "{{doc('wb_dim_farmer_phone')}}"
      - name: village
        description: "{{doc('wb_dim_farmer_village')}}"
      - name: farm_size
        description: "{{doc('wb_dim_farmer_farm_size')}}"
      - name: passport_type
        description: "{{doc('wb_dim_farmer_passport_type')}}"
      - name: passport_number
        description: "{{doc('wb_dim_farmer_passport_number')}}"
      - name: nok_phone
        description: "{{doc('wb_dim_farmer_nok_phone')}}"
      - name: nok_name
        description: "{{doc('wb_dim_farmer_nok_name')}}"
      - name: nok_relationship
        description: "{{doc('wb_dim_farmer_nok_relationship')}}"
      - name: bvn
        description: "{{doc('wb_dim_farmer_bvn')}}"
      - name: farm_coordinates
        description: "{{doc('wb_dim_farmer_farm_coordinates')}}"
      - name: farm_coordinates_polygon
        description: "{{doc('wb_dim_farmer_farm_coordinates_polygon')}}"
      - name: is_blacklist
        description: "{{doc('wb_dim_farmer_is_blacklist')}}"
      - name: languages
        description: "{{doc('wb_dim_farmer_languages')}}"
      - name: client_id
        description: "{{doc('wb_dim_farmer_client_id')}}"
      - name: warehouse_id
        description: "{{doc('wb_dim_farmer_warehouse_id')}}"
      - name: crop_name
        description: "{{doc('wb_dim_farmer_crop_name')}}"
      - name: crop_code
        description: "{{doc('wb_dim_farmer_crop_code')}}"
      - name: feo_name
        description: "{{doc('wb_dim_farmer_feo_name')}}"
      - name: feo_code
        description: "{{doc('wb_dim_farmer_feo_code')}}"
      - name: feo_phone_number
        description: "{{doc('wb_dim_farmer_feo_phone_number')}}"
      - name: cooperative_id
        description: "{{doc('wb_dim_farmer_cooperative_id')}}"
      - name: cooperative_name
        description: "{{doc('wb_dim_farmer_cooperative_name')}}"
      - name: cooperative_code
        description: "{{doc('wb_dim_farmer_cooperative_code')}}"
      - name: account_numbers
        description: "{{doc('wb_dim_farmer_account_numbers')}}"
      - name: tenant_id
        description: "{{doc('wb_dim_farmer_tenant_id')}}"
      - name: phone_invalid
        description: "{{doc('wb_dim_farmer_phone_invalid')}}"
      - name: phone_number_status
        description: "{{doc('wb_dim_farmer_phone_number_status')}}"
      - name: coordinate_status
        description: "{{doc('wb_dim_farmer_coordinate_status')}}"
      - name: id_status
        description: "{{doc('wb_dim_farmer_id_status')}}"

  - name: wb_dim_crop
    description: "This table provides information about crops planted by farmers"
    columns:
      - name: warehouse_id
        description: "{{doc('wb_dim_crop_warehouse_id')}}"
      - name: farmer_created
        description: "{{doc('wb_dim_crop_farmer_created')}}"
      - name: farmer_id
        description: "{{doc('wb_dim_crop_farmer_id')}}"
      - name: code
        description: "{{doc('wb_dim_crop_code')}}"
      - name: name
        description: "{{doc('wb_dim_crop_name')}}"


  - name: wb_dim_clientbank
    description: "This table stores bank details of clients on the workbench platform"
    columns:
      - name: id
        description: "{{doc('wb_dim_clientbank_id')}}"
      - name: bvn
        description: "{{doc('wb_dim_clientbank_bvn')}}"
      - name: account_number
        description: "{{doc('wb_dim_clientbank_account_number')}}"
      - name: bank_code
        description: "{{doc('wb_dim_clientbank_bank_code')}}"
      - name: bank_name
        description: "{{doc('wb_dim_clientbank_bank_name')}}"
      - name: bank_country
        description: "{{doc('wb_dim_clientbank_bank_country')}}"
      - name: created
        description: "{{doc('wb_dim_clientbank_created')}}"
      - name: updated
        description: "{{doc('wb_dim_clientbank_updated')}}"
      - name: client_id
        description: "{{doc('wb_dim_clientbank_client_id')}}"
      - name: tenant_id
        description: "{{doc('wb_dim_clientbank_tenant_id')}}"
      - name: preferred
        description: "{{doc('wb_dim_clientbank_preferred')}}"
      - name: is_deleted
        description: "{{doc('wb_dim_clientbank_is_deleted')}}"
      - name: is_approved
        description: "{{doc('wb_dim_clientbank_is_approved')}}"
      - name: is_rejected
        description: "{{doc('wb_dim_clientbank_is_rejected')}}"
      - name: is_reverted
        description: "{{doc('wb_dim_clientbank_is_reverted')}}"
      - name: is_verified
        description: "{{doc('wb_dim_clientbank_is_verified')}}"
      - name: is_bank_deleted
        description: "{{doc('wb_dim_clientbank_is_bank_deleted')}}"
      - name: is_nominated
        description: "{{doc('wb_dim_clientbank_is_nominated')}}"
      - name: approval_date
        description: "{{doc('wb_dim_clientbank_approval_date')}}"
      - name: approval_done
        description: "{{doc('wb_dim_clientbank_approval_done')}}"
      - name: created_by_id
        description: "{{doc('wb_dim_clientbank_created_by_id')}}"
      - name: next_approval
        description: "{{doc('wb_dim_clientbank_next_approval')}}"
      - name: rejected_date
        description: "{{doc('wb_dim_clientbank_rejected_date')}}"
      - name: revert_reason
        description: "{{doc('wb_dim_clientbank_revert_reason')}}"
      - name: rejected_by_id
        description: "{{doc('wb_dim_clientbank_rejected_by_id')}}"
      - name: created_offline
        description: "{{doc('wb_dim_clientbank_created_offline')}}"
      - name: rejection_reason
        description: "{{doc('wb_dim_clientbank_rejection_reason')}}"

  - name: wb_dim_client
    description: "This table stores records of clients' personal details, including contact information, on the workbench platform"
    columns:
      - name: id
        description: "{{doc('wb_dim_client_id')}}"
      - name: bvn
        description: "{{doc('wb_dim_client_bvn')}}"
      - name: cid
        description: "{{doc('wb_dim_client_cid')}}"
      - name: name
        description: "{{doc('wb_dim_client_name')}}"
      - name: email
        description: "{{doc('wb_dim_client_email')}}"
      - name: phone
        description: "{{doc('wb_dim_client_phone')}}"
      - name: address
        description: "{{doc('wb_dim_client_address')}}"
      - name: created
        description: "{{doc('wb_dim_client_created')}}"
      - name: id_type
        description: "{{doc('wb_dim_client_id_type')}}"
      - name: updated
        description: "{{doc('wb_dim_client_updated')}}"
      - name: user_id
        description: "{{doc('wb_dim_client_user_id')}}"
      - name: temp_cid
        description: "{{doc('wb_dim_client_temp_cid')}}"
      - name: contacted
        description: "{{doc('wb_dim_client_contacted')}}"
      - name: id_number
        description: "{{doc('wb_dim_client_id_number')}}"
      - name: id_status
        description: "{{doc('wb_dim_client_id_status')}}"
      - name: is_active
        description: "{{doc('wb_dim_client_is_active')}}"
      - name: is_deleted
        description: "{{doc('wb_dim_client_is_deleted')}}"
      - name: language_id
        description: "{{doc('wb_dim_client_language_id')}}"
      - name: company_type
        description: "{{doc('wb_dim_client_company_type')}}"
      - name: country_code
        description: "{{doc('wb_dim_client_country_code')}}"
      - name: matched_name
        description: "{{doc('wb_dim_client_matched_name')}}"
      - name: was_restored
        description: "{{doc('wb_dim_client_was_restored')}}"
      - name: created_by_id
        description: "{{doc('wb_dim_client_created_by_id')}}"
      - name: date_restored
        description: "{{doc('wb_dim_client_date_restored')}}"
      - name: phone_invalid
        description: "{{doc('wb_dim_client_phone_invalid')}}"
      - name: is_id_verified
        description: "{{doc('wb_dim_client_is_id_verified')}}"
      - name: is_phone_verified
        description: "{{doc('wb_dim_client_is_phone_verified')}}"
      - name: is_tenant_default
        description: "{{doc('wb_dim_client_is_tenant_default')}}"
      - name: last_verify_attempt
        description: "{{doc('wb_dim_client_last_verify_attempt')}}"
      - name: logistic_officer_id
        description: "{{doc('wb_dim_client_logistic_officer_id')}}"
      - name: phone_number_status
        description: "{{doc('wb_dim_client_phone_number_status')}}"
      - name: name_matches_verified_id
        description: "{{doc('wb_dim_client_name_matches_verified_id')}}"
      - name: client_id
        description: "{{doc('wb_dim_client_client_id')}}"
      - name: client_type
        description: "{{doc('wb_dim_client_client_type')}}"
