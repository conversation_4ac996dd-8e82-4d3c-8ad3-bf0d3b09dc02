

with wallet as (
select  id,
		total_balance,
		available_balance,
		lien_balance,
		cash_advance_limit,
		cash_advance_spent,
		created,
		updated,
		client_id,
		cash_advance_balance,
		is_deleted,
		agent_id,
		currency_id
	from  {{source('ovs','crm_clientwallet')}}  
),
oms as (
select  id,
		name oms_name,
		oms_code
from   {{source('ovs','crm_omsprovider')}} ),
currency as (
select 	id,
		name currency_name,
		code
        currency_code
from 	 {{source('ovs','ecn_currency')}}  ecn_currency),
client as (
select id,cid
from  {{source('ovs','crm_client')}}   ),

client_wallet as (
select 	wallet.id,
		client.cid,
		wallet.created,
		wallet.updated,
		wallet.total_balance,
		wallet.available_balance,
		wallet.lien_balance,
		wallet.cash_advance_limit,
		wallet.cash_advance_spent,
		wallet.cash_advance_balance,
		currency.currency_name,
		currency.currency_code,
		oms.oms_name,
		oms.oms_code,
		wallet.is_deleted
	from wallet
	left join client
	on wallet.client_id = client.id
	left join currency
	on wallet.currency_id = currency.id
	left join oms
	on wallet.agent_id = oms.id
)

select *
from client_wallet
