{{ config ( materialized = 'ephemeral')}}

WITH
contract AS
	(
	SELECT id::text, price, tenure, created, updated, is_deleted, contract_id, is_cancelled, delivery_status, 
		buy_match_order_id as match_order_id, buy_match_order_id, buyer_location_code, sell_match_order_id, seller_location_code, logistics_officer_id
	FROM {{ source ( 'ovs', 'otc_trade_contract' ) }}
	
		UNION
	
	SELECT concat(id::text, '-Sell') id, price, tenure, created, updated, is_deleted, contract_id, is_cancelled, delivery_status, 
		sell_match_order_id as match_order_id, buy_match_order_id, buyer_location_code, sell_match_order_id, seller_location_code, logistics_officer_id
	FROM {{ source ( 'ovs', 'otc_trade_contract' ) }}
	)

SELECT *
FROM contract