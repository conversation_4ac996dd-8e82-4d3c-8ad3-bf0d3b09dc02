{{ config(materialized='table') }}

with client_bank_info as (
		select  id ,
				bvn,
				bank_id,
				created,
				updated,
				client_id,
				preferred,
				tenant_id,
				is_deleted,
				is_approved,
				is_rejected,
				is_reverted,
				is_verified,
				is_nominated,
				approval_date,
				approval_done,
				created_by_id,
				next_approval,
				rejected_date,
				revert_reason,
				account_number,
				rejected_by_id,
				created_offline,
				rejection_reason
		from  {{source('workbench','crm_clientbankinformation')}} 
	),

	
bank as (
		select 
				id,
				code bank_code,
				name bank_name,
				country_id,
				is_deleted as is_bank_deleted,
				created_by_id
		from  {{source('workbench','bank_bank')}} 
	),
	
country as (
		select 	id,
				name country_name,
				region continent
			
		from  {{source('workbench','location_country')}} 
        ),
	
bank_country_table as (
		select 	
				bank.id,
				bank.bank_code,
				bank.bank_name,
				bank.is_bank_deleted,
				country.country_name bank_country
		from bank
		left join country
		on bank.country_id = country.id ),
	
client_bank as(
		select  
				client_bank_info.id ,
				client_bank_info.bvn,
				client_bank_info.account_number,
				bank_country_table.bank_code,
				bank_country_table.bank_name,
				bank_country_table.bank_country,
				client_bank_info.created,
				client_bank_info.updated,
				client_bank_info.client_id,
				client_bank_info.tenant_id,
				client_bank_info.preferred,
				client_bank_info.is_deleted,
				client_bank_info.is_approved,
				client_bank_info.is_rejected,
				client_bank_info.is_reverted,
				client_bank_info.is_verified,
				bank_country_table.is_bank_deleted,
				client_bank_info.is_nominated,
				client_bank_info.approval_date,
				client_bank_info.approval_done,
				client_bank_info.created_by_id,
				client_bank_info.next_approval,
				client_bank_info.rejected_date,
				client_bank_info.revert_reason,
				client_bank_info.rejected_by_id,
				client_bank_info.created_offline,
				client_bank_info.rejection_reason
		from client_bank_info
		left join bank_country_table 
		on client_bank_info.bank_id =  bank_country_table.id
		
	)
	
select *
from client_bank  --154027 ,154939

	
	