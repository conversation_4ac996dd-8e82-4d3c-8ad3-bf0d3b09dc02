from datetime import datetime,timed<PERSON>ta
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from gxoperator import GX<PERSON>perator
from credentials import db_conn

#define great expectations operator
gx = GXOperator()
source_engine, conn_source = db_conn()
# Define your Python function to run
def dim_farmer():    
    json_result  = gx.run_expectations(data_asset_name='dim_farmer',
                        expectation_suite_name='dim_farmer_expectations')
    gx.expectations_to_db(checkpoint_jsonresult=json_result,source_engine=source_engine)
    

def fact_loan():    
    json_result  = gx.run_expectations(data_asset_name='fact_loan',
                        expectation_suite_name='fact_loan_expectations')
    gx.expectations_to_db(checkpoint_jsonresult=json_result,source_engine=source_engine)
    
def fact_grn():    
    json_result  = gx.run_expectations(data_asset_name='fact_grn',
                        expectation_suite_name='fact_grn_expectations')
    gx.expectations_to_db(checkpoint_jsonresult=json_result,source_engine=source_engine)

def dim_warehouse():    
    json_result  = gx.run_expectations(data_asset_name='dim_warehouse',
                        expectation_suite_name='dim_warehouse_expectations')
    gx.expectations_to_db(checkpoint_jsonresult=json_result,source_engine=source_engine)

def dim_item():    
    json_result  = gx.run_expectations(data_asset_name='dim_item',
                        expectation_suite_name='dim_item_expectations')
    gx.expectations_to_db(checkpoint_jsonresult=json_result,source_engine=source_engine)
         
# Define your DAG
dag = DAG(
    'gx_workbench_dag',
    description='Run a Python script in a DAG',
    start_date=datetime(2023, 10, 11),  # Specify the start date
    schedule_interval='0 0 * * 3',
    tags =  ['great_expectations','workbench_mart']
)

# Create a PythonOperator that runs your Python function
dim_farmer_script = PythonOperator(
    task_id='dim_farmer_expectations',
    python_callable=dim_farmer,
    dag=dag,
)


fact_loan_script = PythonOperator(
    task_id='fact_loan_expectations',
    python_callable=fact_loan,
    dag=dag,
)

fact_grn_script = PythonOperator(
    task_id='fact_grn_expectations',
    python_callable=fact_grn,
    dag=dag,
)

dim_warehouse_script = PythonOperator(
    task_id='dim_warehouse_expectations',
    python_callable=dim_warehouse,
    dag=dag,
)

dim_item_script = PythonOperator(
    task_id='dim_item_expectations',
    python_callable=dim_item,
    dag=dag,
)

# Set up the task dependency, if necessary
dim_farmer_script >> fact_loan_script >> fact_grn_script >> dim_warehouse_script >> dim_item_script
