models:
  - name: fact_grn
    description: "contains records on grn transactions"
    columns:
      - name: cid
        data_type: text
        description: "The unique identifier of the Goods Receipt Note (GRN)."
        tests:
          - not_null
          - unique
      - name: bags
        data_type: bigint
        description: "The number of bags in the GRN."
      - name: grade
        data_type: text
        description: "The grade associated with the GRN (1,2,3)"
      - name: grn_id
        data_type: text
        description: "The unique identifier of the GRN."
      - name: receipt_id
        data_type: text
        description: "The unique identifier of the receipt associated with the GRN."
      - name: created
        data_type: timestamp with time zone
        description: "Timestamp indicating when the GRN was created."
        tests:
          - not_null
      - name: updated
        data_type: timestamp with time zone
        description: "Timestamp indicating the last update time for this GRN."
        tests:
          - not_null
      - name: gross_weight
        data_type: double precision
        description: "The gross weight of the GRN."
      - name: net_weight
        data_type: double precision
        description: "The net weight of the GRN."
      - name: deduction
        data_type: double precision
        description: "The deduction amount in the GRN."
      - name: total_deduction
        data_type: double precision
        description: "The total deduction amount in the GRN."
      - name: moisture
        data_type: double precision
        description: "The moisture percentage in the GRN."
      - name: total_commodity_price
        data_type: double precision
        description: "The total commodity price in the GRN."
      - name: price_per_tonne
        data_type: double precision
        description: "The price per tonne in the GRN."
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float
      - name: transaction_type
        data_type: text
        description: "The type of transaction associated with the GRN('Broker Payment','Loan Repayment','Trade','Warehouse Receipt','Storage','Storage To Trade','Com To Input','Com For Equity')"
      - name: approval_permissions
        data_type: text
        description: "Permissions required for approval of the GRN."
      - name: approval_done
        data_type: text
        description: "Approval status of the GRN."
      - name: is_approved
        data_type: boolean
        description: "A flag indicating whether the GRN is approved."
        tests:
          - not_null
      - name: is_approval_completed
        data_type: boolean
        description: "A flag indicating whether the approval process for the GRN is completed."
      - name: approval_date
        data_type: timestamp with time zone
        description: "Timestamp indicating when the GRN was approved."
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              row_condition: "is_approved = 'true'"
      - name: is_received_at_warehouse
        data_type: boolean
        description: "A flag indicating whether the GRN is received at the warehouse."
      - name: is_reverted
        data_type: boolean
        description: "A flag indicating whether the GRN is reverted."
      - name: rejection_reason
        data_type: text
        description: "The reason for rejection of the GRN."
      - name: total_payable_price
        data_type: double precision
        description: "The total payable price in the GRN."
        tests:
          - not_null
      - name: transaction_fees
        data_type: double precision
        description: "The transaction fees associated with the GRN."
      - name: is_processed
        data_type: boolean
        description: "A flag indicating whether the GRN is processed."
      - name: is_disabled_for_listing
        data_type: boolean
        description: "A flag indicating whether the GRN is disabled for listing."
      - name: spot_payment
        data_type: double precision
        description: "Spot payment amount in the GRN."
      - name: employee_id
        data_type: text
        description: "The unique identifier of the employee associated with the GRN."
      - name: cash_advance_account_pk
        data_type: text
        description: "The primary key of the cash advance account associated with the GRN."
      - name: item_name
        data_type: text
        description: "The name of the item associated with the GRN."
      - name: item_code
        data_type: text
        description: "The code representing the item associated with the GRN."
      - name: item_type
        data_type: text
        description: "The type of the item associated with the GRN."
      - name: warehouse_code
        data_type: text
        description: "The code representing the warehouse associated with the GRN."
      - name: is_deleted
        data_type: boolean
        description: "A flag indicating whether the GRN is marked as deleted."
      - name: next_approval
        data_type: text
        description: "The next approval status of the GRN."
      - name: is_rejected
        data_type: boolean
        description: "A flag indicating whether the GRN is rejected"
        tests:
          - not_null
      - name: rejected_date
        data_type: timestamp with time zone
        description: "Timestamp indicating when the GRN was rejected."
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              row_condition: "is_rejected = 'true'"
      - name: created_offline
        data_type: timestamp with time zone
        description: "Timestamp indicating when the GRN was created offline."
      - name: is_traded
        data_type: boolean
        description: "A flag indicating whether the GRN is traded."
        tests:
          - not_null
      - name: raised_for_farmer
        data_type: boolean
        description: "A flag indicating whether the GRN was raised for a farmer."
      - name: cash_advance_account_paid
        data_type: boolean
        description: "A flag indicating whether the cash advance account associated with the GRN is paid."
      - name: truck_no
        data_type: text
        description: "The truck number associated with the GRN."
      - name: created_by_id
        data_type: bigint
        description: "The unique identifier of the user who created the GRN."
      - name: goods_receipt_id
        data_type: bigint
        description: "The unique identifier of the goods receipt associated with the GRN."
      - name: rejected_by_id
        data_type: bigint
        description: "The unique identifier of the user who rejected the GRN."
      - name: cash_advance_account_id
        data_type: bigint
        description: "The unique identifier of the cash advance account associated with the GRN."
      - name: additional_fees
        data_type: double precision
        description: "Additional fees associated with the GRN."
      - name: wm_updated
        data_type: boolean
        description: "A flag indicating whether the warehouse was updated for the GRN."
      - name: is_clean
        data_type: boolean
        description: "A flag indicating whether the GRN is clean."
      - name: revert_reason
        data_type: text
        description: "The reason for reverting the GRN."
      - name: is_accounting_posted
        data_type: boolean
        description: "A flag indicating whether the GRN is posted for accounting."
      - name: tenant_id
        data_type: bigint
        description: "The unique identifier of the tenant associated with the GRN."
      - name: is_uploaded
        data_type: boolean
        description: "A flag indicating whether the GRN is uploaded."
      - name: certified
        data_type: boolean
        description: "A flag indicating whether the GRN is certified."
      - name: is_edited
        data_type: boolean
        description: "A flag indicating whether the GRN is edited."
      - name: payment_option
        data_type: text
        description: "The payment option associated with the GRN."
      - name: preferred_bank_account_id
        data_type: bigint
        description: "The unique identifier of the preferred bank account associated with the GRN."
        tests:
          - not_null
      - name: discount
        data_type: double precision
        description: "The discount amount associated with the GRN."
      - name: source
        data_type: text
        description: "The source of the GRN."

