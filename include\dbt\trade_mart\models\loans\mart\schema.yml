models:
  - name: fact_loan
    description: "contains loan records"
    columns:
      - name: id
        data_type: bigint
        description: "The unique identifier of the loan record."
        tests:
          - not_null
          - unique
      - name: created
        data_type: timestamp with time zone
        description: "Timestamp indicating when the loan record was created."
        tests:
          - not_null
      - name: updated
        data_type: timestamp with time zone
        description: "Timestamp indicating the last update time for this loan record."
        tests:
          - not_null
      - name: loan_bundlename
        data_type: text
        description: "The bundle name associated with the loan."
      - name: created_offline
        data_type: timestamp with time zone
        description: "Timestamp indicating when the loan record was created offline."
      - name: approval_date
        data_type: timestamp with time zone
        description: "Timestamp indicating when the loan was approved."
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              row_condition: "is_approved = 'true'"
      - name: farmer_id
        data_type: bigint
        description: "The unique identifier of the farmer associated with the loan."
        tests:
          - not_null
      - name: project_id
        data_type: bigint
        description: "The unique identifier of the project associated with the loan."
      - name: project_start_date
        data_type: timestamp with time zone
        description: "Timestamp indicating the start date of the project associated with the loan."
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              row_condition: "project_id is not null"
      - name: project_name
        data_type: text
        description: "The name of the loan project. Projects for wet season loans contains 'wet'."
      - name: project_code
        data_type: text
        description: "The code representing the loan project."
      - name: maturity_date
        data_type: date
        description: "The maturity date of the loan."
      - name: warehouse_id
        data_type: bigint
        description: "The unique identifier of the warehouse associated with the loan."
      - name: warehouse_name
        data_type: text
        description: "The name of the warehouse associated with the loan."
      - name: data_identification_verification
        data_type: double precision
        description: ""
      - name: tenant_id
        data_type: bigint
        description: "The unique identifier of the tenant  associated with the loan."
      - name: ln_id
        data_type: text
        description: "The loan ID "
        tests:
          - not_null
          - unique
      - name: hectare
        data_type: bigint
        description: "The area of the farm in hectares for which the loan is provided."
      - name: total_loan_value
        data_type: double precision
        description: "The total value of the loan."
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              row_condition: "ln_id is not null"
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float 
      - name: repayment_value
        data_type: double precision
        description: "The repayment value of the loan."
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float 
      - name: amount_repaid
        data_type: double precision
        description: "The amount that has been repaid for the loan."
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float 
      - name: insurance
        data_type: double precision
        description: "The insurance amount associated with the loan."
      - name: crg
        data_type: double precision
        description: "The credit risk guarantee amount for the loan."
      - name: interest
        data_type: double precision
        description: "The interest amount for the loan."
      - name: admin_fee
        data_type: double precision
        description: "The administrative fee associated with the loan."
      - name: equity
        data_type: double precision
        description: "The equity amount for the loan."
      - name: to_balance
        data_type: double precision
        description: "The balance amount remaining on the loan."
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float 
      - name: loan_status
        data_type: text
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: text
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['Is owing','Not owing','Overage']
              quote_values: true
      - name: is_repaid
        data_type: boolean
        description: "A flag indicating whether the loan is fully repaid. always use this to check if a loan has been paid back"          
        tests:
          - not_null
      - name: is_approved
        data_type: boolean
        description: "A flag indicating whether the loan is approved."
        tests:
          - not_null
      - name: is_approval_completed
        data_type: boolean
        description: "A flag indicating whether the approval process for the loan is completed."
        tests:
          - not_null
      - name: is_rejected
        data_type: boolean
        description: " A flag indicating whether the loan is rejected."
        tests:
          - not_null
      - name: is_reverted
        data_type: boolean
        description: "A flag indicating whether the loan status is reverted."
        tests:
          - not_null 

  - name: fact_loan_breakdown
    description: "contains a breakdown of loan componets contained in fact_loan table"
    columns:
      - name: id
        data_type: bigint
        description: "The unique identifier of the loan breakdown record."
        tests:
          - not_null
          - unique
      - name: created
        data_type: timestamp with time zone
        description: "Timestamp indicating when the loan breakdown record was created."
        tests:
          - not_null
      - name: updated
        data_type: timestamp with time zone
        description: "Timestamp indicating the last update time for this loan breakdown record."
        tests:
          - not_null
      - name: maturity_date
        data_type: timestamp with time zone
        description: "The maturity date of the loan breakdown"
        tests:
          - not_null
      - name: farmer_id
        data_type: bigint
        description: "The unique identifier of the farmer associated with the loan breakdown ref dim_farmer.id"
        tests:
          - not_null
      - name: project_id
        data_type: bigint
        description: "The unique identifier of the project associated with the loan breakdown."
        tests:
          - not_null
      - name: warehouse_id
        data_type: bigint
        description: "The unique identifier of the warehouse associated with the loan breakdown ref dim_warehouse.id"
        tests:
          - not_null
      - name: item_id
        data_type: bigint
        description: "The unique identifier of the item associated with the loan breakdown ref dim_item.id"
        tests:
          - not_null
      - name: tenant_id
        data_type: bigint
        description: "The unique identifier of the tenant  associated with the loan breakdown."
        tests:
          - not_null
      - name: ln_id
        data_type: text
        description: "The loan ID associated with the loan breakdown."
        tests:
          - not_null
      - name: line_id
        data_type: text
        description: "The line ID associated with the loan breakdown."
        tests:
          - not_null
      - name: hectare
        data_type: bigint
        description: "The area of the farm in hectares for which the loan breakdown is provided."
        tests:
          - not_null
      - name: units
        data_type: bigint
        description: "The number of units associated with the loan ."
        tests:
          - not_null
      - name: unit_price
        data_type: double precision
        description: "The unit price associated with the loan breakdown."
        tests:
          - not_null
      - name: total_price
        data_type: double precision
        description: "The total price associated with the loan breakdown."
        tests:
          - not_null
      - name: total_loan_value
        data_type: double precision
        description: "The total value of the loan breakdown."
        tests:
          - not_null
      - name: repayment_value
        data_type: double precision
        description: "The repayment value associated with the loan breakdown."
        tests:
          - not_null
      - name: amount_repaid
        data_type: double precision
        description: "The amount repaid for the loan breakdown."
        tests:
          - not_null
      - name: insurance
        data_type: double precision
        description: "The insurance amount associated with the loan breakdown."
        tests:
          - not_null
      - name: crg
        data_type: double precision
        description: "The credit risk guarantee amount for the loan breakdown."
        tests:
          - not_null
      - name: interest
        data_type: double precision
        description: "The interest amount associated with the loan breakdown."
        tests:
          - not_null
      - name: admin_fee
        data_type: double precision
        description: "The administrative fee associated with the loan breakdown."
        tests:
          - not_null
      - name: equity
        data_type: double precision
        description: "The equity amount associated with the loan breakdown."
        tests:
          - not_null
      - name: to_balance
        data_type: double precision
        description: "The balance amount remaining on the loan breakdown."
        tests:
          - not_null
      - name: loan_status
        data_type: text
        description: " The status of the loan breakdown ('Not owing','Overage','Is owing')."
        tests:
          - not_null
      - name: data_identification_verification
        data_type: double precision
        description: "The verification status for data identification in the loan breakdown."
        tests:
          - not_null
      - name: value_chain_management
        data_type: double precision
        description: "The value chain management status in the loan breakdown"
      - name: is_repaid
        data_type: boolean
        description: "A flag indicating whether the loan breakdown is fully repaid."
        tests:
          - not_null
      - name: is_approved
        data_type: boolean
        description: "A flag indicating whether the loan breakdown is approved."
        tests:
          - not_null
      - name: is_approval_completed
        data_type: boolean
        description: "A flag indicating whether the approval process for the loan breakdown is completed."
        tests:
          - not_null
      - name: is_rejected
        data_type: boolean
        description: " A flag indicating whether the loan breakdown is rejected."
        tests:
          - not_null
      - name: is_reverted
        data_type: boolean
        description: "A flag indicating whether the loan breakdown status is reverted."
        tests:
          - not_null