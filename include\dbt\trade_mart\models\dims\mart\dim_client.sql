{{ config(materialized='table') 
}}

with client_typetable as ( 
    select distinct(cct.client_id),
                    string_agg(ct.name ,',') client_type
from {{source('workbench','crm_client_client_type')}} cct
left join  {{source('workbench','crm_clienttype')}}  ct
on cct.clienttype_id = ct.id
group by cct.client_id
),
ct as (
    select *
    from {{source('workbench','crm_clienttype')}}
),

final as (
select * 
from {{source('workbench','crm_client')}}   client
left join   client_typetable
on client.id = client_typetable.client_id

)

select *
from final