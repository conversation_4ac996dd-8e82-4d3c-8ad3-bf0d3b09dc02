{% docs wb_dim_client_id %}
Unique identifier of the client.
{% enddocs %}

{% docs wb_dim_client_bvn %}
Bank Verification Number associated with the client.
{% enddocs %}

{% docs wb_dim_client_cid %}
Client ID associated with the client.
{% enddocs %}

{% docs wb_dim_client_name %}
Name of the client.
{% enddocs %}

{% docs wb_dim_client_email %}
Email address of the client.
{% enddocs %}

{% docs wb_dim_client_phone %}
Phone number of the client.
{% enddocs %}

{% docs wb_dim_client_address %}
Address of the client.
{% enddocs %}

{% docs wb_dim_client_created %}
Timestamp indicating when the client record was created.
{% enddocs %}

{% docs wb_dim_client_id_type %}
Type of identification document used by the client (e.g., 'International Passport', 'National ID Card', 'Cooperative ID Card', 'BVN').
{% enddocs %}

{% docs wb_dim_client_updated %}
Timestamp indicating the last update time for this client record.
{% enddocs %}

{% docs wb_dim_client_user_id %}
Unique identifier of the user associated with the client on the account_user table.
{% enddocs %}

{% docs wb_dim_client_temp_cid %}
Temporary Client ID associated with the client.
{% enddocs %}

{% docs wb_dim_client_contacted %}
A flag indicating whether the client has been contacted.
{% enddocs %}

{% docs wb_dim_client_id_number %}
Identification number associated with the client.
{% enddocs %}

{% docs wb_dim_client_id_status %}
Status of the client's identification document.
{% enddocs %}

{% docs wb_dim_client_is_active %}
A flag indicating whether the client is active.
{% enddocs %}

{% docs wb_dim_client_is_deleted %}
A flag indicating whether the client record is marked as deleted.
{% enddocs %}

{% docs wb_dim_client_language_id %}
Unique identifier of the language associated with the client.
{% enddocs %}

{% docs wb_dim_client_company_type %}
Type of company associated with the client.
{% enddocs %}

{% docs wb_dim_client_country_code %}
Country code associated with the client.
{% enddocs %}

{% docs wb_dim_client_matched_name %}
Name associated with the client on the identification document provided.
{% enddocs %}

{% docs wb_dim_client_was_restored %}
A flag indicating whether the client record was restored.
{% enddocs %}

{% docs wb_dim_client_created_by_id %}
Unique identifier of the user who created the client record.
{% enddocs %}

{% docs wb_dim_client_date_restored %}
Timestamp indicating when the client record was restored.
{% enddocs %}

{% docs wb_dim_client_phone_invalid %}
A flag indicating whether the client's phone number is invalid.
{% enddocs %}

{% docs wb_dim_client_is_id_verified %}
A flag indicating whether the client's identification is verified.
{% enddocs %}

{% docs wb_dim_client_is_phone_verified %}
A flag indicating whether the client's phone number is verified.
{% enddocs %}

{% docs wb_dim_client_is_tenant_default %}
A flag indicating whether the client belongs to the default tenant.
{% enddocs %}

{% docs wb_dim_client_last_verify_attempt %}
Timestamp indicating the last verification attempt for the client.
{% enddocs %}

{% docs wb_dim_client_logistic_officer_id %}
Unique identifier of the logistic officer associated with the client.
{% enddocs %}

{% docs wb_dim_client_phone_number_status %}
Status of verification of the client's phone number.
{% enddocs %}

{% docs wb_dim_client_name_matches_verified_id %}
A flag indicating whether the client's name matches the verified identification.
{% enddocs %}

{% docs wb_dim_client_client_id %}
Unique identifier of the client.
{% enddocs %}

{% docs wb_dim_client_client_type %}
Type of client (e.g., 'Client', 'Farmer', 'Input Partner', 'Investor', 'Logistic Partner').
{% enddocs %}
