{% docs cx_fact_collateralmgtloan_created %}
The timestamp when the collateral management loan record was created.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_updated %}
The timestamp when the collateral management loan record was last updated.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_client_id %}
The unique identifier of the client associated with the collateral management loan (reference to comx_mart.dim_client.id).
{% enddocs %}

{% docs cx_fact_collateralmgtloan_financier_name %}
The name of the financier providing the loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_financier_accounttype %}
The account type of the financier providing the loan (Investor).
{% enddocs %}

{% docs cx_fact_collateralmgtloan_financier_phone %}
The contact phone number of the financier providing the loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_reference_id %}
A unique reference ID for the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_loan_type %}
The type of loan provided for collateral management ('ASP', 'Collateral').
{% enddocs %}

{% docs cx_fact_collateralmgtloan_deleted_at %}
The timestamp indicating when the collateral management loan was deleted.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_is_deleted %}
A flag indicating whether the collateral management loan is deleted.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_loan_value %}
The value of the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_tranche_name %}
The name of the loan tranche ('Trade Finance', 'Tranche 2', 'ABCP', 'Non-Collateral', 'Tranche 3', 'Tranche 1', 'Tranche-1', 'ASP Loan', 'ASP', 'REPO', 'Tranche-2').
{% enddocs %}

{% docs cx_fact_collateralmgtloan_tranche_value %}
The value of the loan tranche.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_tranche_tenure %}
The tenure of the loan tranche.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_is_released %}
A flag indicating whether the loan tranche is released.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_loan_tenure %}
The tenure of the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_bundle_fields %}
Fields related to loan bundle information.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_interest_rate %}
The interest rate associated with the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_is_ovs_synced %}
A flag indicating whether the loan is synced with the OVS system.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_is_released_at %}
The timestamp indicating when the loan tranche is released.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_loan_bundle_id %}
The unique identifier of the loan bundle associated with the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_is_below_margin %}
A flag indicating whether the loan is below margin.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_loan_start_date %}
The start date of the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_is_ovs_synced_at %}
The timestamp indicating when the loan is synced with OVS.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_date_below_margin %}
The timestamp indicating when the loan is below margin.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_initial_collateral_value %}
The initial value of the collateral.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_equity %}
The equity amount related to the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_data_capture %}
The data capture related to the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_logistic_fee %}
The logistic fee associated with the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_insurance_crg %}
The insurance CRG (Credit Risk Guarantee) associated with the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_interest_value %}
The interest value of the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_insurance_yield %}
The insurance yield associated with the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_repayment_value %}
The repayment value of the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_transaction_fee %}
The transaction fee associated with the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_actual_loan_value %}
The actual value of the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_cash_contribution %}
The cash contribution related to the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_value_chain_management %}
The value chain management related to the collateral management loan.
{% enddocs %}
