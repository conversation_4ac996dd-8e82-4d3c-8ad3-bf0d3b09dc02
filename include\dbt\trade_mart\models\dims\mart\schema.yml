models:
  - name: dim_client
    description: "contains records on clients personal details like contact details for the on workbench platform, do not confuse the client in this table for the client on the comx_mart.dim_client"
    columns:
      - name: _airbyte_unique_key
        data_type: text
        description: ""
      - name: id
        data_type: bigint
        description: "The unique identifier of the client table"
        tests:
          - unique
          - not_null
      - name: bvn
        data_type: text
        description: "The Bank Verification Number associated with the client."
        tests:
          - dbt_expectations.expect_column_value_lengths_to_equal:
              value: 11
      - name: cid
        data_type: text
        description: "The Client ID associated with the client."
        tests:
          - unique
          - not_null
          - dbt_expectations.expect_column_value_lengths_to_equal:
                value: 10
      - name: name
        data_type: text
        description: "The name of the client."
        tests:
          - not_null
      - name: email
        data_type: text
        description: "The email address of the client"
      - name: phone
        data_type: text
        description: "The phone number of the client."
        tests:
          - not_null
      - name: address
        data_type: text
        description: "The address of the client."
      - name: created
        data_type: timestamp with time zone
        description: "Timestamp indicating when the client record was created."
        tests:
          - not_null
      - name: id_type
        data_type: text
        description: "The type of identification document used by the client e.g ('International Passport' ,'National ID Card','Cooperative ID Card', 'BVN')"
        tests:
          - not_null
        
      - name: updated
        data_type: timestamp with time zone
        description: "Timestamp indicating the last update time for this client record."
        tests:
          - not_null
      - name: user_id
        data_type: bigint
        description: "The unique identifier of the user associated with the client on account_user table."
        tests:
          - not_null
      - name: temp_cid
        data_type: text
        description: "Temporary Client ID associated with the client."
      - name: contacted
        data_type: boolean
        description: "A flag indicating whether the client has been contacted."
      - name: id_number
        data_type: text
        description: "The identification number associated with the client."
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              row_condition: "id_type is not null"
      - name: id_status
        data_type: text
        description: "The status of the client's identification document."
        tests:
          - not_null
      - name: is_active
        data_type: boolean
        description: "A flag indicating whether the client is active."
        tests:
          - not_null
      - name: is_deleted
        data_type: boolean
        description: "A flag indicating whether the client record is marked as deleted."
      - name: language_id
        data_type: bigint
        description: "The unique identifier of the language associated with the client."
      - name: company_type
        data_type: text
        description: "The type of company associated with the client."
      - name: country_code
        data_type: text
        description: "The country code associated with the client."
      - name: matched_name
        data_type: text
        description: "The name associated with the client on the identification document provided."
      - name: was_restored
        data_type: boolean
        description: "A flag indicating whether the client record was restored."
        tests:
          - not_null
      - name: created_by_id
        data_type: bigint
        description: "The unique identifier of the user who created the client record."
      - name: date_restored
        data_type: timestamp with time zone
        description: "Timestamp indicating when the client record was restored."
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              row_condition: "was_restored = 'true'"
      - name: phone_invalid
        data_type: boolean
        description: "A flag indicating whether the client's phone number is invalid."
      - name: is_id_verified
        data_type: boolean
        description: "A flag indicating whether the client's identification is verified."
        tests:
          - not_null
      - name: is_phone_verified
        data_type: boolean
        description: "A flag indicating whether the client's phone number is verified."
        tests:
          - not_null
      - name: is_tenant_default
        data_type: boolean
        description: "A flag indicating whether the client belongs to the default tenant."
        tests:
          - not_null
      - name: last_verify_attempt
        data_type: timestamp with time zone
        description: "Timestamp indicating the last verification attempt for the client."
      - name: logistic_officer_id
        data_type: bigint
        description: "The unique identifier of the logistic officer associated with the client."
      - name: phone_number_status
        data_type: text
        description: "The status of verification of the client's phone number."
      - name: inventory_settings_id
        data_type: bigint
        description: ""
      - name: name_matches_verified_id
        data_type: boolean
        description: "A flag indicating whether the client's name matches the verified identification."
      - name: _airbyte_ab_id
        data_type: character varying
        description: ""
      - name: _airbyte_emitted_at
        data_type: timestamp with time zone
        description: ""
      - name: _airbyte_normalized_at
        data_type: timestamp with time zone
        description: ""
      - name: _airbyte_crm_client_hashid
        data_type: text
        description: ""
      - name: client_id
        data_type: bigint
        description: "The unique identifier of the client."
      - name: client_type
        data_type: text
        description: "The type of client, e.g., 'Client','Farmer','Input Partner','Investor','Logistic Partner',"
        tests:
          - not_null
        
  - name: dim_clientbank
    description: "contains bank details of clients on workbench"
    columns:
      - name: id
        data_type: bigint
        description: "The unique identifier of the client bank record."
        tests:
          - not_null
          - unique
      - name: bvn
        data_type: text
        description: "The Bank Verification Number associated with the client bank account."
        tests:
          - dbt_expectations.expect_column_value_lengths_to_equal:
              value: 11
              row_condition: "id is not null"
      - name: account_number
        data_type: text
        description: "The account number of the client bank account."
        tests:
          - dbt_expectations.expect_column_value_lengths_to_equal:
              value: 10
              row_condition: "id is not null"
      - name: bank_code
        data_type: text
        description: "The code representing the bank associated with the client bank account."
      - name: bank_name
        data_type: text
        description: "The name of the bank associated with the client bank account."
      - name: bank_country
        data_type: text
        description: "The country where the bank associated with the client bank account is located."
      - name: created
        data_type: timestamp with time zone
        description: "Timestamp indicating when the client bank record was created."
        tests:
          - not_null
      - name: updated
        data_type: timestamp with time zone
        description: "Timestamp indicating the last update time for this client bank record."
        tests:
          - not_null
      - name: client_id
        data_type: bigint
        description: "The unique identifier of the client associated with the bank account ref workbench_mart.dim_client.id"
        tests:
          - not_null
      - name: tenant_id
        data_type: bigint
        description: "The unique identifier of the tenant  associated with the client bank account."
        tests:
          - not_null
      - name: preferred
        data_type: boolean
        description: "A flag indicating whether the client bank account is the preferred account."
      - name: is_deleted
        data_type: boolean
        description: "A flag indicating whether the client bank record is marked as deleted."
        tests:
          - not_null
      - name: is_approved
        data_type: boolean
        description: "A flag indicating whether the client bank account is approved."
        tests:
          - not_null
      - name: is_rejected
        data_type: boolean
        description: "A flag indicating whether the client bank account is rejected."
        tests:
          - not_null
      - name: is_reverted
        data_type: boolean
        description: "A flag indicating whether the client bank account status is reverted."
        tests:
          - not_null
      - name: is_verified
        data_type: boolean
        description: "A flag indicating whether the client bank account is verified."
        tests:
          - not_null
      - name: is_bank_deleted
        data_type: boolean
        description: "A flag indicating whether the associated bank is deleted."
        
      - name: is_nominated
        data_type: boolean
        description: "A flag indicating whether the client bank account is nominated."
      - name: approval_date
        data_type: timestamp with time zone
        description: "Timestamp indicating when the client bank account was approved."
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              row_condition: "is_approved = 'true'"
          
      - name: approval_done
        data_type: text
        description: "Indicates the approval status of the client bank account."
      - name: created_by_id
        data_type: bigint
        description: "The unique identifier of the user who created the client bank record."
      - name: next_approval
        data_type: text
        description: "The next approval status for the client bank account."
      - name: rejected_date
        data_type: timestamp with time zone
        description: "Timestamp indicating when the client bank account was rejected"
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              row_condition: "is_rejected = 'true'"
      - name: revert_reason
        data_type: text
        description: "The reason for reverting the status of the client bank account."
      - name: rejected_by_id
        data_type: bigint
        description: "The unique identifier of the user who rejected the client bank account."
      - name: created_offline
        data_type: timestamp with time zone
        description: "Timestamp indicating when the client bank record was created offline."
      - name: rejection_reason
        data_type: text
        description: "The reason for rejecting the client bank account."
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              row_condition: "is_rejected = 'true'"
          
  - name: dim_farmer
    description: "contains records of farmer details that I/we have , note warehouse_name does not exist in this table but you can get it by joining on dim_warehouse"
    columns:
      - name: id
        data_type: bigint
        description: "The unique identifier of the farmer."
        tests:
          - not_null
          - unique
      - name: created
        data_type: timestamp with time zone
        description: "Timestamp indicating when the farmer record was created."
        tests:
          - not_null
      - name: updated
        data_type: timestamp with time zone
        description: "Timestamp indicating the last update time for this farmer record."
        tests:
          - not_null
      - name: is_deleted
        data_type: boolean
        description: ""
      - name: folio_id
        data_type: text
        description: "The folio ID associated with the farmer."
        tests:
          - not_null
          - unique
      - name: title
        data_type: text
        description: "The title of the farmer."
      - name: first_name
        data_type: text
        description: "The first name of the farmer."
        tests:
          - not_null
      - name: last_name
        data_type: text
        description: "The last name of the farmer."
        tests:
          - not_null
      - name: middle_name
        data_type: text
        description: "The middle of the farmer."
      - name: address
        data_type: text
        description: "The address of the farmer."
      - name: gender
        data_type: text
        description: "The gender of the farmer i.e (Male , Female)"
        tests:
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['Other','Male','Female']
              quote_values: true
              row_condition: "id is not null"
      - name: marital_status
        data_type: text
        description: "The marital status of the farmer."
      - name: dob
        data_type: date
        description: "The date of birth of the farmer."
      - name: phone
        data_type: text
        description: "The phone number of the farmer."
      - name: village
        data_type: text
        description: "The village associated with the farmer."
      - name: farm_size
        data_type: text
        description: "The size of the farm owned by the farmer."
      - name: passport_type
        data_type: text
        description: "The type of passport held by the farmer('Driver's License','Others','University ID Card','Drivers License','International Passport','National ID Card','Cooperative ID Card','BVN','Voters Card')"
      - name: passport_number
        data_type: text
        description: "The passport number of the farmer."
      - name: nok_phone
        data_type: text
        description: "The phone number of the next of kin (NOK) of the farmer."
      - name: nok_name
        data_type: text
        description: "The name of the next of kin (NOK) of the farmer."
      - name: nok_relationship
        data_type: text
        description: "The relationship of the next of kin (NOK) to the farmer."
      - name: bvn
        data_type: text
        description: "The Bank Verification Number (BVN) of the farmer."
      - name: farm_coordinates
        data_type: text
        description: "The coordinates of the farmer's farm."
      - name: farm_coordinates_polygon
        data_type: text
        description: "The polygon coordinates of the farmer's farm."
      - name: is_blacklist
        data_type: boolean
        description: ""
      - name: languages
        data_type: text
        description: "The languages spoken by the farmer."
      - name: client_id
        data_type: bigint
        description: "The unique identifier of the client associated with the farmer."
      - name: warehouse_id
        data_type: bigint
        description: "The unique identifier of the warehouse associated with the farmer. This can be used to join with the dim_warehouse table to get warehouse details ref dim_warehouse.id"
      - name: phone_invalid
        data_type: boolean
        description: "A flag indicating whether the phone number of the farmer is invalid."
      - name: phone_number_status
        data_type: text
        description: ""
      - name: coordinate_status
        data_type: text
        description: "('Pending Check','Invalid')"
      - name: id_status
        data_type: text
        description: "('Pending Check','Confirmed Valid','Valid','Invalid','Confirmed Invalid')."
      - name: is_id_verified
        data_type: boolean
        description: ""
      - name: matched_name
        data_type: text
        description: ""
      - name: name_matches_verified_id
        data_type: boolean
        description: ""
      - name: crop_name
        data_type: text
        description: "The name of the crop associated with the farmer."
      - name: crop_code
        data_type: text
        description: "The code representing the crop associated with the farmer."
      - name: feo_name
        data_type: text
        description: "The name of the Field Extension Officer (FEO) associated with the farmer."
      - name: feo_code
        data_type: text
        description: "The code representing the Field Extension Officer (FEO) associated with the farmer."
      - name: feo_phone_number
        data_type: text
        description: "The phone number of the Field Extension Officer (FEO) associated with the farmer."
      - name: cooperative_id
        data_type: bigint
        description: "The unique identifier of the cooperative associated with the farmer."
      - name: cooperative_name
        data_type: text
        description: "The name of the cooperative associated with the farmer."
      - name: cooperative_code
        data_type: text
        description: "The code representing the cooperative associated with the farmer."
      - name: ward
        data_type: text
        description: ""
      - name: account_numbers
        data_type: text
        description: "The account numbers associated with the farmer."
      - name: lga_of_origin
        data_type: text
        description: "The Local Government Area (LGA) of origin for the farmer"
      - name: lga_of_residence
        data_type: text
        description: "The Local Government Area (LGA) of residence for the farmer."
      - name: state_of_origin
        data_type: text
        description: "The state of origin for the farmer."
      - name: state_of_residence
        data_type: text
        description: "The state of residence for the farmer."
      - name: tenant_id
        data_type: bigint
        description: "The unique identifier of the tenant associated with the farmer."
     
  - name: dim_farmercrops
    description: "contains farmers and  the crops they plant;"
    columns:
      - name: folio_id
        data_type: bigint
        description: "TThe folio ID associated with the farmer. This is the unique identifier of each farmer"
        tests:
          - not_null
          - unique
      - name: crop_name
        data_type: text
        description: "The name of the crop."
        tests:
          - not_null
      - name: crop_code
        data_type: text
        description: "The code representing the crop"
        tests:
          - not_null

  - name: dim_item
    description: "contains details of items which grn and loans are raised"
    columns:
      - name: id
        data_type: bigint
        description: "The unique identifier of the item."
        tests:
          - not_null
          - unique
      - name: tenant_id
        data_type: bigint
        description: "The identifier of the tenant associated with the item."
      - name: created
        data_type: timestamp with time zone
        description: "Timestamp indicating when the item record was created."
        tests:
          - not_null
      - name: updated
        data_type: timestamp with time zone
        description: "Timestamp indicating the last update time for this item record."
        tests:
          - not_null
      - name: grade_one_deduction
        data_type: double precision
        description: "The deduction value for grade one of the item."
      - name: grade_two_deduction
        data_type: double precision
        description: "The deduction value for grade two of the item."
      - name: grade_three_deduction
        data_type: double precision
        description: "The deduction value for grade three of the item."
      - name: name
        data_type: text
        description: "The name of the item."
        tests:
          - not_null
      - name: code
        data_type: text
        description: "The code representing the item"
        tests:
          - not_null
      - name: product_type
        data_type: text
        description: "The type of the item, e.g., ('Discount Note' ,'Commodity','Fees','Input', 'Expense') "
        tests:
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['Discount Note' ,'Commodity','Fees','Input', 'Expense']
              quote_values: true
              row_condition: "id is not null"

  - name: dim_warehouse
    description: "contains warehouse information like warehouse name and their location, note that to identify the location of a farmer you can use the warehouse that he belongs to"
    columns:
      - name: id
        data_type: bigint
        description: "The unique identifier of the warehouse."
        tests:
          - not_null
          - unique
      - name: created
        data_type: timestamp with time zone
        description: "Timestamp indicating when the warehouse record was created."
        tests:
          - not_null
      - name: updated
        data_type: timestamp with time zone
        description: "Timestamp indicating the last update time for this warehouse record."
        tests:
          - not_null
      - name: location_id
        data_type: bigint
        description: "The unique identifier of the location associated with the warehouse."
        tests:
          - not_null
      - name: tenant_id
        data_type: bigint
        description: "The unique identifier of the tenant or customer associated with the warehouse."
        tests:
          - not_null
      - name: tenant_name
        data_type: text
        description: "The name of the tenant or customer associated with the warehouse."
        tests:
          - not_null
      - name: warehouse_name
        data_type: text
        description: "The name of the warehouse."
        tests:
          - not_null
      - name: warehouse_code
        data_type: text
        description: "The code representing the warehouse."
      - name: capacity
        data_type: bigint
        description: " The capacity or storage space of the warehouse."
      - name: warehouse_manager_id
        data_type: bigint
        description: "The unique identifier of the warehouse manager."
      - name: address
        data_type: text
        description: "The address of the warehouse."
      - name: longitude
        data_type: double precision
        description: "The longitude coordinates of the warehouse location."
      - name: latitude
        data_type: double precision
        description: "The latitude coordinates of the warehouse location."
      - name: warehouse_email
        data_type: text
        description: "The email address of the warehouse"
      - name: location
        data_type: text
        description: "The specific location of the warehouse, subset of a state."
      - name: state
        data_type: text
        description: "The state or province where the warehouse is located."
      - name: capital
        data_type: text
        description: "The capital city or administrative center associated with the state or province."
      - name: region_code
        data_type: text
        description: "The code representing the region where the warehouse is located."
      - name: region_name
        data_type: text
        description: "The name of the region where the warehouse is located."
      - name: country
        data_type: text
        description: "The country where the warehouse is located."
      - name: country_capital
        data_type: text
        description: "The capital city or administrative center associated with the country."
      - name: continent
        data_type: text
        description: "he continent where the warehouse is located."
      - name: continent_subregion
        data_type: text
        description: "The subregion or subcontinent within the larger continent."
