{{ config(materialized='ephemeral') }}

-- Rank user login time - Their most recent login time is placed first
WITH ranked_client_login AS (
	SELECT 
		activity_time,
		cid,
		ROW_NUMBER() OVER (PARTITION BY cid ORDER BY activity_time DESC) login_rank
	FROM
		{{ ref('stg_activity_with_time_band') }}
	ORDER BY cid, login_rank
),

	
-- Extract each client last login time and the time passed since they were last seen online
client_last_login AS (
	SELECT 
		cid,
		activity_time last_seen,
		AGE(NOW(), activity_time) time_since_last_seen
	FROM ranked_client_login
	WHERE login_rank = 1
    AND cid is NOT NULL
)
SELECT * FROM client_last_login