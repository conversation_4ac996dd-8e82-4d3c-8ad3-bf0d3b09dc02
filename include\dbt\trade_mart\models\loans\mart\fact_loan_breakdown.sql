

{{ config(materialized='table') }}

with final as ( select lline.id,lline.created,lline.updated,loan.maturity_date,loan.farmer_id,
		loan.project_id,loan.warehouse_id,
		lline.item_id,lline.tenant_id,lline.ln_id,lline.line_id,loan.hectare,
		lline.units,lline.unit_price,lline.total_price,loan.total_loan_value,
		loan.repayment_value,loan.amount_repaid,loan.insurance,loan.crg,interest,loan.admin_fee,loan.equity,
		(CASE when loan.repayment_value > loan.amount_repaid then (loan.repayment_value - loan.amount_repaid)
		when (loan.repayment_value < loan.amount_repaid) then (loan.amount_repaid - loan.repayment_value ) 	else 0 end) to_balance,
		(CASE when loan.repayment_value > loan.amount_repaid then 'Is owing' 
		when (loan.repayment_value < loan.amount_repaid) then 'Overage' else 'Not owing' end) loan_status,
		loan.data_identification_verification	,
		loan.value_chain_management,
		loan.is_repaid,
		loan.is_approved,loan.is_approval_completed,loan.is_rejected,loan.is_reverted
from  {{source('workbench','loan_loanline')}}   lline 
inner join   {{source('workbench','loan_loan')}}   loan
on lline.ln_id  = loan.ln_id
)

select * from final