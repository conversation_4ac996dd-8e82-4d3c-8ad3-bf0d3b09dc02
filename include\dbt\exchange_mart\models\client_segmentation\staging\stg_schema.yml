version: 3.9

models:
  - name: stg_exchange_members
    description: "Staging model for exchange clients. All clients who have registered on the exchange platform"
    columns:
      - name: client_id
        description: "Primary identifier for clients"
        tests:
          - unique
          - not_null
      - name: cid
        description: "Client cid. Use to identify user across the exchange system"
        tests:
          - unique
          - not_null

  - name: stg_client_wallet_trade_stream
    description: "Staging model that extracts and combine client trade and wallet activities as their actions on the exchange system"
    columns:
      - name: action_date
        description: "Timestamp for when the action is done by the user"
        tests:
          - not_null
      - name: cid
        description: "Client cid"
        tests:
          - not_null
      - name: action_type
        description: "This flags the kind of action the client took ('Credit', 'Withdrawal', 'Buy', 'Sell')"
  - name: stg_client_commodity_transactions
    description: "Staging model that extracts commodity transactions on the exchange"
    columns:
      - name: trans_cid
        description: "cid of the client involve in the transaction"
        tests:
          - not_null
      - name: trade_created_at
        description: "Timestamp indicated when the transaction order was first raised"
        tests:
          - not_null
      - name: consolidated_commodity_name
        description: "Name of the commodity"
      - name: security_type
        description: "Security type of the commodity (security). This can be considered as the board type. They include ('OTC', 'Spot', 'Dawa', 'FI')"
        tests:
          - not_null
      - name: security_code
        description: "Code code assigned to each securitized commodity. Form from the commodity code and the security type. e.g Maize with commodity code MAZ under Spot board (security type) will have security code SMAZ"
        tests:
          - not_null
      - name: order_type
        description: "This is the type of order under that transaction. Options include ('Buy' and 'Sell')"
        tests:
          - not_null
      - name: executed_volume_kg
        description: "Volume of commodity that was aggregated or traded under a transaction"


  - name: stg_activity_with_time_band
    description: "This staging model extracts all activities and the associated cid"
    columns:
      - name: cid
        description: "Client cid"
        tests:
          - not_null
      - name: activity_time
        description: "Timestamp indicating when action was done"
        tests:
          - not_null
      - name: activity_type
        description: "The type of action done by the user"
      - name: activity_date
        description: "Date of the activity"
        tests:
          - not_null
      - name: activity_hour
        description: "Hour of the day which the action was done"
        tests: 
          - not_null