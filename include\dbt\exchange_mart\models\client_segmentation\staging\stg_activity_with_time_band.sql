{{ config(materialized='ephemeral') }}

WITH activity_with_time_band AS (
    -- Step 1: Extract the clock hour as the time band (based on activity time)
    SELECT DISTINCT
        c.cid,
        aas.created activity_time,
        'activity_stream' activity_type,
        DATE(aas.created) AS activity_date,
        EXTRACT(HOUR FROM aas.created) AS activity_hour
 
    FROM {{ source('comx','crm_client') }} c

    LEFT JOIN {{ source('comx','crm_clientuser') }} cu -- Left join to ensure every user is associated with a cid 

    ON cu.client_id = c.id
 
    LEFT JOIN {{ source('comx', 'administration_activitystream') }} aas

    ON aas.user_id = cu.user_id
 
    WHERE cu.user_id is not Null
    AND cu.id IS NOT NULL    -- Make sure each client gets a client and user id else they are ignored 
    AND c.cid IS NOT NULL    -- Ensure There must be a client ID else user is ignored
    AND c.cid != ''
)
-- Step 2: Calculate the first and last activity time for each user, per day, per hour (time band)
SELECT *
FROM activity_with_time_band

