{{ config ( materialized = 'ephemeral')}}

WITH
dispatches AS
	(
	SELECT contract.contract_id, CONCAT(contract.contract_id, '-', dispatchlog.id) delivery_id,
			dispatchlog.created dispatch_created_at, initial_dis_log.dispatch_date,
			CASE WHEN ((dispatchlog.created at time zone 'WAT') :: time) >= '15:00:00'
					THEN DATE((dispatchlog.created at time zone 'WAT') + INTERVAL '1 day')
			ELSE DATE((dispatchlog.created at time zone 'WAT'))
			END AS adjusted_dispatch_created_at,

			dispatchlog.updated dispatch_updated_at, dispatchlog.bags dispatch_bag, dispatchlog.is_deleted dispatch_is_deleted,
			dispatchlog.is_reverted dispatch_is_reverted, dispatchlog.is_buyer_pickup dispatch_is_buyer_pickup, 
			dispatchlog.delivered_volume,

			CASE WHEN (dispatchlog.is_buyer_pickup is false) or (dispatchlog.is_buyer_pickup is true and dispatchlog.is_settlement_confirmed is true ) 
				THEN dispatchlog.delivered_volume
			ELSE null
			END AS actual_delivered_volume,

			dispatchlog.id dispatchlog_pk, dispatchlog.reverted_from_id,
			initial_dis_log.dispatched_volume initial_dispatched_volume, initial_dis_log.bags dispatched_bags,
			wb_phy_dispatch.weight/1000 wb_phy_dispatch_weight, wb_phy_dispatch.bags wb_phy_dispatch_bags, 
			initial_dis_log.dispatch_id, initial_dis_log.physical_dispatch_log_id, dispatchlog.is_settlement_confirmed,
			initial_dis_log.delivery_company, initial_dis_log.truck_number, initial_dis_log.drivers_name, initial_dis_log.drivers_phone,
			initial_dis_log.status dispatch_status,
			initial_dis_log.warehouse_code, initial_dis_log.warehouse_name, wb_phy_dispatch.tenant_id wb_tenant_id, dispatchlog.initial_dispatch_id

	FROM {{ source ('ovs', 'otc_trade_otcdispatchlog') }} dispatchlog
	
	LEFT JOIN {{ source ( 'ovs', 'otc_trade_contract' ) }} contract
		ON dispatchlog.contract_id = contract.id

	LEFT JOIN {{ source ('ovs', 'otc_trade_initialotcdispatchlog') }} initial_dis_log
		ON dispatchlog.initial_dispatch_id = initial_dis_log.id
	
	LEFT JOIN {{ source ('workbench', 'dispatch_physicaldispatch') }} wb_phy_dispatch
		ON initial_dis_log.physical_dispatch_log_id = wb_phy_dispatch.dispatch_log_id
	
	WHERE delivered_volume > 0
	
	),

reverted_dispatches AS
	(
	SELECT contract.contract_id, 
			CONCAT(contract.contract_id, '-', dispatchlog.reverted_from_id, '-reverted') delivery_id,
			dispatchlog.updated dispatch_created_at, initial_dis_log.dispatch_date,
			CASE WHEN ((dispatchlog.updated at time zone 'WAT') :: time) >= '15:00:00'
					THEN DATE((dispatchlog.updated at time zone 'WAT') + INTERVAL '1 day')
			ELSE DATE((dispatchlog.updated at time zone 'WAT'))
			END AS adjusted_dispatch_created_at,

			dispatchlog.updated dispatch_updated_at, 
			
			(dispatchlog.bags * -1) dispatch_bag,
			dispatchlog.is_deleted dispatch_is_deleted, dispatchlog.is_reverted dispatch_is_reverted, dispatchlog.is_buyer_pickup dispatch_is_buyer_pickup, 
			dispatchlog.delivered_volume,

			CASE WHEN (dispatchlog.is_buyer_pickup is false) or (dispatchlog.is_buyer_pickup is true and dispatchlog.is_settlement_confirmed is true ) 
				THEN dispatchlog.delivered_volume
			ELSE null
			END AS actual_delivered_volume,

			dispatchlog.id dispatchlog_pk, dispatchlog.reverted_from_id,

			(initial_dis_log.dispatched_volume * -1) initial_dispatched_volume, 
			(initial_dis_log.bags * -1) dispatched_bags,
			((wb_phy_dispatch.weight/1000) * -1) wb_phy_dispatch_weight,
			(wb_phy_dispatch.bags * -1) wb_phy_dispatch_bags, 
			initial_dis_log.dispatch_id, initial_dis_log.physical_dispatch_log_id, dispatchlog.is_settlement_confirmed,
			initial_dis_log.delivery_company, initial_dis_log.truck_number, initial_dis_log.drivers_name, initial_dis_log.drivers_phone,
			initial_dis_log.status dispatch_status,
			initial_dis_log.warehouse_code, initial_dis_log.warehouse_name, wb_phy_dispatch.tenant_id wb_tenant_id, dispatchlog.initial_dispatch_id

	FROM {{ source ('ovs', 'otc_trade_otcdispatchlog') }} dispatchlog
	
	LEFT JOIN {{ source ( 'ovs', 'otc_trade_contract' ) }} contract
		ON dispatchlog.contract_id = contract.id

	LEFT JOIN {{ source ('ovs', 'otc_trade_initialotcdispatchlog') }} initial_dis_log
		ON dispatchlog.initial_dispatch_id = initial_dis_log.id
	
	LEFT JOIN {{ source ('workbench', 'dispatch_physicaldispatch') }} wb_phy_dispatch
		ON initial_dis_log.physical_dispatch_log_id = wb_phy_dispatch.dispatch_log_id
	
	WHERE delivered_volume < 0

	
		UNION

	
	SELECT contract_id, CONCAT(delivery_id, '-reverted') delivery_id, dispatch_updated_at dispatch_created_at, dispatch_date, 
			
			CASE WHEN ((dispatch_updated_at at time zone 'WAT') :: time) >= '15:00:00'
					THEN DATE((dispatch_updated_at at time zone 'WAT') + INTERVAL '1 day')
			ELSE DATE((dispatch_updated_at at time zone 'WAT'))
			END AS adjusted_dispatch_created_at,
			
			dispatch_updated_at,

			(dispatch_bag * -1) dispatch_bag, 
			dispatch_is_deleted, dispatch_is_reverted, dispatch_is_buyer_pickup,
			(delivered_volume * -1) delivered_volume, 
			(actual_delivered_volume * -1) actual_delivered_volume, 
			dispatchlog_pk, reverted_from_id, 
			(initial_dispatched_volume * -1) initial_dispatched_volume, 
			(dispatched_bags * -1) dispatched_bags,
			(wb_phy_dispatch_weight * -1) wb_phy_dispatch_weight, 
			(wb_phy_dispatch_bags * -1) wb_phy_dispatch_bags, 
			dispatch_id, physical_dispatch_log_id, is_settlement_confirmed,
			delivery_company, truck_number, drivers_name, drivers_phone, dispatch_status, warehouse_code, warehouse_name,
			wb_tenant_id, initial_dispatch_id

	FROM dispatches
	
	WHERE dispatch_is_reverted IS true 
			AND reverted_from_id IS NULL
			AND dispatch_created_at < '2023-12-19' -- This cut-off date was determined through manual research to find the first instance of negative delivered volume in the dispatchlog table.
	
	),

final_dispatches AS 
	(
	SELECT *

	FROM dispatches
	
		UNION 
	
	SELECT *
	
	FROM reverted_dispatches
	)

SELECT *
FROM final_dispatches