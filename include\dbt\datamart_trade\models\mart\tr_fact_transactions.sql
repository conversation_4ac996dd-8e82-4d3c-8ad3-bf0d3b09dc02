WITH calc_vol_per_unit AS
(
SELECT id, tid, created, volume_per_unit, CASE WHEN trade.volume_per_unit = 0 THEN 1
											ELSE trade.volume_per_unit
											END AS calc_volume_per_unit
	
	FROM ovs.trade_clientorderrequest trade 
),

without_creator_info AS
(
SELECT match.order_id, trade.created trade_creation_date, trade.tid, trade.oms_provider_id,
		trade.status trade_status, trade.ovs_validation, 
		CASE WHEN match.order_id IS null
			THEN 'Unmatched'
			ELSE 'Matched' 
			END trade_status_summary,
		trade.order_type, trade.is_on_behalf, trade.is_rejected, trade.is_deleted, trade.is_order_cancelled,
		trade.volume_per_unit, trade2.calc_volume_per_unit, trade.units trade_order_units,
		((trade.units * trade2.calc_volume_per_unit)::float /1000) trade_order_vol_mt,
		trade.total_order_volume trade_order_vol_kg,
		trade.matched_qty matched_units, match.matched_units matchedorder_units, match.invoiced_units,
		((match.matched_units * trade2.calc_volume_per_unit)::float/1000) matchedorder_vol_mt,
		(match.matched_units * trade2.calc_volume_per_unit) matchedorder_vol_kg,
		trade.cancelled_units, currency.name currency, currency.code currency_code, currency.sign currency_sign,
		trade.order_price, trade.order_base_price,
		trade.complete_charged_fees, trade.fee_per_unit,
		trade.use_vat, trade.use_ecn_fees,
		trade.fees_breakdown brokerage_fees_breakdown, trade.brokerage_fee brokerage_fee_percent,
		match.cm_fee, match.sec_fee, trade.discount_type, trade.discount, match.vat_value,
		match.exchange_fee, match.total_oms_fees,
		match.total_afex_fees, match.trade_value, match.trade_value_with_fee, match.calculated_trade_value,
		match.otc_bags_delivered, match.otc_volume_delivered, match.otc_calculated_trade_value_delivered,
		trade.deduction_type, trade.deduction,
		sec.security_code, sec.security_name, sec.sec_security_type, sec.security_type,
		trade.board_type, sec.board_type_sec, trade.security_location_code,
		client.created client_creation_date, client.cid client_cid,
		CONCAT(client.first_name, ' ', client.last_name) client_fullname,
		client.account_type client_account_type, client.user_account_type client_user_account_type,
		client.email client_email, client.phone client_phone, client.country client_country,
		client.country_code client_country_code, client.is_kyc_complete client_is_kyc_complete,
		comxtrade.brokerage_community_id, bro_com.code broker_community_code, bro_com.name broker_community_name, 
		bro_com.city broker_community_city, bro_com.state broker_community_state,
		bro_com.is_viewable broker_community_is_viewable, bro_com.is_approved broker_community_is_approved, 
		bro_com.created broker_community_creation_date, bro_com.owner_id broker_community_owner_id,
		bro_info.cid brocom_owner_cid, CONCAT(bro_info.first_name, ' ', bro_info.last_name) brocom_owner_fullname, 
		bro_info.account_type brocom_owner_account_type, bro_info.user_account_type brocom_owner_user_account_type,
		bro_info.email brocom_owner_email, bro_info.phone brocom_owner_phone, bro_info.country brocom_owner_country,
		bro_info.country_code brocom_owner_country_code, bro_info.is_kyc_complete brocom_owner_is_kyc_complete,
		bro_info.is_afex_broker brocom_owner_is_afex_broker,
		comxtrade.promoter_community_id, pro_com.code promoter_community_code, pro_com.name promoter_community_name, 
		pro_com.city promoter_community_city, pro_com.state promoter_community_state,
		pro_com.is_viewable promoter_community_is_viewable, pro_com.is_approved promoter_community_is_approved, 
		pro_com.created promoter_community_creation_date, pro_com.owner_id promoter_community_owner_id,
		pro_info.cid procom_owner_cid, CONCAT(pro_info.first_name, ' ', pro_info.last_name) procom_owner_fullname, 
		pro_info.account_type procom_owner_account_type, pro_info.user_account_type procom_owner_user_account_type,
		pro_info.email procom_owner_email, pro_info.phone procom_owner_phone, pro_info.country procom_owner_country,
		pro_info.country_code procom_owner_country_code, pro_info.is_kyc_complete procom_owner_is_kyc_complete,
		pro_info.is_afex_broker procom_owner_is_afex_broker,
		CASE WHEN trade.is_on_behalf = false
			THEN 'Account Owner'
			ELSE 'Broker'
			END trade_creator_type,	
		CASE WHEN trade.is_on_behalf = false
			THEN client.cid
			ELSE bro_info.cid
			END creator_cid
	
	FROM ovs.trade_clientorderrequest trade
	
	LEFT JOIN calc_vol_per_unit trade2
	ON trade.id = trade2.id
	
	LEFT JOIN ovs.ecn_currency currency
	ON trade.currency_id = currency.id
	
	LEFT JOIN (SELECT order_id, SUM(cm_fee) cm_fee, SUM(sec_fee) sec_fee,
			   			SUM(discount) discount, SUM(vat_value) vat_value,
						SUM(exchange_fee) exchange_fee, SUM(matched_units) matched_units,
			   			SUM(invoiced_units) invoiced_units, SUM(total_oms_fees) total_oms_fees,
			   			SUM(total_afex_fees) total_afex_fees, SUM(total_order_price) trade_value,
			   			SUM(total_order_price_with_fees) trade_value_with_fee, 
			   			SUM(matched_price * matched_units) calculated_trade_value,
						SUM(bags) otc_bags_delivered, SUM(delivered_volume) otc_volume_delivered,
						SUM(matched_price * delivered_volume) otc_calculated_trade_value_delivered
			   
					FROM ( SELECT *
							  FROM ovs.trade_matchedorder match_2
							  LEFT JOIN ovs.otc_trade_contract contract
							  ON match_2.id = contract.buy_match_order_id

							  LEFT JOIN (SELECT contract_id, SUM(bags) bags, SUM(delivered_volume) delivered_volume
											 FROM ovs.otc_trade_otcdispatchlog
											 GROUP BY contract_id) dispatchlog
						  
								ON contract.id = dispatchlog.contract_id ) match_1
			   		GROUP BY order_id) match
	
	ON trade.id = match.order_id
	
	LEFT JOIN ( SELECT sec.id, sec.code security_code, sec.name security_name,
			   			sec.security_type sec_security_type, board.name board_type_sec,
						CASE WHEN board.name = 'SCIT'
									OR board.name = 'Virtual'
								THEN board.name

							WHEN sec.code LIKE 'ABCP%'
			   						OR sec.code LIKE '%AF%'
									OR sec.code LIKE '%AI%'
									OR sec.code LIKE '%ATF%'
									OR sec.code LIKE 'C%'
									OR sec.code LIKE '%FETC%'
									OR sec.code LIKE 'NPRL%'
									OR sec.code LIKE 'D%'
								THEN sec.security_type

							WHEN sec.code LIKE 'O%'
									OR sec.code LIKE 'S%'
								THEN board.name

							ELSE null
							END AS security_type
			   		
			   		FROM ovs.crm_security sec
	
					LEFT JOIN ovs.crm_board board
					ON sec.board_id = board.id ) sec
	ON trade.security_id = sec.id
	
	LEFT JOIN ovs.crm_client pseudo_client
	ON trade.client_id = pseudo_client.id
	
	LEFT JOIN comx_mart.dim_client client
	ON pseudo_client.cid = client.cid
	
	LEFT JOIN comx.trading_trade comxtrade
	ON trade.tid = comxtrade.tid
	
	LEFT JOIN comx.crm_community bro_com
	ON comxtrade.brokerage_community_id = bro_com.id
		
	LEFT JOIN comx_mart.dim_client bro_info
	ON bro_com.owner_id = bro_info.id
	
	LEFT JOIN comx.crm_community pro_com
	ON comxtrade.promoter_community_id = pro_com.id
	
	LEFT JOIN comx_mart.dim_client pro_info
	ON pro_com.owner_id = pro_info.id
)

SELECT CASE WHEN woci.security_type = 'OTC' 
						AND woci.order_type = 'Buy'
						AND woci.otc_calculated_trade_value_delivered is not null
					THEN woci.otc_calculated_trade_value_delivered
				ELSE woci.trade_value
				END actual_trade_value, 
			CASE WHEN woci.security_type = 'OTC' 
						AND woci.order_type = 'Buy'
						AND woci.otc_calculated_trade_value_delivered is not null
					THEN woci.otc_volume_delivered
				ELSE woci.matchedorder_vol_mt
				END actual_trade_vol_mt,

				
		woci.*,
						
		CONCAT(creator.first_name, ' ', creator.last_name) creator_fullname, 
		creator.account_type creator_account_type, creator.user_account_type creator_user_account_type,
		creator.email creator_email, creator.phone creator_phone, creator.country creator_country,
		creator.country_code creator_country_code, creator.is_kyc_complete creator_is_kyc_complete,
		creator.is_afex_broker creator_is_afex_broker
	
	FROM without_creator_info woci
	LEFT JOIN comx_mart.dim_client creator
	ON woci.creator_cid = creator.cid
