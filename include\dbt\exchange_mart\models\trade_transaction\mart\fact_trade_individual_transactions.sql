{{ config ( materialized = 'table')}}

WITH 

client_order_request AS
	(
	SELECT CASE WHEN ((security.security_type = 'OTC') AND 
					((dispatch_is_buyer_pickup is false) or (dispatch_is_buyer_pickup is true and is_settlement_confirmed is true)))
				THEN dispatch_created_at
			WHEN security.security_type != 'OTC' THEN mor.created
			ELSE null
                        END AS execution_created_at,

            CASE WHEN ((security.security_type = 'OTC') AND 
					((dispatch_is_buyer_pickup is false) or (dispatch_is_buyer_pickup is true and is_settlement_confirmed is true)))
				THEN  dispatch_updated_at
                        WHEN security.security_type != 'OTC' THEN mor.updated
			ELSE null
                        END AS execution_updated_at,

			cor.created trade_created_at, cor.updated trade_updated_at,
			mor.created deal_created_at,

			CASE WHEN ((mor.created at time zone 'WAT') :: time) >= '15:00:00'
				THEN DATE((mor.created at time zone 'WAT') + INTERVAL '1 day')
			ELSE DATE((mor.created at time zone 'WAT'))
			END AS adjusted_deal_created_at,

			mor.updated deal_updated_at, mor.processed_on deal_processed_at,
			cor.is_on_behalf trade_is_on_behalf, cor.status trade_status, cor.is_order_cancelled, cor.is_rejected trade_is_rejected,
			cor.tid, trans_client.cid trans_cid, cor.order_type, mor.matched_id, mor.sell_tid, mor.buy_tid, mor.seller_cid, mor.buyer_cid,
			cor.volume_per_unit, 
			CASE
				WHEN cor.volume_per_unit = 0 THEN 1
				ELSE cor.volume_per_unit
			END calc_volume_per_unit, --Using this instead because I need the correct value for order request, became important when computing closing price
			-- mor.calc_volume_per_unit,
			case when cor.currency_id is null and security.security_type in ('Spot', 'FI') then 'Naira'
				when cor.currency_id is null and mor.matched_id is null and cor.created < '2021-05-31' then 'Naira'
				when cor.currency_id is null and security.security_type = 'Virtual' and (date(cor.created) between '2021-02-01' and '2021-02-28') then 'Naira'
			else currency.name 
			end currency,
			cor.units order_units, cor.order_price, mor.matched_units, mor.matched_price, cor.cancelled_units,
			commodity.name commodity_name, commodity.code commodity_code,
			CASE WHEN security.security_code LIKE '%CSN%' THEN 'Cashew Nuts'
					WHEN (security.security_code LIKE '%COC%' OR security.security_code LIKE '%CCO%') THEN 'Cocoa'
					WHEN security.security_code LIKE '%GNG%' THEN 'Ginger'
					WHEN security.security_code LIKE '%MAZ%' THEN 'Maize'
					WHEN security.security_code LIKE '%PRL%' THEN 'Paddy Rice'
					WHEN (security.security_code LIKE '%SSM%' OR  security.security_code LIKE '%SSC%') THEN 'Sesame'
					WHEN security.security_code LIKE '%SGM%' THEN 'Sorghum'
					WHEN security.security_code LIKE '%SBS%' THEN 'Soyabean'
					WHEN security.security_code LIKE '%WHT%' THEN 'Wheat'
	-- 				WHEN (security.security_code LIKE '%A%' OR security.security_code LIKE '%FETC%') THEN 'Finance Note'
			ELSE security.security_code
			END consolidated_commodity_name,
			security.board, security.security_type, security.security_name, security.security_code,
			oms.name oms_name, oms.oms_code,
			mor.total_order_price, mor.total_order_price_with_fees, cor.use_ecn_fees, mor.total_afex_fees,
			mor.total_oms_fees, mor.sec_fee, mor.exchange_fee, mor.cm_fee, mor.brokerage_fee,
			mor.vat_value, cor.fees_breakdown, mor.discount, 
			mor.is_contract_note_sent, mor.processed_for_inventory,
			cor.trade_tenure, cor.ovs_validation, cor.is_manually_matched,
			
			contract.created contract_created_at, contract.updated contract_updated_at,		
			contract.price contract_price, contract.tenure contract_tenure, contract.is_deleted contract_is_deleted,
			contract.is_cancelled contract_is_cancelled, contract.delivery_status contract_delivery_status, contract.buy_match_order_id contract_buy_match_order_id,
			cor.security_location_code,
			loc.name security_location, coalesce(loc.state, cor.security_location_code) security_location_state,
			contract.seller_location_code contract_seller_location_code, seller_reg.code seller_region,
			contract.buyer_location_code contract_buyer_location_code, buyer_reg.code buyer_region,
			cor.logistic_differential,

			final_dispatches.*,

			comx_trade.community,
			bro_community.name brokerage_community_name,
			bro_community.code brokerage_community_code,
			pro_community.name promoter_community_name,
			pro_community.code promoter_community_code,
			comx_trade.created_by_id trade_created_by_id



	FROM {{ ref ( 'stg_clientorderrequest' )}} cor
	LEFT JOIN {{ ref ('stg_matchedorder') }} mor
		ON cor.tid = mor.tid

	LEFT JOIN {{source ( 'ovs', 'crm_omsprovider' ) }} oms
		ON cor.oms_provider_id = oms.id
	
	LEFT JOIN {{ source ( 'ovs', 'ecn_currency' ) }} currency
		ON cor.adj_currency_id = currency.id
	
	LEFT JOIN {{ ref ( 'dim_security' ) }} security
		ON cor.security_id = security.id	
	LEFT JOIN {{ source ( 'ovs', 'crm_commodity' ) }} commodity
		ON security.commodity_code = commodity.code
	
	LEFT JOIN {{ source ( 'ovs', 'crm_location' ) }} loc
		ON cor.security_location_code = loc.code

	LEFT JOIN {{ ref ( 'int_trade_contracts' ) }} contract
		ON mor.id = contract.match_order_id

	LEFT JOIN {{ source ('ovs', 'crm_client' ) }} trans_client
		ON cor.client_id = trans_client.id

	LEFT JOIN {{ source ('workbench', 'location_state' ) }} buyer_sta
		ON contract.buyer_location_code = buyer_sta.name
	LEFT JOIN {{ source ('workbench', 'location_region' ) }} buyer_reg
		ON buyer_sta.region_id = buyer_reg.id

	LEFT JOIN {{ source ('workbench', 'location_state' ) }} seller_sta
		ON contract.seller_location_code = seller_sta.name
	LEFT JOIN {{ source ('workbench', 'location_region' ) }} seller_reg
		ON seller_sta.region_id = seller_reg.id

	LEFT JOIN {{ ref ( 'int_trade_dispatches' ) }} final_dispatches
		ON contract.contract_id = final_dispatches.contract_id

	LEFT JOIN {{ source ( 'comx', 'trading_trade' ) }}  comx_trade
		ON cor.tid = comx_trade.tid
	LEFT JOIN {{ source ( 'comx', 'crm_community' ) }}  bro_community
		ON comx_trade.brokerage_community_id =  bro_community.id
	LEFT JOIN  {{ source ( 'comx', 'crm_community' ) }}  pro_community
		ON comx_trade.promoter_community_id =  pro_community.id
		),

execution_date_and_volume AS (
	SELECT CASE WHEN ((security_type = 'OTC') AND 
					((dispatch_is_buyer_pickup is false) or (dispatch_is_buyer_pickup is true and is_settlement_confirmed is true)))
				THEN adjusted_dispatch_created_at
			WHEN security_type != 'OTC' THEN adjusted_deal_created_at
                        ELSE null
                        END AS execution_date,

			CASE WHEN security_type = 'OTC' THEN round((actual_delivered_volume * 1000), 2)
			ELSE round((matched_units * calc_volume_per_unit), 2)
			END AS "executed_volume_kg",

			round((matched_price / calc_volume_per_unit), 2) matched_price_kg,
	
			client_order_request.*

			-- CASE WHEN contract_id IS null THEN 0
			-- 		ELSE DENSE_RANK() OVER(PARTITION BY contract_id ORDER BY dispatch_created_at) 
			-- 		END dispatch_index

	FROM client_order_request
	),
	
deal_identifiers as( 
	SELECT CASE WHEN (execution_date is not null) and (security_type = 'OTC') THEN delivery_id
					WHEN (execution_date is not null) and (security_type != 'OTC') THEN matched_id
			ELSE null
			END AS execution_id, -- tags otc trades per delivery

			CASE WHEN (execution_date is not null) and (security_type = 'OTC') THEN CONCAT(contract_id, '-', execution_date)
					WHEN (execution_date is not null) and (security_type != 'OTC') THEN matched_id
			ELSE null
			END AS deal_id, -- tags otc trades per day

			round(matched_price_kg * executed_volume_kg, 4) AS execution_turnover,

			execution_date_and_volume.* 
	
	FROM execution_date_and_volume
	)
-- Update Final OTC to include the list of required columns only, as should be. Let's be very guided here.	
-- , final as()

SELECT 
	execution_id, -- tags otc trades per delivery
	deal_id, -- tags otc trades per day
	execution_turnover,
	execution_date,
	executed_volume_kg,
	matched_price_kg,
	execution_created_at,
	execution_updated_at,
	trade_created_at, 
	trade_updated_at,
	deal_created_at,
	adjusted_deal_created_at,
	deal_updated_at, 
	deal_processed_at,
	trade_is_on_behalf, 
	trade_status, 
	is_order_cancelled, 
	trade_is_rejected,
	tid, 
	trans_cid, 
	order_type, 
	matched_id, 
	sell_tid, 
	buy_tid, 
	seller_cid, 
	buyer_cid,
	calc_volume_per_unit volume_per_unit,
	currency,
	order_units, 
	order_price, 
	matched_units, 
	matched_price, 
	cancelled_units,
	commodity_name, 
	commodity_code,
	consolidated_commodity_name,
	board, 
	security_type, 
	security_name, 
	security_code,
	oms_name, 
	oms_code,
	total_order_price, 
	total_order_price_with_fees, 
	use_ecn_fees, 
	total_afex_fees,
	total_oms_fees, 
	sec_fee, 
	exchange_fee, 
	cm_fee, 
	brokerage_fee,
	vat_value, 
	fees_breakdown, 
	discount, 
	is_contract_note_sent, 
	processed_for_inventory,
	trade_tenure, 
	ovs_validation, 
	is_manually_matched,		
	contract_created_at, 
	contract_updated_at,		
	contract_price, 
	contract_tenure, 
	contract_is_deleted,
	contract_is_cancelled, 
	contract_delivery_status, 
	contract_buy_match_order_id,
	security_location_code,
	security_location, 
	security_location_state,
	contract_seller_location_code, 
	seller_region,
	contract_buyer_location_code, 
	buyer_region,
	logistic_differential,

	-- Fron final_dispatches
	contract_id, 
	delivery_id,
	dispatch_created_at, 
	dispatch_date,
	adjusted_dispatch_created_at,	
	dispatch_updated_at, 
	dispatch_bag, 
	dispatch_is_deleted,
	dispatch_is_reverted, 
	dispatch_is_buyer_pickup, 
	delivered_volume,
	actual_delivered_volume,
	dispatchlog_pk, 
	reverted_from_id,
	initial_dispatched_volume, 
	dispatched_bags,
	wb_phy_dispatch_weight, 
	wb_phy_dispatch_bags, 
	dispatch_id, 
	physical_dispatch_log_id, 
	is_settlement_confirmed,
	delivery_company, 
	truck_number, 
	drivers_name, 
	drivers_phone,
	dispatch_status,
	warehouse_code, 
	warehouse_name, 
	wb_tenant_id, 
	initial_dispatch_id

	community,
	brokerage_community_name,
	brokerage_community_code,
	promoter_community_name,
	promoter_community_code,
	trade_created_by_id

FROM 
	deal_identifiers --final
