{{ config(materialized='ephemeral') }}


WITH commodity_details AS (
  SELECT
    trans_cid,
	MAX(trade_created_at) last_traded_time,
    consolidated_commodity_name,

	--- Count Transactions
	COUNT(*) FILTER (WHERE order_type = 'Buy') buy_count,
	COUNT(*) FILTER (WHERE order_type = 'Sell') sell_count,


	-- Executed transaction volumes
    COALESCE(SUM(executed_volume_kg) FILTER (WHERE order_type = 'Buy'),0)  buy_vol,
	COALESCE(SUM(executed_volume_kg) FILTER (WHERE order_type = 'Sell'),0) sell_vol,

	-- Total trade
    COUNT(*) AS total_trade_count,
	COALESCE(SUM(executed_volume_kg),0) AS total_trade_vol_kg
	
	
	
  FROM 
        {{ ref('stg_client_commodity_transactions') }}
	
  GROUP BY trans_cid, consolidated_commodity_name
),

commodity_aggregates AS (
  SELECT
    trans_cid,
    JSON_AGG(
      JSON_BUILD_OBJECT(
        'commodity', consolidated_commodity_name,
       	'buy_count', buy_count,
        'sell_count',  sell_count,
		'buy_vol', buy_vol,
        'sell_vol',  sell_vol
      )
    ) AS commodities
  FROM commodity_details
  GROUP BY trans_cid
),

-- Aggregate securities per client (code, name, count, volume)
security_details AS (
  SELECT
    trans_cid,
	MAX(trade_created_at) last_traded_time,
	security_type,
	-- Count Transactions
	COUNT(*) FILTER (WHERE order_type = 'Buy') buy_count,
	COUNT(*) FILTER (WHERE order_type = 'Sell') sell_count,


	-- Executed transaction volumes
	
    COALESCE(SUM(executed_volume_kg) FILTER (WHERE order_type = 'Buy'),0)  buy_vol,
	COALESCE(SUM(executed_volume_kg) FILTER (WHERE order_type = 'Sell'),0) sell_vol,

	-- Total trade
    COUNT(*) AS total_trade_count,
	COALESCE(SUM(executed_volume_kg),0) AS total_trade_vol_kg
	
	
  FROM 
	 {{ ref('stg_client_commodity_transactions') }}
  GROUP BY 
	trans_cid, 
	security_type
),

security_aggregates AS (
  SELECT
    trans_cid,
    JSON_AGG(
      JSON_BUILD_OBJECT(
        'board', security_type,
        'buy_count', buy_count,
        'sell_count',  sell_count,
		'buy_vol', buy_vol,
        'sell_vol',  sell_vol
		-- 'trade_last_at',last_traded_time
      )
    ) AS securities
  FROM security_details
  GROUP BY trans_cid
),

-- Traded commodities aggregate
traded_board_commodity AS (
  SELECT 
    s.trans_cid,
    s.securities boards,
    c.commodities
  FROM  
    security_aggregates s
  INNER JOIN
    commodity_aggregates c
  ON s.trans_cid = c.trans_cid
  
 )

 SELECT * FROM traded_board_commodity 

 