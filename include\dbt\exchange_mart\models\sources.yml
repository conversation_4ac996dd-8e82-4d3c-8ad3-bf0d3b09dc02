version: 2

sources:

  - name: comx
    description: "Source data for the COMX specific exchange activities, accounts and clients"
    tables:
      - name: account_country
      - name: account_survey
      - name: account_surveyquestion
      - name: account_surveyquestionoption
      - name: account_user
      - name: account_usersurveytaker
      - name: administration_activitystream
        description: "Logs all user activity on the exchange platform"
        columns:              
          - name: created
            description: "Timestamp for when that activity happened"
          - name: user_id
            description: "user_id of the user that take the action. It is null when the user did not login"
      - name: crm_board
      - name: crm_client
        description: "Client table from the exchange system. Stores client information"
        columns:
          - name: id
            description: "Primary key for clientuser"
          - name: cid
            description: "Client assigned cid. Unique identify of the client across the entire system"
          - name: last_name
            description: "C<PERSON>'s last name"
          - name: email
            description: "<PERSON><PERSON><PERSON>'s email address"
          - name: user_id
            description: "C<PERSON>'s user_id, references to the account user table"
          - name: email
            description: "Client's email address"
      - name: crm_clientuser
        description: "Links the clients table and the accountuser table"
        columns:              
          - name: id
            description: "Primary key of the table"
          - name: client_id
            description: "Unique foreign key. Maps to the primary key of the client table"
          - name: user_id
            description: "Unique foreign key. Maps to the primary key of the account user table"
      - name: crm_commodity
      - name: crm_community
      - name: crm_currency
      - name: crm_location
      - name: crm_security
      - name: trading_matchorder
      - name: trading_trade


  - name: csd
    tables:
      - name: wallet_clientwalletoperationrequestlog
        description: "Client wallet operation request table from the exchange system. Logs all wallet request operation of client"
        columns:
          - name: id
            description: "Primary key for clientuser"
          - name: client_id
            description: "Client id. Unique identify of the client on the client table. This maps to the id column on the crm_client table"
         
      - name: crm_client
        description: "Client table from the exchange system. Stores client information"
        columns:
          - name: id
            description: "Primary key for clientuser"
          - name: cid
            description: "Client assigned cid. Unique identify of the client across the entire system"
          - name: last_name
            description: "Client's last name"
          - name: email
            description: "Clients's email address"


  - name: ovs
    description: "Source data from the order validation system backend, including market data dataset, that is commodities and securities prices."
    tables:
      - name: crm_board
      - name: crm_client
        description: One record per registered client on the exchange with a system generated identification number
        columns:
          - name: id
            description: This is the table's unique identifier and primary key
            tests:
              - unique
              - not_null
          - name: cid
            description: Client's unique identifier across AFEX HoldCo systems.
            tests:
              - unique
              - not_null
      - name: crm_clientwallet
        description: One record per latest wallet balance, per exchange clients with wallet accounts
        columns:
          - name: id
            description: This is the table's unique identifier and primary key
            tests:
              - unique
              - not_null
          - name: total_balance
            description: ''
            tests:
              - not_null
          - name: available_balance
            description: ''
            tests:
              - not_null
          - name: lien_balance
            description: ''
            tests:
              - not_null
          - name: cash_advance_limit
            description: ''
          - name: cash_advance_spent
            description: ''
          - name: created
            description: This is the creation timestamp
            tests:
              - not_null
          - name: updated
            description: This is the updated timestamp
            tests:
              - not_null
          - name: client_id
            description: ''
            tests:
              - unique
              - not_null
          - name: cash_advance_balance
            description: ''
          - name: is_deleted
            description: ''
            tests:
              - not_null
          - name: agent_id
            description: ''
          - name: currency_id
            description: ''
            tests:
              - not_null
      - name: crm_commodity
      - name: crm_location
      - name: crm_omsprovider
      - name: crm_security
      - name: databank_commodityhistoricalprice
      - name: databank_historicalprice
      - name: ecn_currency
      - name: otc_trade_contract
      - name: otc_trade_initialotcdispatchlog
      - name: otc_trade_otcdispatchlog
      - name: trade_clientorderrequest
        description: One records per exchange clients intention to buy or sell. Trade step before matching.
      - name: trade_matchedorder
        description: One record per matched client order request records. Completed orders for non-OTC transactions


  - name: workbench
    description: "Source data for AFTL's activities, accounts and clients"
    tables:
      - name: dispatch_physicaldispatch
      - name: location_region
      - name: location_state


  - name: afex_gen_mart
    description: "Modeled dataset for AFEX activities that are not limited to a particular entity"
    tables:
      - name: dim_date
      - name: dim_logistics_grid
      - name: national_transaction_volume
      

  - name: exchange_mart
    description: "Modeled dataset for ACEL's (aka Exchange) activities"
    tables:
      - name: historical_aci_aei_upto_2024_02_23
      - name: historical_aci_upto_2024_11_03
      - name: historical_aei_upto_2024_11_03
      - name: historical_commoditiesprices_upto_2024_02_23
      - name: national_transaction_volume


  - name: trade_mart
    description: "Modeled dataset for AFTL's (aka Trade) activities"
    tables:
      - name: dim_warehouse
      - name: fact_grn
