{{ config (materialized = 'ephemeral') }}

with 
ranked_prices as (
    select *
            , row_number() over(partition by date, commodity_code order by date, source) row_num
    
    from {{ ref ('ovs_stg_commodities_closing_prices')}}
    ),

closing_prices as (
    select *

    from ranked_prices

    where row_num = 1
    ),

non_null_prices as (
	select 
		dim_date.date_actual date, distinct_comm.commodity_code,
		distinct_comm.commodity_name,
		case when (commodity_closing_price is null and dim_date.date_actual = current_date)
				then closing_prices.commodity_closing_price
			when commodity_closing_price is null 
				then (lag(closing_prices.commodity_closing_price, 1) over (partition by distinct_comm.commodity_code order by dim_date.date_actual))
			else commodity_closing_price
			end as commodity_closing_price,
		closing_prices.source

	from {{source ('exchange_mart', 'dim_date') }} dim_date -- use dbt's date_dim
	cross join (select distinct commodity_code, commodity_name from closing_prices) distinct_comm
	left join closing_prices
		on date(dim_date.date_actual) = date(closing_prices.date)
		and distinct_comm.commodity_code = closing_prices.commodity_code	

	where dim_date.date_actual <= current_date
	)

select *
from non_null_prices
order by date desc, commodity_code