{{ config(materialized='table') }}

with d as (
    select * from {{source('workbench','workbench_warehouse')}}
),
tt as (
    select * from {{source('workbench','tenant_tenant')}}
),
k as (
    select * from {{source('workbench','location_location')}}
),
l as (
    select * from {{source('workbench','location_baselocation')}}
),
m as (
    select * from {{source('workbench','location_state')}}
),
o as (
    select * from {{source('workbench','location_region')}}
),
c as (
    select * from {{source('workbench','location_country')}}
),

final as (
    select  d.id,d.created,d.updated,  k.id location_id,tt.id tenant_id,tt.company_name tenant_name,d.name warehouse_name,d.code warehouse_code,d.capacity,d.warehouse_manager_id,d.address,d.longitude,d.latitude,d.warehouse_email,
l.name "location",m.name state,m.capital capital,
o.code region_code,o.name region_name,c.name country,c.capital country_capital,c.region continent,c.subregion continent_subregion
from d 
inner join  tt on d.tenant_id = tt.id
LEFT JOIN  k ON d.location_id = k.id 
LEFT JOIN  l ON k.base_location_id = l.id 
LEFT JOIN  m ON l.state_id = m.id 
LEFT JOIN  o ON m.region_id = o.id 
LEFT JOIN  c on m.country_id = c.id
)

select * from final
where  tenant_id in (241,216,64,7)
