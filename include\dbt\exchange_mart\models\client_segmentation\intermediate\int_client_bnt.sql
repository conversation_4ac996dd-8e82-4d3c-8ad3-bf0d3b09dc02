{{ config(materialized='ephemeral') }}


-- Step 2: Calculate the first and last activity time for each user, per day, per hour (time band)
WITH first_last_activity_times AS (
SELECT
    cid,
    activity_type,
    activity_date,
    activity_hour,
    MIN(activity_time) AS first_activity_time,   -- First appearance in that hour on that day
    MAX(activity_time) AS last_activity_time -- Last appearance in that hour on that day

FROM {{ ref('stg_activity_with_time_band') }} -- activity_with_time_band 
GROUP BY 
    cid, 
    activity_type, 
    activity_date, 
    activity_hour

),
-- Compute the activity of a user and their online duration for each hour each day
activity_streams AS (
     SELECT 
         cid,
         activity_type,
         activity_date,
         activity_hour,
         first_activity_time,
         last_activity_time,
         (last_activity_time - first_activity_time) online_duration
 
     FROM first_last_activity_times
     ORDER BY cid, activity_date, activity_hour
),

activities AS (   
    SELECT 
        cid,
        TO_TIMESTAMP(eas.activity_hour::text, 'HH24')::TIME activity_hour,
        activity_type,
        AVG(online_duration) avg_online_duration_within_hour, -- On average, how long did they spend within this hour?  
        COUNT(activity_hour) AS activity_hour_frequency -- How many times does the user's activity fall within this hour?

    FROM activity_streams eas

    GROUP BY 
        cid, 
        eas.activity_type, 
        eas.activity_hour
),

ranked_hours AS (
    -- Step 4: Rank hours based on frequency for each user
    SELECT 
        cid,
        activity_hour,
        activity_hour_frequency,
        avg_online_duration_within_hour,
        ROW_NUMBER() OVER (PARTITION BY cid ORDER BY activity_hour_frequency DESC, avg_online_duration_within_hour DESC) AS rank
    
    FROM activities
),

-- Step 5: Calculate the overall most active hour across all users
overall_most_active_hour AS ( -- This will serve as default best notification hour for clients without any activity
    
    SELECT 
        activity_hour most_frequent_activity_hour,
        COUNT(*) AS frequency
    FROM activities
    
    WHERE activity_hour IS NOT NULL
    GROUP BY activity_hour
    ORDER BY activity_hour DESC
    LIMIT 1
),

-- Final Step: Extract clients and their bnt, clients without bnt are assigned the most frequent hour of all the clients
client_bnt as (
     SELECT 
         r.cid,
         COALESCE(r.activity_hour, o.most_frequent_activity_hour) best_notification_hour,
         COALESCE(r.activity_hour_frequency, 0) activity_hour_frequency,
         COALESCE(r.avg_online_duration_within_hour, '0 Seconds'::interval) avg_online_duration_within_hour
  
     FROM ranked_hours r
     CROSS JOIN overall_most_active_hour o
     WHERE r.rank = 1
 )

-- Final Select Statement
SELECT * FROM client_bnt

