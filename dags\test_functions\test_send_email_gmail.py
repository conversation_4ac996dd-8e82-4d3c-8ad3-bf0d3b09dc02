import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from dotenv import load_dotenv
load_dotenv()
import os

def send_email(sender_email, sender_password, recipients, subject, message_body, is_html=True):
    # Set up the SMTP server
    smtp_server = "smtp.gmail.com"
    smtp_port = 587  # For TLS

    # Create the email message
    msg = MIMEMultipart()
    msg['From'] = sender_email
    msg['To'] = ", ".join(recipients)
    msg['Subject'] = subject

    # Attach the message body
    msg.attach(MIMEText(message_body, 'html' if is_html else 'plain'))

    with smtplib.SMTP(smtp_server, smtp_port) as server:
        try:
            # Establish a connection to the server
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()  # Enable TLS encryption
            server.login(sender_email, sender_password)  # Login to your email account

            # Send the email
            server.sendmail(sender_email, recipients, msg.as_string())
            print("Email sent successfully!")

        except Exception as e:
            print(f"Error: {e}")

        finally:
            server.quit()  