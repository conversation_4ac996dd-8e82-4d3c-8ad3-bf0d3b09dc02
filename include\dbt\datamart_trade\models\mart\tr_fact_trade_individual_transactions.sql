{{ config ( materialized = 'table')}}

WITH 
dispatches AS
	(
	SELECT contract.contract_id, CONCAT(contract.contract_id, '-', dispatchlog.id) delivery_id,
			dispatchlog.created dispatch_created_at, initial_dis_log.dispatch_date,
			CASE WHEN (CAST((dispatchlog.created at time zone 'WAT') :: timestamp as time)) > '15:00:00'
					THEN DATE(dispatchlog.created + INTERVAL '1 day')
			ELSE DATE(dispatchlog.created)
			END AS adjusted_dispatch_created_at,

			dispatchlog.updated dispatch_updated_at, dispatchlog.bags dispatch_bag, dispatchlog.is_deleted dispatch_is_deleted,
			dispatchlog.is_reverted dispatch_is_reverted, dispatchlog.is_buyer_pickup dispatch_is_buyer_pickup, 
			dispatchlog.delivered_volume,

			CASE WHEN (dispatchlog.is_buyer_pickup is false) or (dispatchlog.is_buyer_pickup is true and dispatchlog.is_settlement_confirmed is true ) 
				THEN dispatchlog.delivered_volume
			ELSE null
			END AS actual_delivered_volume,

			dispatchlog.id dispatchlog_pk, dispatchlog.reverted_from_id,
			initial_dis_log.dispatched_volume initial_dispatched_volume, initial_dis_log.bags dispatched_bags,
			wb_phy_dispatch.weight/1000 wb_phy_dispatch_weight, wb_phy_dispatch.bags wb_phy_dispatch_bags, 
			initial_dis_log.dispatch_id, initial_dis_log.physical_dispatch_log_id, dispatchlog.is_settlement_confirmed,
			initial_dis_log.delivery_company, initial_dis_log.truck_number, initial_dis_log.drivers_name, initial_dis_log.drivers_phone,
			initial_dis_log.status dispatch_status,
			initial_dis_log.warehouse_code, initial_dis_log.warehouse_name, wb_phy_dispatch.tenant_id wb_tenant_id, dispatchlog.initial_dispatch_id

	FROM {{ source ('ovs', 'otc_trade_otcdispatchlog') }} dispatchlog
	
	LEFT JOIN {{ source ( 'ovs', 'otc_trade_contract' ) }} contract
		ON dispatchlog.contract_id = contract.id

	LEFT JOIN {{ source ('ovs', 'otc_trade_initialotcdispatchlog') }} initial_dis_log
		ON dispatchlog.initial_dispatch_id = initial_dis_log.id
	
	LEFT JOIN {{ source ('workbench', 'dispatch_physicaldispatch') }} wb_phy_dispatch
		ON initial_dis_log.physical_dispatch_log_id = wb_phy_dispatch.dispatch_log_id
	
	WHERE delivered_volume > 0
	
	),

reverted_dispatches AS
	(
	SELECT contract.contract_id, 
			CONCAT(contract.contract_id, '-', dispatchlog.reverted_from_id, '-reverted') delivery_id,
			dispatchlog.updated dispatch_created_at, initial_dis_log.dispatch_date,
			CASE WHEN (CAST((dispatchlog.updated at time zone 'WAT') :: timestamp as time)) > '15:00:00'
					THEN DATE(dispatchlog.updated + INTERVAL '1 day')
			ELSE DATE(dispatchlog.updated)
			END AS adjusted_dispatch_created_at,

			dispatchlog.updated dispatch_updated_at, 
			
			(dispatchlog.bags * -1) dispatch_bag,
			dispatchlog.is_deleted dispatch_is_deleted, dispatchlog.is_reverted dispatch_is_reverted, dispatchlog.is_buyer_pickup dispatch_is_buyer_pickup, 
			dispatchlog.delivered_volume,

			CASE WHEN (dispatchlog.is_buyer_pickup is false) or (dispatchlog.is_buyer_pickup is true and dispatchlog.is_settlement_confirmed is true ) 
				THEN dispatchlog.delivered_volume
			ELSE null
			END AS actual_delivered_volume,

			dispatchlog.id dispatchlog_pk, dispatchlog.reverted_from_id,

			(initial_dis_log.dispatched_volume * -1) initial_dispatched_volume, 
			(initial_dis_log.bags * -1) dispatched_bags,
			((wb_phy_dispatch.weight/1000) * -1) wb_phy_dispatch_weight,
			(wb_phy_dispatch.bags * -1) wb_phy_dispatch_bags, 
			initial_dis_log.dispatch_id, initial_dis_log.physical_dispatch_log_id, dispatchlog.is_settlement_confirmed,
			initial_dis_log.delivery_company, initial_dis_log.truck_number, initial_dis_log.drivers_name, initial_dis_log.drivers_phone,
			initial_dis_log.status dispatch_status,
			initial_dis_log.warehouse_code, initial_dis_log.warehouse_name, wb_phy_dispatch.tenant_id wb_tenant_id, dispatchlog.initial_dispatch_id

	FROM {{ source ('ovs', 'otc_trade_otcdispatchlog') }} dispatchlog
	
	LEFT JOIN {{ source ( 'ovs', 'otc_trade_contract' ) }} contract
		ON dispatchlog.contract_id = contract.id

	LEFT JOIN {{ source ('ovs', 'otc_trade_initialotcdispatchlog') }} initial_dis_log
		ON dispatchlog.initial_dispatch_id = initial_dis_log.id
	
	LEFT JOIN {{ source ('workbench', 'dispatch_physicaldispatch') }} wb_phy_dispatch
		ON initial_dis_log.physical_dispatch_log_id = wb_phy_dispatch.dispatch_log_id
	
	WHERE delivered_volume < 0

	
		UNION

	
	SELECT contract_id, CONCAT(delivery_id, '-reverted') delivery_id, dispatch_updated_at dispatch_created_at, dispatch_date, 
			
			CASE WHEN (CAST((dispatch_updated_at at time zone 'WAT') :: timestamp as time)) > '15:00:00'
					THEN DATE(dispatch_updated_at + INTERVAL '1 day')
			ELSE DATE(dispatch_updated_at)
			END AS adjusted_dispatch_created_at,
			
			dispatch_updated_at,

			(dispatch_bag * -1) dispatch_bag, 
			dispatch_is_deleted, dispatch_is_reverted, dispatch_is_buyer_pickup,
			(delivered_volume * -1) delivered_volume, 
			(actual_delivered_volume * -1) actual_delivered_volume, 
			dispatchlog_pk, reverted_from_id, 
			(initial_dispatched_volume * -1) initial_dispatched_volume, 
			(dispatched_bags * -1) dispatched_bags,
			(wb_phy_dispatch_weight * -1) wb_phy_dispatch_weight, 
			(wb_phy_dispatch_bags * -1) wb_phy_dispatch_bags, 
			dispatch_id, physical_dispatch_log_id, is_settlement_confirmed,
			delivery_company, truck_number, drivers_name, drivers_phone, dispatch_status, warehouse_code, warehouse_name,
			wb_tenant_id, initial_dispatch_id

	FROM dispatches
	
	WHERE dispatch_is_reverted IS true 
			AND reverted_from_id IS NULL
			AND dispatch_created_at < '2023-12-19' -- This cut-off date was determined through manual research to find the first instance of negative delivered volume in the dispatchlog table.
	
	),

final_dispatches AS 
	(
	SELECT *

	FROM dispatches
	
		UNION 
	
	SELECT *
	
	FROM reverted_dispatches
	),

client_order_request AS
	(
	SELECT CASE WHEN ((fact_match.security_type = 'OTC') AND 
					((dispatch_is_buyer_pickup is false) or (dispatch_is_buyer_pickup is true and is_settlement_confirmed is true)))
				THEN dispatch_created_at
			WHEN fact_match.security_type != 'OTC' THEN fact_match.created
			ELSE null
                        END AS execution_created_at,

                        CASE WHEN ((fact_match.security_type = 'OTC') AND 
					((dispatch_is_buyer_pickup is false) or (dispatch_is_buyer_pickup is true and is_settlement_confirmed is true)))
				THEN  dispatch_updated_at
                        WHEN fact_match.security_type != 'OTC' THEN fact_match.updated
			ELSE null
                        END AS execution_updated_at,

			cor.created trade_created_at, cor.updated trade_updated_at,
			fact_match.created deal_created_at,

			CASE WHEN (CAST((fact_match.created at time zone 'WAT') :: timestamp as time)) > '15:00:00'
					THEN DATE(fact_match.created + INTERVAL '1 day')
			ELSE DATE(fact_match.created)
			END AS adjusted_deal_created_at,

			fact_match.updated deal_updated_at, fact_match.processed_on deal_processed_at,
			fact_match.is_on_behalf trade_is_on_behalf, cor.status trade_status, cor.is_order_cancelled, cor.is_rejected trade_is_rejected,
			cor.tid, cor.order_type, fact_match.matched_id, fact_match.sell_tid, fact_match.buy_tid, fact_match.seller_cid, fact_match.buyer_cid,
			fact_match.volume_per_unit, fact_match.calc_volume_per_unit, currency.name currency,
			cor.units order_units, cor.order_price, fact_match.matched_units, fact_match.matched_price, cor.cancelled_units,
			commodity.name commodity,
			CASE WHEN fact_match.security_code LIKE '%CSN%' THEN 'Cashew Nuts'
					WHEN (fact_match.security_code LIKE '%COC%' OR fact_match.security_code LIKE '%CCO%') THEN 'Cocoa'
					WHEN fact_match.security_code LIKE '%GNG%' THEN 'Ginger'
					WHEN fact_match.security_code LIKE '%MAZ%' THEN 'Maize'
					WHEN fact_match.security_code LIKE '%PRL%' THEN 'Paddy Rice'
					WHEN (fact_match.security_code LIKE '%SSM%' OR  fact_match.security_code LIKE '%SSC%') THEN 'Sesame'
					WHEN fact_match.security_code LIKE '%SGM%' THEN 'Sorghum'
					WHEN fact_match.security_code LIKE '%SBS%' THEN 'Soyabean'
					WHEN fact_match.security_code LIKE '%WHT%' THEN 'Wheat'
	-- 				WHEN (fact_match.security_code LIKE '%A%' OR fact_match.security_code LIKE '%FETC%') THEN 'Finance Note'
			ELSE fact_match.security_code
			END consolidated_commodity_name,
			fact_match.board_type, fact_match.security_type, fact_match.security_name, fact_match.security_code,
			fact_match.oms_name, fact_match.oms_code,
			fact_match.total_order_price, fact_match.total_order_price_with_fees, cor.use_ecn_fees, fact_match.total_afex_fees,
			fact_match.total_oms_fees, fact_match.sec_fee, fact_match.exchange_fee, fact_match.cm_fee, fact_match.brokerage_fee,
			fact_match.vat_value, cor.fees_breakdown, fact_match.discount, 
			fact_match.is_contract_note_sent, fact_match.processed_for_inventory,
			cor.trade_tenure, cor.ovs_validation, cor.is_manually_matched,
			
			contract.created contract_created_at, contract.updated contract_updated_at,		
			contract.price contract_price, contract.tenure contract_tenure, contract.is_deleted contract_is_deleted,
			contract.is_cancelled contract_is_cancelled, contract.delivery_status contract_delivery_status, contract.buy_match_order_id contract_buy_match_order_id,
			loc.name security_location, loc.state security_location_state,
			contract.seller_location_code contract_seller_location_code, seller_reg.code seller_region,
			contract.buyer_location_code contract_buyer_location_code, buyer_reg.code buyer_region,
			cor.logistic_differential,

			final_dispatches.*



	FROM {{ source ( 'ovs', 'trade_clientorderrequest' )}} cor
	LEFT JOIN {{ ref ( 'tr_fact_matchedorder' )}} fact_match
		ON cor.tid = fact_match.tid
	
	LEFT JOIN {{ source ( 'ovs', 'ecn_currency' ) }} currency
		ON cor.currency_id = currency.id
	
	LEFT JOIN {{ source ( 'ovs', 'crm_security' ) }} security
		ON fact_match.security_code = security.code	
	LEFT JOIN {{ source ( 'ovs', 'crm_commodity' ) }} commodity
		ON security.commodity_id = commodity.id
	
	LEFT JOIN {{ source ( 'comx', 'crm_location' ) }} loc
		ON fact_match.security_location_code = loc.code

	LEFT JOIN {{ source ( 'ovs', 'otc_trade_contract' ) }} contract
		ON fact_match.id = contract.buy_match_order_id

	LEFT JOIN {{ source ('workbench', 'location_state' ) }} buyer_sta
		ON contract.buyer_location_code = buyer_sta.name
	LEFT JOIN {{ source ('workbench', 'location_region' ) }} buyer_reg
		ON buyer_sta.region_id = buyer_reg.id

	LEFT JOIN {{ source ('workbench', 'location_state' ) }} seller_sta
		ON contract.seller_location_code = seller_sta.name
	LEFT JOIN {{ source ('workbench', 'location_region' ) }} seller_reg
		ON seller_sta.region_id = seller_reg.id

	LEFT JOIN final_dispatches
		ON contract.contract_id = final_dispatches.contract_id
		),

executed_date_and_volume AS (
	SELECT CASE WHEN ((security_type = 'OTC') AND 
					((dispatch_is_buyer_pickup is false) or (dispatch_is_buyer_pickup is true and is_settlement_confirmed is true)))
				THEN adjusted_dispatch_created_at
			WHEN security_type != 'OTC' THEN adjusted_deal_created_at
                        ELSE null
                        END AS executed_date,

			CASE WHEN security_type = 'OTC' THEN (actual_delivered_volume * 1000)
			ELSE (matched_units * calc_volume_per_unit)
			END AS "executed_volume_kg",
	
			client_order_request.*

			-- CASE WHEN contract_id IS null THEN 0
			-- 		ELSE DENSE_RANK() OVER(PARTITION BY contract_id ORDER BY dispatch_created_at) 
			-- 		END dispatch_index

	FROM client_order_request
	),
	
deal_identifiers as( 
	SELECT CASE WHEN (executed_date is not null) and (security_type = 'OTC') THEN delivery_id
					WHEN (executed_date is not null) and (security_type != 'OTC') THEN matched_id
			ELSE null
			END AS execution_id, -- tags otc trades per delivery

			CASE WHEN (executed_date is not null) and (security_type = 'OTC') THEN CONCAT(contract_id, '-', executed_date)
					WHEN (executed_date is not null) and (security_type != 'OTC') THEN matched_id
			ELSE null
			END AS deal_id, -- tags otc trades per day

			executed_date_and_volume.* 
	
	FROM executed_date_and_volume
	)
-- Update Final OTC to include the list of required columns only, as should be. Let's be very guided here.	
-- , final as()

SELECT *		
FROM deal_identifiers 
WHERE executed_date is not null --final
