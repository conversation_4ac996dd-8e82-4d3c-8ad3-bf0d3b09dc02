 {% macro test_custom_expect_column_values_to_be_greater_than(model, column_name, value, row_condition=None) %}
 with validation as (
     select
         {{ column_name }} as column_value
     from {{ model }}
     {% if row_condition %}
     where {{ column_name }} <= {{ value }} and {{ row_condition }}
     {% else %}
     where {{ column_name }} <= {{ value }}
     {% endif %}
 )
 select
     count(*) as num_errors
 from validation
 {% endmacro %}