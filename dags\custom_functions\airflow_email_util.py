"""
Name: Email notification utility functions for airflow
Author: <PERSON>

"""
import os
from airflow.utils.email import send_email_smtp
from dotenv import load_dotenv, find_dotenv
load_dotenv(find_dotenv())

def failure_email(context):
    subject = "[Airflow] DAG {0} Failed".format(context['task_instance_key_str'])
    html_content = """
    DAG: {0}<br>
    Task: {1}<br>
    Failed at: {2}
    """.format(context['task_instance'].dag_id, context['task_instance'].task_id, context['ts'])
    send_email_smtp(to=['<EMAIL>', '<EMAIL>'], subject=subject , html_content=html_content)


def warning_email(context):
    task_id = context['task_instance'].task_id
    dag_id = context['task_instance'].dag_id
    ct = context['ts']

    subject = "[Airflow] DAG Task Warning: {} - Missing or Imcomplete Closing Price data detected".format(task_id)
    html_content = f"""
    <p><strong>DAG:</strong> {dag_id}</p>
    <p><strong>Task:</strong> {task_id}</p>
    <p><strong>Occurred at:</strong> {ct}</p>
    """
    recipient_emails = ["<EMAIL>", "<EMAIL>"]

    send_email_smtp(to=recipient_emails, subject=subject , html_content=html_content)