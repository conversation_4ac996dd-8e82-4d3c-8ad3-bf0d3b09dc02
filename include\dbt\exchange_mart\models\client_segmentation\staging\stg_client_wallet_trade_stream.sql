{{ config(materialized='ephemeral') }}


-- Get all trading activities -Clients and their trading features
WITH trading_clients AS (
	SELECT 
			tt.trade_created_at action_date,
			tt.trans_cid cid, 
			tt.order_type action_type,
			tt.consolidated_commodity_name commodity_name,
			tt.board,
			tt.security_type,
			tt.security_name
	FROM 
		{{ ref('fact_trade_individual_transactions') }} tt
	
),


-- Get all wallet funding and withdrawal actions of each clients
wallet_operation AS (
	SELECT 
		wo.created action_date,
		cl.cid,
		transaction_type action_type
	FROM
		{{ source('csd', 'wallet_clientwalletoperationrequestlog')}} wo 
	LEFT JOIN
		{{ source('csd', 'crm_client') }} cl
	ON
		wo.client_id = cl.id
	WHERE transaction_type in ('Credit', 'Withdrawal') -- This actions can only be done by client


),

-- Combine both results as all client trade and wallet actions on the system
client_actions AS (
	SELECT 
		action_date,
		cid,
		action_type
	FROM trading_clients
	
	UNION ALL
	
	SELECT 
		action_date,
			cid,
			action_type
	FROM wallet_operation
)
SELECT * FROM client_actions