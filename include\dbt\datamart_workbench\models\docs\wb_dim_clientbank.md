{% docs wb_dim_clientbank_id %}
Unique identifier of the client bank record.
{% enddocs %}

{% docs wb_dim_clientbank_bvn %}
Bank Verification Number associated with the client bank account.
{% enddocs %}

{% docs wb_dim_clientbank_account_number %}
Account number of the client bank account.
{% enddocs %}

{% docs wb_dim_clientbank_bank_code %}
Code representing the bank associated with the client bank account.
{% enddocs %}

{% docs wb_dim_clientbank_bank_name %}
Name of the bank associated with the client bank account.
{% enddocs %}

{% docs wb_dim_clientbank_bank_country %}
Country where the bank associated with the client bank account is located.
{% enddocs %}

{% docs wb_dim_clientbank_created %}
Timestamp indicating when the client bank record was created.
{% enddocs %}

{% docs wb_dim_clientbank_updated %}
Timestamp indicating the last update time for this client bank record.
{% enddocs %}

{% docs wb_dim_clientbank_client_id %}
Unique identifier of the client associated with the bank account (reference to workbench_mart.dim_client.id).
{% enddocs %}

{% docs wb_dim_clientbank_tenant_id %}
Unique identifier of the tenant associated with the client bank account.
{% enddocs %}

{% docs wb_dim_clientbank_preferred %}
A flag indicating whether the client bank account is the preferred account.
{% enddocs %}

{% docs wb_dim_clientbank_is_deleted %}
A flag indicating whether the client bank record is marked as deleted.
{% enddocs %}

{% docs wb_dim_clientbank_is_approved %}
A flag indicating whether the client bank account is approved.
{% enddocs %}

{% docs wb_dim_clientbank_is_rejected %}
A flag indicating whether the client bank account is rejected.
{% enddocs %}

{% docs wb_dim_clientbank_is_reverted %}
A flag indicating whether the client bank account status is reverted.
{% enddocs %}

{% docs wb_dim_clientbank_is_verified %}
A flag indicating whether the client bank account is verified.
{% enddocs %}

{% docs wb_dim_clientbank_is_bank_deleted %}
A flag indicating whether the associated bank is deleted.
{% enddocs %}

{% docs wb_dim_clientbank_is_nominated %}
A flag indicating whether the client bank account is nominated.
{% enddocs %}

{% docs wb_dim_clientbank_approval_date %}
Timestamp indicating when the client bank account was approved.
{% enddocs %}

{% docs wb_dim_clientbank_approval_done %}
Indicates the approval status of the client bank account.
{% enddocs %}

{% docs wb_dim_clientbank_created_by_id %}
Unique identifier of the user who created the client bank record.
{% enddocs %}

{% docs wb_dim_clientbank_next_approval %}
The next approval status for the client bank account.
{% enddocs %}

{% docs wb_dim_clientbank_rejected_date %}
Timestamp indicating when the client bank account was rejected.
{% enddocs %}

{% docs wb_dim_clientbank_revert_reason %}
The reason for reverting the status of the client bank account.
{% enddocs %}

{% docs wb_dim_clientbank_rejected_by_id %}
Unique identifier of the user who rejected the client bank account.
{% enddocs %}

{% docs wb_dim_clientbank_created_offline %}
Timestamp indicating when the client bank record was created offline.
{% enddocs %}

{% docs wb_dim_clientbank_rejection_reason %}
The reason for rejecting the client bank account.
{% enddocs %}
