import requests
import pandas as pd
import os
from urllib.parse import quote_plus
# from weather_utils import fetch_warehouse_locations, get_forecast, config_params, delete_existing_forecast, push_to_db
import openmeteo_requests
import requests_cache
import pandas as pd
import numpy as np
import os
from urllib.parse import quote_plus
from retry_requests import retry
from sqlalchemy import create_engine, text
import os
from sqlalchemy import text, bindparam
from dotenv import load_dotenv, find_dotenv

load_dotenv(find_dotenv())
from credentials import  db_conn

# Setup the Open-Meteo API client with cache and retry on error
cache_session = requests_cache.CachedSession('.cache', expire_after = 3600)
retry_session = retry(cache_session, retries = 5, backoff_factor = 0.2)
openmeteo = openmeteo_requests.Client(session = retry_session)

# Make sure all required weather variables are listed here
# The order of variables in hourly or daily is important to assign them correctly below
url = os.environ['WEATHER_API_URL']

def config_params(lat, long, start, end, include_fields=None, exclude_fields=None):
    fields = ["weather_code", "temperature_2m_max", "temperature_2m_min", "apparent_temperature_max", "apparent_temperature_min", "sunrise", "sunset", "daylight_duration", "sunshine_duration", "uv_index_max", "uv_index_clear_sky_max"]
    return {
        "latitude": lat,
        "longitude": long,
        "daily": fields,
        "start_date": start,
        "end_date": end

    }, fields


config_target = 'MODELED_TARGET_DB'
engine, conn =  db_conn(conn_param=config_target)

def fetch_warehouse_locations():
    conn = None
    try:
        with engine.connect() as conn:
            query = text("""
                        SELECT 
                            warehouse_name,
                            id warehouse_id,
                            tenant_id,
                            long longitude, 
                            lat latitude
                        FROM 
                            workbench_mart.geo_dim_warehouse
                        
                        --WHERE 
                        --    lat is not null
                        """)
            cur =  conn.execute(query)

            df = pd.DataFrame(cur.fetchall())
        return df
    except Exception as e:
        print(f"Error fetching warehouse locations: {e}")
    finally:
        if conn:
            conn.close()
    
def get_forecast(params, fields):
    # if window > 0:
    #     end = 
    if params['latitude'] is None or pd.isna(params['latitude']):
        weather_data = {field:np.array([np.nan]* 8) for field in fields}

    else:
        responses = openmeteo.weather_api(url, params=params)

        # Process first location. Add a for-loop for multiple locations or weather models
        response = responses[0]
        # Process daily data. The order of variables needs to be the same as requested.
        daily = response.Daily()
        weather_data = {field:daily.Variables(i).ValuesAsNumpy() for i, field in enumerate(fields)}

    daily_data = {
        "weather_date": pd.date_range(
        start=params['start_date'],  # Start date
        end=params['end_date'],      # End date
        freq='1D',     # Frequency (1 day)
        inclusive="both"   # Exclude the end date (if needed), use "both" if inclusive
    )
    }

    daily_data.update(weather_data)

    daily_dataframe = pd.DataFrame(data = daily_data)
    #print(daily_dataframe)
    return daily_dataframe

def push_to_db(data, table='fact_warehouse_weather_forecasts'):
    with engine.connect() as conn:
        data.to_sql(table, con=conn, schema='workbench_mart', 
                    if_exists='replace', index=False)
        print("loaded")
    return True

def delete_existing_forecast(warehouse_ids, forecast_date):

    with engine.connect() as conn:
        for id in warehouse_ids:
            delete_query = text("""
                DELETE FROM workbench_mart.fact_warehouse_weather_forecasts
                WHERE warehouse_id = :id AND weather_date >= :start_date;
                """)

            conn.execute(delete_query, {"id":id, 
                                            "start_date":forecast_date})
        print("Deleted")
    return True


def process_forecast_data(warehouse_locations):
    weather_df = pd.DataFrame()
    wh_iter = warehouse_locations.iterrows()
    for i, row in wh_iter:
        lat = row['latitude']
        long = row['longitude']
        start = pd.Timestamp.now().strftime("%Y-%m-%d")
        end = (pd.Timestamp.now() + pd.Timedelta(days=7)).strftime("%Y-%m-%d")
        
        params, fields = config_params(lat=lat, long=long, start=start, end=end)
        weather_data =  get_forecast(params=params, fields=fields)
        dim_data = pd.DataFrame({col:[val]*len(weather_data) 
                    for col, val in zip(row.index, row.values)})
        # print(dim_data)
        dim_weather_data = pd.concat([pd.DataFrame(dim_data), weather_data], axis=1)
        weather_df =  pd.concat([weather_df, dim_weather_data])

    return weather_df, warehouse_locations['warehouse_id'].tolist(), start


def main():
    warehouse_locations = fetch_warehouse_locations()
    print(warehouse_locations)
    if warehouse_locations is not None and not warehouse_locations.empty:
        forecast_data, wh_ids, start = process_forecast_data(warehouse_locations)
        forecast_data.to_csv('warehouse_weather_forecast.csv', index=False)
        _ = delete_existing_forecast(tuple(wh_ids), start)
        _ = push_to_db(forecast_data)
        
if __name__ == "__main__":
    main()
