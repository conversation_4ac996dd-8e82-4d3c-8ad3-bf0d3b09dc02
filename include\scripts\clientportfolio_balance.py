import configparser
import ast
from datetime import datetime, date, timedelta
import pandas as pd
from sqlalchemy import text
import os
from credentials import  db_conn


dir_path = os.path.dirname(os.path.realpath('__file__'))

config = configparser.ConfigParser()
config.read(f"{dir_path}/config.ini")

config_source = 'MODELED_SOURCE_DB'
config_target = 'MODELED_TARGET_DB'

# connection
source_engine,conn_source =  db_conn(conn_param=config_source)
#target_engine,conn_target =  db_conn(conn_param=config_source)


# Define the query to check for existing records for today's date
check_query = """
SELECT COUNT(*) as count
FROM exchange_mart.fact_clientdailyportfoliobalance
WHERE DATE(portfolio_date) = (CURRENT_DATE - 1) """


# Execute the check query
existing_count = pd.read_sql_query(check_query, source_engine).iloc[0]['count']


# If there are existing records for today, delete them
if existing_count > 0:
    delete_query = """
    DELETE FROM exchange_mart.fact_clientdailyportfoliobalance
    WHERE DATE(portfolio_date) = (CURRENT_DATE - 1)
    """
    with source_engine.connect() as connection:
        connection.execute(text(delete_query))
        

# Get the latest portfolio balance for all clients
latest_balance = """
with base_table as (
	select logg.*, coalesce(location_id, 0) as adj_location_id_,
			sec.security_name, sec.security_code, sec.security_type
				
	from ovs.operation_portfoliologging logg
	left join ovs_mart.dim_security sec
		on logg.security_id = sec.id

	where not (sec.security_type = 'Dawa' and logg.location_id is null )
	),

coalesce_fi_dummy_location as (
	select *, case when security_type = 'FI' or (security_type = 'Spot' and security_code not like 'S%') then 152
				else adj_location_id_
				end adj_location_id
	
	from base_table	
	),

adjusted_location_id as (
	select logg.client_id id_index, DATE(logg.created) portfolio_date,
			logg.*,	client.cid, 
			loc.code location_code, loc.name location_name, loc.state location_state
			
			
	from coalesce_fi_dummy_location logg
	left join ovs.crm_client client
		on logg.client_id = client.id
	left join ovs.crm_location loc
		on logg.adj_location_id = loc.id
	
	),
	
indexed_table as (
	select row_number() over (partition by id_index, security_id, adj_location_id order by created desc) daily_index,*
	
	from adjusted_location_id 
	)

select portfolio_date, cid, security_name, security_code, security_type, location_code, location_name, 
		location_state, lien_units_after latest_lien_units, 
		available_units_after latest_available_units, total_units_after latest_total_units,
		created latest_trans_at
from indexed_table
where daily_index = 1
order by portfolio_date desc 
"""
cur =  conn_source.execute(text(latest_balance))
res = cur.fetchall()
# hot_data = pd.read_sql(latest_balance,source_engine) # Formatted the Query as text and fetched the result as query text object. The assumption is that pandas is not able to parse the query directly, probably because of the wildcard content.
hot_data = pd.DataFrame(res)

fin_data = hot_data[['portfolio_date', 'cid', 'security_name', 'security_code', 
                     'security_type', 'location_code', 'location_name', 
                     'location_state', 'latest_lien_units', 'latest_available_units', 
                     'latest_total_units', 'latest_trans_at']]

# change the latest portfolio_date to the current date
yesterday = (date.today()) - timedelta(days=1)
fin_data.portfolio_date = yesterday.strftime("%Y-%m-%d")
print('\n\n\ntoday_date :',yesterday)

fin_data.to_sql('fact_clientdailyportfoliobalance',source_engine, schema= 'exchange_mart', index=False, if_exists="append")