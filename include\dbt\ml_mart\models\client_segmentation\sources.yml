version: 2

sources:
  - name: trade_mart
    tables:
    - name: fact_transactions
      description: 'One record per completed trade on the exchange'
      columns:
        - name: tid
          tests:
            - unique
            - not_null
    
    - name: fact_clientwallet
      description: 'One record per exchange client wallet balance'
      columns:
        - name: id
          tests:
            - unique
            - not_null

  - name: comx_mart
    tables:
    - name: fact_collateralmgtloan
    - name: dim_client

  - name: comx
    tables:
    - name: crm_wallettransactionhistory
    - name: crm_individualclient
    - name: crm_corporateclient
    - name: account_lga
    - name: account_state

  - name: ml_mart
    tables:
    - name: corp_client_seg_result
      description: 'One record per corporate client segment label. Result of segmentation analysis'
      columns:
        - name: cid
          tests:
            - unique
            - not_null
    - name: ind_client_seg_result
      description: 'One record per individual client segment label. Result of segmentation analysis'
      columns:
        - name: cid
          tests:
            - unique
            - not_null