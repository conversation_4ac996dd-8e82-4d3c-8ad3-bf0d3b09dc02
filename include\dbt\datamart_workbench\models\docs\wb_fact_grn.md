{% docs wb_fact_grn_cid %}
The unique identifier of the GRN.
{% enddocs %}

{% docs wb_fact_grn_bags %}
The number of bags in the GRN.
{% enddocs %}

{% docs wb_fact_grn_grade %}
The grade associated with the GRN (1, 2, 3).
{% enddocs %}

{% docs wb_fact_grn_grn_id %}
The unique identifier of the GRN.
{% enddocs %}

{% docs wb_fact_grn_receipt_id %}
The unique identifier of the receipt associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_created %}
Timestamp indicating when the GRN was created.
{% enddocs %}

{% docs wb_fact_grn_updated %}
Timestamp indicating the last update time for this GRN.
{% enddocs %}

{% docs wb_fact_grn_gross_weight %}
The gross weight of the GRN.
{% enddocs %}

{% docs wb_fact_grn_net_weight %}
The net weight of the GRN.
{% enddocs %}

{% docs wb_fact_grn_deduction %}
The deduction amount in the GRN.
{% enddocs %}

{% docs wb_fact_grn_total_deduction %}
The total deduction amount in the GRN.
{% enddocs %}

{% docs wb_fact_grn_moisture %}
The moisture percentage in the GRN.
{% enddocs %}

{% docs wb_fact_grn_total_commodity_price %}
The total commodity price in the GRN.
{% enddocs %}

{% docs wb_fact_grn_price_per_tonne %}
The price per tonne in the GRN.
{% enddocs %}

{% docs wb_fact_grn_transaction_type %}
The type of transaction associated with the GRN ('Broker Payment', 'Loan Repayment', 'Trade', 'Warehouse Receipt', 'Storage', 'Storage To Trade', 'Com To Input', 'Com For Equity').
{% enddocs %}

{% docs wb_fact_grn_approval_permissions %}
Permissions required for approval of the GRN.
{% enddocs %}

{% docs wb_fact_grn_approval_done %}
Approval status of the GRN.
{% enddocs %}

{% docs wb_fact_grn_is_approved %}
A flag indicating whether the GRN is approved.
{% enddocs %}

{% docs wb_fact_grn_is_approval_completed %}
A flag indicating whether the approval process for the GRN is completed.
{% enddocs %}

{% docs wb_fact_grn_approval_date %}
Timestamp indicating when the GRN was approved.
{% enddocs %}

{% docs wb_fact_grn_is_received_at_warehouse %}
A flag indicating whether the GRN is received at the warehouse.
{% enddocs %}

{% docs wb_fact_grn_is_reverted %}
A flag indicating whether the GRN is reverted.
{% enddocs %}

{% docs wb_fact_grn_rejection_reason %}
The reason for the rejection of the GRN.
{% enddocs %}

{% docs wb_fact_grn_total_payable_price %}
The total payable price in the GRN.
{% enddocs %}

{% docs wb_fact_grn_transaction_fees %}
The transaction fees associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_is_processed %}
A flag indicating whether the GRN is processed.
{% enddocs %}

{% docs wb_fact_grn_is_disabled_for_listing %}
A flag indicating whether the GRN is disabled for listing.
{% enddocs %}

{% docs wb_fact_grn_spot_payment %}
Spot payment amount in the GRN.
{% enddocs %}

{% docs wb_fact_grn_employee_id %}
The unique identifier of the employee associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_cash_advance_account_pk %}
The primary key of the cash advance account associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_item_name %}
The name of the item associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_item_code %}
The code representing the item associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_item_type %}
The type of the item associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_warehouse_code %}
The code representing the warehouse associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_is_deleted %}
A flag indicating whether the GRN is marked as deleted.
{% enddocs %}

{% docs wb_fact_grn_next_approval %}
The next approval status of the GRN.
{% enddocs %}

{% docs wb_fact_grn_is_rejected %}
A flag indicating whether the GRN is rejected.
{% enddocs %}

{% docs wb_fact_grn_rejected_date %}
Timestamp indicating when the GRN was rejected.
{% enddocs %}

{% docs wb_fact_grn_created_offline %}
Timestamp indicating when the GRN was created offline.
{% enddocs %}

{% docs wb_fact_grn_is_traded %}
A flag indicating whether the GRN is traded.
{% enddocs %}

{% docs wb_fact_grn_raised_for_farmer %}
A flag indicating whether the GRN was raised for a farmer.
{% enddocs %}

{% docs wb_fact_grn_cash_advance_account_paid %}
A flag indicating whether the cash advance account associated with the GRN is paid.
{% enddocs %}

{% docs wb_fact_grn_truck_no %}
The truck number associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_created_by_id %}
The unique identifier of the user who created the GRN.
{% enddocs %}

{% docs wb_fact_grn_goods_receipt_id %}
The unique identifier of the goods receipt associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_rejected_by_id %}
The unique identifier of the user who rejected the GRN.
{% enddocs %}

{% docs wb_fact_grn_cash_advance_account_id %}
The unique identifier of the cash advance account associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_additional_fees %}
Additional fees associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_wm_updated %}
A flag indicating whether the warehouse was updated for the GRN.
{% enddocs %}

{% docs wb_fact_grn_is_clean %}
A flag indicating whether the GRN is clean.
{% enddocs %}

{% docs wb_fact_grn_revert_reason %}
The reason for reverting the GRN.
{% enddocs %}

{% docs wb_fact_grn_is_accounting_posted %}
A flag indicating whether the GRN is posted for accounting.
{% enddocs %}

{% docs wb_fact_grn_tenant_id %}
The unique identifier of the tenant associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_is_uploaded %}
A flag indicating whether the GRN is uploaded.
{% enddocs %}

{% docs wb_fact_grn_certified %}
A flag indicating whether the GRN is certified.
{% enddocs %}

{% docs wb_fact_grn_is_edited %}
A flag indicating whether the GRN is edited.
{% enddocs %}

{% docs wb_fact_grn_payment_option %}
The payment option associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_preferred_bank_account_id %}
The unique identifier of the preferred bank account associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_discount %}
The discount amount associated with the GRN.
{% enddocs %}

{% docs wb_fact_grn_source %}
The source of the GRN.
{% enddocs %}
