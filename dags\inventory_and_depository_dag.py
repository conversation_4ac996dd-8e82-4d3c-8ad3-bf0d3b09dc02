from datetime import datetime,timedelta
from airflow import DAG
from airflow.operators.bash  import <PERSON><PERSON><PERSON>perator
from custom_functions.airflow_email_util import failure_email

PATH_TO_SCRIPT_FOLDER = "/usr/local/airflow/include/scripts"

default_args  = {
     'owner' : 'Oluwatomisin Soetan',
     'retries' : 0,
     'retry_delay' : timedelta(minutes=2),
     'on_failure_callback': failure_email,
}

with DAG(
    dag_id = 'inventory_and_depository',
    default_args = default_args,
    description =  'Updates the inventory and depository tables for warehouses and clients, with daily balances from the previous day',
    start_date =  datetime(2023, 5, 7),
    catchup=False,
    schedule_interval =  '20 2 * * *' ### this uses utc time
) as dag:
    
    task1 =  BashOperator(
        task_id = 'warehouse_daily_inventory_balance',
        bash_command = 'python3 warehouseinventory_balance.py' , 
        cwd=PATH_TO_SCRIPT_FOLDER,
    )

    task2 =  BashOperator(
        task_id = 'client_daily_inventory_balance',
        bash_command = 'python3 clientinventory_balance.py' , 
        cwd=PATH_TO_SCRIPT_FOLDER,
    )

    task3 =  BashOperator(
        task_id = 'client_daily_depository_balance',
        bash_command = 'python3 clientdepository_balance.py' , 
        cwd=PATH_TO_SCRIPT_FOLDER,
    )


task1 >> task2 >> task3
