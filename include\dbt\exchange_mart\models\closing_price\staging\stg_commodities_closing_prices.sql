{{ config (materialized = 'ephemeral') }}

with
price as (
	-- To retrieve the commodities' closing prices
	-- The upper part of the union includes closing prices from an excel sheet that doesn't live in the Database.
	-- Pre June 15, 2023 data only were utilized because the on-system closing price data starts on 15/06/2023
	-- Lower part is the computed closing price from the exchange.

	
	select date(date)
			, case when commodity = 'Sorghum' then 'SGM'
					when commodity = 'Soybean' then 'SBS'
					when commodity = 'Paddy Rice' then 'PRL'
					when commodity = 'Maize' then 'MAZ'
					when commodity = 'Cocoa' then 'CCO'
					when commodity = 'Ginger' then 'GNG'
				else commodity
				end commodity_code
			, case when commodity = 'Sorghum' then 'Sorghum'
					when commodity = 'Soybean' then 'Soybean'
					when commodity = 'Paddy Rice' then 'Paddy Rice Long Grain'
					when commodity = 'Maize' then 'Maize Feed Grade - White'
					when commodity = 'Cocoa' then 'Cocoa'
					when commodity = 'Ginger' then 'Ginger Dried Split'
				else commodity
				end commodity_name
			, (price * 10)/1000 commodity_closing_price -- original price is by bag
			, 'old' source
	
	from {{ source ('exchange_mart', 'historical_commoditiesprices_upto_2024_02_23') }}
	
	
	UNION
	
	
	select price.date
			-- , case when price.commodity_code = 'SSC' then 'SSM'
			-- 	else price.commodity_code
			-- 	end commodity_code
			-- , case when comm.name = 'Sesame Seed Cleaned' then 'Sesame Seed'
			-- 	else comm.name 
			-- 	end commodity_name -- This should be deleted if the dependent dataset turn out well (Tomisin)
			, price.commodity_code
			, comm.name commodity_name
			, price.price commodity_closing_price
			, 'new' source

	from {{ source ('ovs', 'databank_commodityhistoricalprice') }} price
	left join {{ source ('ovs', 'crm_commodity') }} comm
		on price.commodity_code = comm.code

	where price.price != 0
	),

ranked_prices as (
    select *
            , row_number() over(partition by date, commodity_code order by date, source) row_num
    
    from price
    ),

deduplicated_prices as (
    select *

    from ranked_prices

    where row_num = 1
    ),

non_null_day_prices as (
	select dim_date.date_actual date,
		dim_date.day_of_month, dim_date.day_of_quarter, dim_date.day_of_year,
		distinct_comm.commodity_code,
		distinct_comm.commodity_name,
		case when (commodity_closing_price is null and dim_date.date_actual = current_date)
				then deduplicated_prices.commodity_closing_price
			when commodity_closing_price is null 
				then (lag(deduplicated_prices.commodity_closing_price, 1) over (partition by distinct_comm.commodity_code order by dim_date.date_actual))
			else commodity_closing_price
			end as commodity_closing_price,
		deduplicated_prices.source

	from {{source ('afex_gen_mart', 'dim_date') }} dim_date -- use dbt's date_dim
	cross join (select distinct commodity_code, commodity_name from deduplicated_prices) distinct_comm
	left join deduplicated_prices
		on date(dim_date.date_actual) = date(deduplicated_prices.date)
		and distinct_comm.commodity_code = deduplicated_prices.commodity_code	

	where dim_date.date_actual <= current_date
	)

select *
from non_null_day_prices
order by date desc, commodity_code