import configparser
import ast
from datetime import datetime, date, timedelta
import pandas as pd
import os
from credentials import  db_conn
from datetime import datetime
import psycopg2


dir_path = os.path.dirname(os.path.realpath('__file__'))

config = configparser.ConfigParser()
config.read(f"{dir_path}/config.ini")

config_source = 'MODELED_SOURCE_DB'

# connection
source_engine,conn_source =  db_conn(conn_param=config_source)
#target_engine,conn_target =  db_conn(conn_param=config_target)


# Define the query to check for existing records for today's date
check_query = """
SELECT COUNT(*) as count
FROM trade_mart.fact_client_dailyinventorybalance
WHERE DATE(inventory_date) = (CURRENT_DATE - 1)
"""


# Execute the check query
try:
	existing_count = pd.read_sql_query(check_query, source_engine).iloc[0]['count']
except:
      
      psycopg2.errors.UndefinedTable
      existing_count = 0



# If there are existing records for today, delete them
if existing_count > 0:
    delete_query = """
    DELETE FROM trade_mart.fact_client_dailyinventorybalance
    WHERE DATE(inventory_date) = (CURRENT_DATE - 1)
    """
    with source_engine.connect() as connection:
        connection.execute(delete_query)
        

# Get the latest portfolio balance for all clients
latest_balance = """
with base_table as (
	select client.cid, acc.warehouse_id,
				wh.code as warehouse_code,
				wh.name as warehouse_name,
				acc.item_id,
				prod_item.name as item_name,
                prod_item.code as item_code,
				prod_item.product_type as item_type,
				DATE(trans.created) inventory_date,
				trans.*
	from workbench.inventory_clientinventorytransaction trans
	left join workbench.inventory_clientinventoryaccount acc
	on trans.client_inventory_account_id = acc.id
	left join workbench.crm_client client
	on acc.client_id = client.id
	left join workbench.workbench_warehouse wh
	on acc.warehouse_id = wh.id
	left join trade_mart.dim_item prod_item
	on acc.item_id = prod_item.id
	),
	
indexed_table as (
	select row_number() over (partition by client_inventory_account_id order by created desc) daily_index,*
	from base_table )

select inventory_date, cid, warehouse_code, warehouse_name, 
		item_code, item_name, item_type, tenant_id, 
		lien_weight_after as latest_lien_weight,
		net_weight_after as latest_available_net_weight,
		total_weight_after as latest_total_weight,
        created latest_trans_at, client_inventory_account_id
	
from indexed_table
where daily_index = 1
order by created desc
"""

hot_data = pd.read_sql_query(latest_balance,source_engine)

fin_data = hot_data[['inventory_date', 'cid', 'warehouse_code', 'warehouse_name', 
						'item_code', 'item_name', 'item_type', 'tenant_id', 
						'latest_lien_weight', 'latest_available_net_weight',
                        'latest_total_weight',
                        'latest_trans_at', 'client_inventory_account_id']]

# change the latest inventory_date to the current date
yesterday = (date.today()) - timedelta(days=1)
fin_data.inventory_date = yesterday.strftime("%Y-%m-%d")
print('\n\n\ntoday_date :',yesterday)

fin_data.to_sql('fact_client_dailyinventorybalance',source_engine, schema= 'trade_mart', index=False, if_exists="append")