version: 

models:
  - name: fact_prices_commodities_prices
    description: "Daily closing prices of commodities on the exchange"
    columns:
      - name: date
        data_type: Date
        description: "Trading date"
        tests:
          - not_null

      - name: dow
        data_type: integer
        description: "Day of the week (0-6)"
        tests:
          - not_null

      - name: woy
        data_type: text
        description: "Week of the year. Example 2025_8"
        tests:
          - not_null

      - name: quarter
        data_type: text
        description: "Quarter of the year. Example 2025_Q1"
        tests:
          - not_null

      - name: commodity_code
        data_type: text
        description: "Unique commodity identifier"

      - name: commodity_name
        data_type: text
        description: "Commodity name"

      - name: closing_price_kg
        data_type: double precision
        description: "Closing price of the commodity per kilogram"
        tests:
          - custom_expect_column_values_to_be_greater_than:
              value: 0
              severity: error
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              severity: warn

      - name: opening_price_kg
        data_type: double precision
        description: "Opening price of the commodity per kilogram"
        tests:
          - custom_expect_column_values_to_be_greater_than:
              value: 0
              severity: error
              
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: max_price_kg
        data_type: double precision
        description: "Maximum price per kilogram. This is the Maximum price that security sold for on that day. When no transaction, this is the same as closing price." 
        tests:
          - custom_expect_column_values_to_be_greater_than:
              value: 0
              # severity: error
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: min_price_kg
        data_type: double precision
        decription: "Minimum price per kilogram. This is the Minimum price that security sold for on that day. When no transaction, this is the same as closing price."
        tests:
          - custom_expect_column_values_to_be_greater_than:
              value: 0
              # severity: error

          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: dod_price_dif
        data_type: double precision
        description: "Day-over-day closing price difference"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
                column_type: double precision
                config:
                  severity: warn
        

      - name: dod_price_change 
        data_type: double precision
        description: "Day-over-day percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: week_start_price 
        data_type: double precision
        description: "Opening price of the week. Week starts 15:00:00 Monday"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: week_end_price 
        data_type: double precision
        description: "Closing price of the week. Week ends 14:59:59 Friday"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: wtd_price_change
        data_type: double precision
        description: "Week-to-date percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: previous_week_price
        data_type: double precision
        description: "Closing price of the previous week"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: wow_price_change
        data_type: double precision
        description:  "Week-over-week percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: month_start_price
        data_type: double precision
        description: "Opening price on first day of month"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: month_end_price
        data_type: double precision
        description: "Closing price on last day of month"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: mtd_price_change 
        data_type: double precision
        description: "Month-to-date percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: previous_month_price
        data_type: double precision
        description: "Closing price of the previous month"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: mom_price_change
        data_type: double precision
        description: "Month-over-month percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: previous_month_end_price
        data_type: double precision
        description: "Closing price of previous month"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: month_end_price_change
        data_type: double precision
        description:  "Month-end to month-end percentage change. This is the price change between this month end the previous month end"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: quarter_start_price
        data_type: double precision
        description: "Opening price of current quarter"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: quarter_end_price
        data_type: double precision
        description: "Closing price of current quarter"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: qtd_price_change
        data_type: double precision
        description: "Quarter-to-date percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: previous_quarter_price
        data_type: double precision
        description: "Closing price of previous quarter"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: qoq_price_change 
        data_type: double precision
        description: "Quarter-over-quarter percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: previous_quarter_end_price
        data_type: double precision
        description: "Closing price of previous quarter"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: quarter_end_price_change
        data_type: double precision
        description: "Quarter-end to quarter-end percentage change. Price change between this quarter end the previous quarter end"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: year_start_price
        data_type: double precision
        description: "Opening price of the year"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: ytd_price_change
        data_type: double precision
        description: "Year-to-date percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

  - name: fact_prices_securities_prices
    description: "Daily closing prices of securities on the exchange"
    columns:
      - name: date
        data_type: Date
        description: "Trading date"
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              config:
                severity: warn  # Logs the failing rows but does not stop execution
      - name: dow
        data_type: integer
        description: "Day of the week (0-6)"
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              config:
                severity: warn  # Logs the failing rows but does not stop execution

      - name: woy
        data_type: text
        description: "Week of the year. Example 2025_8"
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              config:
                severity: warn  # Logs the failing rows but does not stop execution

      - name: season
        data_type: text
        description: "Season of the year"
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              config:
                severity: warn  # Logs the failing rows but does not stop execution

      - name: quarter
        data_type: text
        description: "Quarter of the year. Example 2025_Q1"
        tests:
          - dbt_expectations.expect_column_values_to_not_be_null:
              config:
                severity: warn  # Logs the failing rows but does not stop execution

      - name: security_code
        data_type: text
        description: "Unique security identifier"
      

      - name: security_name
        data_type: text
        description: "Security name"
        

      - name: security_type
        data_type: text
        description: "Security type. This typically the board type of the security ('OTC', 'Spot')"
        

      - name: closing_price_kg
        data_type: double precision
        description: "Closing price of the commodity per kilogram"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn
          - custom_expect_column_values_to_be_greater_than:
              value: 0.00
              row_condition: "date > '2024-12-31'" # The row condition was added to capture zero values from 2025 January. Since the pipeline is not an incremental load we want to ignore revious values that has been zeros
              # severity: error

      - name: closing_price_per_unit
        data_type: double precision
        description: "Closing price of the commodity per unit"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn
          - custom_expect_column_values_to_be_greater_than:
              value: 0.00
              row_condition: "date > '2024-12-31'" # The row condition was added to capture zero values from 2025 January. Since the pipeline is not an incremental load we want to ignore revious values that has been zeros
              # severity: error

      - name: opening_price_kg
        data_type: double precision
        description: "Opening price of the commodity per kilogram"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn
          - custom_expect_column_values_to_be_greater_than:
              value: 0
              row_condition: "date > '2024-12-31'" # The row condition was added to capture zero values from 2025 January. Since the pipeline is not an incremental load we want to ignore revious values that has been zeros
              # severity: error

      - name: opening_price_per_unit
        data_type: double precision
        description: "Opening price of the commodity per unit"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn
          - custom_expect_column_values_to_be_greater_than:
              value: 0
              row_condition: "date > '2024-12-31'" # The row condition was added to capture zero values from 2025 January. Since the pipeline is not an incremental load we want to ignore revious values that has been zeros
              # severity: error

      - name: max_price_kg
        data_type: double precision
        description: "Maximum price per kilogram. This is the Maximum price that security sold for on that day. When no transaction, this is the same as closing price."  # overall maximum or per day ?
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: max_price_per_unit
        data_type: double precision
        description: "Maximum price per unit"  # overall maximum or per day ?
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: min_price_kg
        data_type: double precision
        decription: "Minimum price per kilogram. This is the Minimum price that security sold for on that day. When no transaction, this is the same as closing price."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: min_price_per_unit
        data_type: double precision
        decription: "Minimum price per unit"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: dod_price_dif
        data_type: double precision
        description: "Day-over-day closing price difference"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: dod_price_change 
        data_type: double precision
        description: "Day-over-day percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: week_start_price 
        data_type: double precision
        description: "Opening price of the week."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: week_end_price 
        data_type: double precision
        description: "Closing price of the week. Week ends 14:59:59 Friday"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: wtd_price_change
        data_type: double precision
        description: "Week-to-date percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: previous_week_price
        data_type: double precision
        description: "Closing price of the previous week"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: wow_price_change
        data_type: double precision
        description:  "Week-over-week percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: month_start_price
        data_type: double precision
        description: "Opening price on first day of month"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: month_end_price
        data_type: double precision
        description: "Closing price on last day of month"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: mtd_price_change 
        data_type: double precision
        description: "Month-to-date percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: previous_month_price
        data_type: double precision
        description: "Closing price of the previous month"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: mom_price_change
        data_type: double precision
        description: "Month-over-month percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: previous_month_end_price
        data_type: double precision
        description: "Closing price of previous month"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: month_end_price_change
        data_type: double precision
        description:  "Month-end to month-end percentage change. That is the price change between this month end the previous month end."
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: quarter_start_price
        data_type: double precision
        description: "Opening price of current quarter"
        tests:
          - not_null

      - name: quarter_end_price
        data_type: double precision
        description: "Closing price of current quarter"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: qtd_price_change
        data_type: double precision
        description: "Quarter-to-date percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: previous_quarter_price
        data_type: double precision
        description: "Closing price of previous quarter"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: qoq_price_change 
        data_type: double precision
        description: "Quarter-over-quarter percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: previous_quarter_end_price
        data_type: double precision
        description: "Closing price of previous quarter"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: quarter_end_price_change
        data_type: double precision
        description: "Quarter-end to quarter-end percentage change. This is the price change between this quarter end the previous quarter end"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: season_start_price
        data_type: double precision
        description: "Opening price of the season"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: std_price_change
        data_type: double precision
        description: "Season-to-date percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: year_start_price
        data_type: double precision
        description: "Opening price of the year"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn

      - name: ytd_price_change
        data_type: double precision
        description: "Year-to-date percentage change"
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: double precision
              config:
                severity: warn