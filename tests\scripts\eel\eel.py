"""
Description: EEL (Extract Embed Load) Script to load datawarehouse metatada into vectore databases for sherlock consumption
Author: <PERSON>
Created: 13 March, 2025

"""
import os
from connect import create_engine_to_source_datawarehouse, get_vector_database_collection
from utils import  get_all_projects, get_prject_schema_file_paths, parse_model, read_yml, logger

from llama_index.core import SQLDatabase
from llama_index.core.schema import TextNode
from llama_index.embeddings.gemini import GeminiEmbedding
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.vector_stores.utils import (
    legacy_metadata_dict_to_node,
    metadata_dict_to_node,
    node_to_metadata_dict,
)

from sqlalchemy import text
import sqlalchemy

from dotenv import load_dotenv, find_dotenv

load_dotenv(find_dotenv())



# create the chroma client and add our data



def main():
    """Run the embedding process"""
    # tags = []
    # logger = get_logger()
     # Example log
    logger.info("EEL process started")
    
    COLLECTION_NAME = os.getenv("VECTOR_DB_COLLECTION")
    # GOOGLE_GEMINI_API_KEY = os.getenv("GOOGLE_API_KEY")
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    # metadata = MetaData()
    

    source_engine = create_engine_to_source_datawarehouse()
    logger.info("Connected to database")
    collection = get_vector_database_collection(COLLECTION_NAME)
    logger.info(f"Connected to Chroma db @ {collection}")
    # model_name = "models/embedding-001"
    include_projects = {'banking_mart', 'trade_mart', 'exchange_mart'}

    
    # embed_model = GeminiEmbedding(model_name=model_name, api_key=GOOGLE_GEMINI_API_KEY)
    
    # print("Usable Tables", usable_tables)
    embed_model = OpenAIEmbedding(api_key=OPENAI_API_KEY)
    relevant_projects = get_all_projects(include_projects=include_projects)
    logger.info(f"Found {len(relevant_projects)} dbt projects. Searching for schema.yaml files")

    for proj_name in relevant_projects:
        sql_database = SQLDatabase(source_engine, schema=proj_name)
        usable_tables = sql_database.get_usable_table_names()

        logger.info(f"Processing dbt project {proj_name}")
        logger.info(f"Searching for schema.yaml files in dbt project {proj_name}")
        schema_files_paths = get_prject_schema_file_paths(proj_name)
        # if schema_files_paths:
        logger.info(f"Found {len(schema_files_paths)} schema.yml files in mart directories")
       
        models = []
        for schema_file in schema_files_paths:
            schemas = read_yml(schema_file)
            models += schemas['models']

            tags= [proj_name]
            for model in models:
                table_info = parse_model(model, schema_name=proj_name)
                if table_info is None:
                    continue
                # for model in models:

        # for table_name in sql_database.get_usable_table_names():
                table_name = model['name']
                
                if not table_name in usable_tables:
                    logger.warning(f"Found description for table {table_name} but no table found in database. Skipping..")
                    continue
                
                node_id=f"SH_{proj_name}.{table_name}"
                node = TextNode(text=table_info, node_id=node_id)

                metadata_dict = node_to_metadata_dict(
                    node, remove_text=False
        )
                    ## generate embedding
                MAX_LENGTH = 3500
                if len(table_info) > MAX_LENGTH:
                    table_info_to_be_embed = table_info[:MAX_LENGTH]
                else:
                    table_info_to_be_embed = table_info
                #
            
                embeddings = embed_model.get_text_embedding(table_info_to_be_embed)

                # embeddings = embed_model.get_text_embedding(table_info[:MAX_LENGTH])
                data = (node_id, embeddings, metadata_dict)
    

                collection.upsert(records = [data]
                        )
                
                        
                print(f'Added {table_name} to embedding collection')
    logger.info("Creating Index")
    collection.create_index()
    logger.info("EEL process completed")

if __name__ == '__main__':
     main()