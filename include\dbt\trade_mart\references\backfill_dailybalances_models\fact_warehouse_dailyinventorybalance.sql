/*

-- WAREHOUSE DAILY INVENTORY BALANCE
-- *** NOTE This query is to recreate the historic table only and is not meant to update the table. There is another script to achieve this incremental updates***

-- -- Create fact_warehouse_dailyinventorybalance Table
DROP TABLE trade_mart.fact_warehouse_dailyinventorybalance;

CREATE TABLE trade_mart.fact_warehouse_dailyinventorybalance (
				inventory_date DATE, 
				warehouse_code VARCHAR(25),
				warehouse_name TEXT,
				item_code VARCHAR(25),
				item_name TEXT,
				item_type VARCHAR(25),
				grade VARCHAR(25),
				tenant_id VARCHAR(25), 
				latest_lien_bags INT,
				latest_available_bags INT,
				latest_total_bags INT,
				latest_lien_net_weight DOUBLE PRECISION,
				latest_available_net_weight DOUBLE PRECISION,
				latest_total_net_weight DOUBLE PRECISION,
				latest_lien_gross_weight DOUBLE PRECISION,
				latest_available_gross_weight DOUBLE PRECISION,
				latest_total_gross_weight DOUBLE PRECISION,
				latest_trans_at TIMESTAMP,
				warehouse_inventory_account_id VARCHAR(25)
	);


-- -- Insert Historic Data Into The Table
INSERT INTO trade_mart.fact_warehouse_dailyinventorybalance (inventory_date, warehouse_code, warehouse_name, item_code, item_name, item_type, grade, tenant_id, 
																	latest_lien_bags, latest_available_bags, latest_total_bags, 
																	latest_lien_net_weight, latest_available_net_weight, latest_total_net_weight, 
																	latest_lien_gross_weight, latest_available_gross_weight, latest_total_gross_weight, latest_trans_at,
																	warehouse_inventory_account_id)

-- Create an index to identify each warehouse's commodity and commodity grade
with base_table as (

	select *
			, row_number() over (partition by date(created), warehouse_inventory_account_id  order by created desc) daily_index
	
	from workbench.inventory_warehouseinventorytransaction
	
	where is_deleted is false
	
	),
	
-- Because we are only concerned with the inventory balance per day,
	-- we need to retrieve only the last transaction of each warehouse per day per commodity and per commodity grade
last_daily_trans as (
	select *
	from base_table
	where daily_index = 1
	),

date_dim AS (
    -- Generate a date range for the required period (adjustable based on business needs)
	select date_actual
	
	from afex_gen_mart.dim_date
    where date_actual between '2021-04-12' AND (current_date - 1) 	--2021-04-21 is the first date on the wallet_walletoperationlogging table
	),

warehouse_inventory_start_date as (
    -- Determine the first transaction date for each client
    select warehouse_inventory_account_id
        	, date(min(created)) as start_date
	
    from base_table
    group by warehouse_inventory_account_id
	),

warehouses_and_dates as (
        -- Generate a date series for each client, starting from their first transaction date
    select warehouse_inventory_start_date.*
            , date_dim.date_actual
	
    from warehouse_inventory_start_date
    left join date_dim
    on warehouse_inventory_start_date.start_date <= date_dim.date_actual
	),

balance_per_day as(
        -- Now we join this date series for each warehouse to the balances and total amount table, to get the balance for the days they did transact, however we are not done yet.
        -- We need to get the latest transaction value for days with no transactions after the warehouse started transacting, and we require and helper column for that, for which we created running total field
        -- This field would be concatenated with the warehouse_id to create a unique identifier which would be used in identifying the latest transaction at each level. This is done in the following cte
	SELECT wad.date_actual inventory_date
			, wad.warehouse_inventory_account_id, wh.code as warehouse_code, wh.name as warehouse_name 
			, prod_item.code as item_code, prod_item.name as item_name, prod_item.product_type as item_type
			, acc.grade, acc.tenant_id

			, bal.created, bal.updated, bal.source
			, bal.bags, bal.lien_bags_before, bal.lien_bags_after
			, bal.bags_before, bal.bags_after
			, bal.total_bags_before, bal.total_bags_after
			, bal.net_weight, bal.lien_net_weight_before, bal.lien_net_weight_after
			, bal.net_weight_before, bal.net_weight_after
			, bal.total_net_weight_before, bal.total_net_weight_after
			, bal.gross_weight, bal.lien_gross_weight_before, bal.lien_gross_weight_after
			, bal.gross_weight_before, bal.gross_weight_after
			, bal.total_gross_weight_before, bal.total_gross_weight_after
	
			, sum( case when bal.net_weight is null then 0 else 1 end ) 
					over( partition by wad.warehouse_inventory_account_id order by wad.date_actual ) running_total_transactions
	
	from warehouses_and_dates wad
	left join last_daily_trans bal
	on wad.date_actual = date(bal.created)
		and wad.warehouse_inventory_account_id = bal.warehouse_inventory_account_id
	left join workbench.inventory_warehouseinventoryaccount acc
	on wad.warehouse_inventory_account_id = acc.id
	left join workbench.workbench_warehouse wh
	on acc.warehouse_id = wh.id
	left join trade_mart.dim_item prod_item
	on acc.item_id = prod_item.id
	),


-- Carry forward the last known balance
final_data as (
	select inventory_date, warehouse_code, warehouse_name, item_code, item_name, item_type, grade, tenant_id, 
			first_value(lien_bags_after) over (partition by warehouse_inventory_account_id, running_total_transactions order by created) latest_lien_bags,
			first_value(bags_after) over (partition by warehouse_inventory_account_id, running_total_transactions order by created) latest_available_bags,
			first_value(total_bags_after) over (partition by warehouse_inventory_account_id, running_total_transactions order by created) latest_total_bags,
			first_value(lien_net_weight_after) over (partition by warehouse_inventory_account_id, running_total_transactions order by created) latest_lien_net_weight,
			first_value(net_weight_after) over (partition by warehouse_inventory_account_id, running_total_transactions order by created) latest_available_net_weight,
			first_value(total_net_weight_after) over (partition by warehouse_inventory_account_id, running_total_transactions order by created) latest_total_net_weight,
			first_value(lien_gross_weight_after) over (partition by warehouse_inventory_account_id, running_total_transactions order by created) latest_lien_gross_weight,
			first_value(gross_weight_after) over (partition by warehouse_inventory_account_id, running_total_transactions order by created) latest_available_gross_weight,
			first_value(total_gross_weight_after) over (partition by warehouse_inventory_account_id, running_total_transactions order by created) latest_total_gross_weight,
			coalesce(created, (first_value(created) over (partition by warehouse_inventory_account_id, running_total_transactions order by created))) latest_trans_at,
			warehouse_inventory_account_id


	from balance_per_day
	)

select * 
from final_data
order by inventory_date desc, warehouse_name, item_code, grade
*/