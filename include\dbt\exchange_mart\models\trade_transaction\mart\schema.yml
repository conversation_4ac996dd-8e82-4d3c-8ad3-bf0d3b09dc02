# This was generated using dbt_codegen package. Install then run the below code in terminal.
# dbt run-operation generate_model_yaml --args '{"model_names": ["fact_clientwallet", "fact_closingprice", "fact_matchedorder", "fact_trade_individual_transactions", "fact_transactions"], "upstream_descriptions": true}'

# NOTE: If The Model(s) is(are) updated, the command can be rerun for the particular model(s), note that except the descriptions of columns are in preceeding source tables, any maually entered description here will not be auto generated.
#       As a result, it is advisable to add new documnetation one by one.
#                     it is also advisable to enter descriptions in source tables first before running these which will then inherit the descriptions from the preceeding table(s) descriptions

version: 2

models:
  - name: fact_trade_individual_transactions
    description: ""
    columns:
      - name: execution_id
        data_type: text
        description: "Identifier for transaction execution. This tags otc trades per delivery"
        tests:
        - unique
      - name: deal_id
        data_type: text
        description: "Identifier for a fulfiled deal. This tags otc trades per day"
      
      - name: executed_date
        data_type: date
        description: "Date the transaction was executed. When the non-otc order was matched or the otc order was delivered."
      - name: executed_volume_kg
        data_type: double precision
        description: "Total volume that was delivered in that transaction"
      - name: execution_created_at
        data_type: timestamp with time zone
        description: "This is date when the non-otc transaction was first matched or when the otc dispatch was done"
      - name: execution_updated_at
        data_type: timestamp with time zone
        description: "When transaction was updated"
      - name: trade_created_at
        data_type: timestamp with time zone
        description: "This the date the client first made the order - order creation"
      - name: trade_updated_at
        data_type: timestamp with time zone
        description: "When the initial order was updated"
      - name: deal_created_at
        data_type: timestamp with time zone
        description: "Date the order was first matched"
      - name: adjusted_deal_created_at
        data_type: date
        description: "Deal created date adjust to timezone WAT"
      - name: deal_updated_at
        data_type: timestamp with time zone
        description: "The last time the deal was updated i.e the last time the matched order was updated"
      - name: deal_processed_at
        data_type: timestamp with time zone
        description: "When the matched order was first processed"
      - name: trade_is_on_behalf
        data_type: boolean
        description: "Is the trade on behalf of another client"
      - name: trade_status
        data_type: text
        description: "Status of the trade. Is the trade Canceled, Market Canceled, Partial or Pending"
        
      - name: is_order_cancelled
        data_type: boolean
        description: "Flag to show if order has been canceled"
      - name: trade_is_rejected
        data_type: boolean
        description: "Flag to show if traded is rejected"
      - name: tid
        data_type: text
        description: "Unique transaction identifier. This id however is not unique on this table"
      - name: trans_cid
        data_type: text
        description: "Client CID associated with the order request"
        
      - name: order_type
        data_type: text
        description: "Type of order"
        tests:
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['Buy', 'Sell']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)
                    
      - name: matched_id
        data_type: text
        description: "Unique trade match identifier"
      - name: sell_tid
        data_type: text
        description: "tid of the seller's transaction"
      - name: buy_tid
        data_type: text
        description: "tid of the buyer's transaction"
      - name: seller_cid
        data_type: text
        description: "Unique cid of the seller"
      - name: buyer_cid
        data_type: text
        description: "Unique cid of the buyer"
      - name: volume_per_unit
        data_type: bigint
        description: "Volume of commodity in a unit of the commodity on the exchange"
        tests:
          - not_null
       
      - name: calc_volume_per_unit
        data_type: bigint
        description: "Calculated volume of commodity in a unit of the commodity"
        tests:
          - not_null
        
      - name: currency
        data_type: text
        description: "Currency which is being used in that transaction"
        tests:
          - not_null
        
      - name: order_units
        data_type: bigint
        description: "Total number of units raised by the order"
        
      - name: order_price
        data_type: double precision
        description: "Bid price for the commodity"
        
      - name: matched_units
        data_type: bigint
        description: "Total number of units that was matched from the order units under this tid"
      - name: matched_price
        data_type: double precision
        description: "Price of the matched units"
        
      - name: cancelled_units
        data_type: bigint
        description: "Number of canceled units from the order units"
        
      - name: commodity
        data_type: text
        description: "Commodity to be traded"
        
      - name: commodity_code
        data_type: text
        
      - name: consolidated_commodity_name
        data_type: text
        description: "Standardized name of the commodity."
      - name: board_type
        data_type: text
        description: "Type of security"
        tests:
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
                value_set: ['OTC', 'Spot', 'Dawa', 'FI']
                quote_values: true # (Optional. Default is 'true'.)
                row_condition: "id is not null" # (Optional)
        
      - name: security_type
        data_type: text
        description: "Type of security associated with the transaction."
        tests:
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
                value_set: ['OTC', 'Spot', 'Dawa', 'FI']
                quote_values: true # (Optional. Default is 'true'.)
                row_condition: "id is not null" # (Optional)
        
      - name: security_name
        data_type: text
        description: "Name of the security associated with the trade."
      - name: security_code
        data_type: text
        description: "Security code"
      - name: oms_name
        data_type: text
        description: "Order Management System name"
        tests:
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['ComX', 'Astra']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)
        
      - name: oms_code
        data_type: text
        description: "Order Management System code"
        
        
      - name: total_order_price
        data_type: double precision
        description: "Total price of the order before any fees or additional charges."
         
      - name: total_order_price_with_fees
        data_type: double precision
        description: "Total price of the order after including all applicable fees."
      
      - name: use_ecn_fees
        data_type: boolean
        description: "Indicates whether ECN (Electronic Communication Network) fees are applied to the order."
      
      - name: total_afex_fees
        data_type: double precision
        description: "Total AFEX (African Exchange) fees applied to the transaction."
      
      - name: total_oms_fees
        data_type: double precision
        description: "Total fees charged by the Order Management System (OMS)."
      
      - name: sec_fee
        data_type: double precision
        description: "Fee charged by the Securities and Exchange Commission (SEC)."
      
      - name: exchange_fee
        data_type: double precision
        description: "Fee charged by the exchange for processing the transaction."
      
      - name: cm_fee
        data_type: double precision
        description: "Clearing Member (CM) fee applied to the transaction."
      
      - name: brokerage_fee
        data_type: double precision
        description: "Fee charged by the broker for facilitating the trade."
      
      - name: vat_value
        data_type: double precision
        description: "Value Added Tax (VAT) amount applied to the transaction."
      
      - name: fees_breakdown
        data_type: text
        description: "Total fees breakdown in JSON format, detailing individual components of the total fees."
      
      - name: discount
        data_type: double precision
        description: "Discount applied to the transaction."
      
      - name: is_contract_note_sent
        data_type: boolean
        description: "Indicates whether the contract note has been sent."
      
      - name: processed_for_inventory
        data_type: boolean
        description: "Indicates whether the transaction has been processed for inventory records."
      
      - name: trade_tenure
        data_type: timestamp with time zone
        description: "Duration of the trade or contract."
      
      - name: ovs_validation
        data_type: text
        description: "Validation status from the OVS (Operational Validation System)."
      
      - name: is_manually_matched
        data_type: boolean
        description: "Indicates whether the contract was manually matched."
      
      - name: contract_created_at
        data_type: timestamp with time zone
        description: "Timestamp when the contract was created."
      
      - name: contract_updated_at
        data_type: timestamp with time zone
        description: "Timestamp when the contract was last updated."
      
      - name: contract_price
        data_type: double precision
        description: "Price agreed upon in the contract."
      
      - name: contract_tenure
        data_type: timestamp with time zone
        description: "Duration of the contract."
      
      - name: contract_is_deleted
        data_type: boolean
        description: "Indicates whether the contract has been deleted."
      
      - name: contract_is_cancelled
        data_type: boolean
        description: "Indicates whether the contract has been cancelled."
      
      - name: contract_delivery_status
        data_type: text
        description: "Current status of the contract delivery."
      
      - name: contract_buy_match_order_id
        data_type: bigint
        description: "ID of the buy match order associated with the contract."
      
      - name: security_location
        data_type: text
        description: "Location where the security is stored."
      
      - name: security_location_state
        data_type: text
        description: "State in which the security location is situated."
      
      - name: contract_seller_location_code
        data_type: text
        description: "Location code of the seller in the contract."
      
      - name: seller_region
        data_type: text
        description: "Region of the seller involved in the contract."
      
      - name: contract_buyer_location_code
        data_type: text
        description: "Location code of the buyer in the contract."
      
      - name: buyer_region
        data_type: text
        description: "Region of the buyer involved in the contract."
      
      - name: logistic_differential
        data_type: double precision
        description: "Cost adjustment due to logistics."
      
      - name: contract_id
        data_type: text
        description: "Unique identifier for the contract."
      
      - name: delivery_id
        data_type: text
        description: "Unique identifier for the delivery."
      
      - name: dispatch_created_at
        data_type: timestamp with time zone
        description: "Timestamp when the dispatch record was created."
      
      - name: dispatch_date
        data_type: timestamp with time zone
        description: "Date when the commodity was dispatched."
      
      - name: adjusted_dispatch_created_at
        data_type: date
        description: "Adjusted date when the dispatch was created."
      
      - name: dispatch_updated_at
        data_type: timestamp with time zone
        description: "Timestamp when the dispatch record was last updated."
      
      - name: dispatch_bag
        data_type: bigint
        description: "Number of bags dispatched."
      
      - name: dispatch_is_deleted
        data_type: boolean
        description: "Indicates whether the dispatch record has been deleted."
      
      - name: dispatch_is_reverted
        data_type: boolean
        description: "Indicates whether the dispatch has been reverted."
      
      - name: dispatch_is_buyer_pickup
        data_type: boolean
        description: "Indicates whether the buyer picked up the dispatched goods."
      
      - name: delivered_volume
        data_type: double precision
        description: "Volume of goods delivered."
      
      - name: actual_delivered_volume
        data_type: double precision
        description: "Actual measured volume of goods delivered."
      
      - name: dispatchlog_pk
        data_type: bigint
        description: "Primary key of the dispatch log."
      
      - name: reverted_from_id
        data_type: bigint
        description: "ID of the original dispatch record that was reverted."
      
      - name: initial_dispatched_volume
        data_type: double precision
        description: "Volume of goods initially dispatched before any adjustments."
      
      - name: dispatched_bags
        data_type: bigint
        description: "Total number of bags dispatched."
      
      - name: wb_phy_dispatch_weight
        data_type: double precision
        description: "Weight of the dispatched goods as recorded on workbench."
      
      - name: wb_phy_dispatch_bags
        data_type: bigint
        description: "Number of bags recorded on workbench during dispatch."
      
      - name: dispatch_id
        data_type: text
        description: "Unique identifier for the dispatch transaction."
      
      - name: physical_dispatch_log_id
        data_type: text
        description: "Identifier for the physical dispatch log entry."
      
      - name: is_settlement_confirmed
        data_type: boolean
        description: "Indicates whether the settlement has been confirmed."
      
      - name: delivery_company
        data_type: text
        description: "Name of the company responsible for delivery."
      
      - name: truck_number
        data_type: text
        description: "Number of the truck used for dispatch."
      
      - name: drivers_name
        data_type: text
        description: "Name of the truck driver handling the dispatch."
      
      - name: drivers_phone
        data_type: text
        description: "Phone number of the truck driver."
      
      - name: dispatch_status
        data_type: text
        description: "Current status of the dispatch."
      
      - name: warehouse_code
        data_type: text
        description: "Code of the warehouse where the goods are stored."
      
      - name: warehouse_name
        data_type: text
        description: "Name of the warehouse where the goods are stored."
      
      - name: wb_tenant_id
        data_type: bigint
        description: "Tenant ID on workbench associated with the warehouse where the physical commodity is"
      
      - name: initial_dispatch_id
        data_type: bigint
        description: "Unique identifier for the initial dispatch of the commodity."
