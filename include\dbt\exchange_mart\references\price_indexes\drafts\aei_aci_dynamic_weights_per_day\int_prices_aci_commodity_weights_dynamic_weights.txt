/*
with 

seasonal_cum_volumes as (
	select *
			, sum(exchange_executed_vol_mt) over (partition by season, commodity order by date) com_cumm_ex_vol_mt_per_season
			, sum(exchange_executed_vol_mt) over (partition by season order by date) cumm_ex_vol_mt_per_season
			, sum(prior_national_vol_mt) over (partition by season, commodity order by date) prior_seas_nat_com_cumm_vol_mt
			, sum(prior_national_vol_mt) over (partition by season order by date) prior_seas_nat_cumm_vol_mt
	
	from {{ ref ('int_prices_commodities_volumes') }}
    where commodity in ('Maize Feed Grade - White'
						, 'Paddy Rice Long Grain'
						, 'Soybean'
						, 'Sorghum')
	),
	
prior_season_cumm_vol as (
	select *
			, case when extract('month' from date) = 10 and extract('day' from date) = 1 -- October 1
					then (lag(com_cumm_ex_vol_mt_per_season, 1) over (partition by commodity order by date))
				else null
				end prior_com_cumm_ex_vol_mt_per_season
			, case when extract('month' from date) = 10 and extract('day' from date) = 1 -- October 1
					then (lag(cumm_ex_vol_mt_per_season, 1) over (partition by commodity order by date))
				else null
				end prior_cumm_ex_vol_mt_per_season
			
	
	from seasonal_cum_volumes
	),

each_day_with_prior_season_cumm_vol as (
	select *
			, coalesce((first_value(prior_com_cumm_ex_vol_mt_per_season) over (partition by season, commodity order by date)), 0) prior_seas_ex_com_vol_mt
			, coalesce((first_value(prior_cumm_ex_vol_mt_per_season) over (partition by season, commodity order by date)), 0) prior_seas_ex_vol_mt 
			-- Using 0 since exchange trdes commenced in 2020 yeah, so no prior season value
	
	from prior_season_cumm_vol
	),
	
commodity_wt  as (
	select *
		, ((prior_seas_ex_com_vol_mt * 0.7) / (nullif(prior_seas_ex_vol_mt, 0))) exchange_weight
		, ((prior_seas_nat_com_cumm_vol_mt * 0.3) / (nullif(prior_seas_nat_cumm_vol_mt, 0))) national_weight
		, ((prior_seas_ex_com_vol_mt * 0.7) / (nullif(prior_seas_ex_vol_mt, 0))) + ((prior_seas_nat_com_cumm_vol_mt * 0.3) / (nullif(prior_seas_nat_cumm_vol_mt, 0))) commodity_weight	
		, extract(dow from date::timestamp) dow
		, concat(extract(year from date::timestamp), '_', extract(week from date::timestamp)) woy
	
		
	from each_day_with_prior_season_cumm_vol
	)

select *
from commodity_wt
*/