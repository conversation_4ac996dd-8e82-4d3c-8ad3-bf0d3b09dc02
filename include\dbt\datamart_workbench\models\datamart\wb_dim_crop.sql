


{{ config(materialized='table') }}

with a as (
    select * from {{source('workbench','crm_farmer')}}
),

z as (SELECT a.farmer_id ,c.code,c.name
    FROM {{source('workbench','crm_farmer_crop_type')}} a
    INNER JOIN {{source('workbench','inventory_item')}} b ON a.item_id = b.id
    INNER JOIN {{source('workbench','workbench_product')}} c ON b.product_id = c.id
   
),
final as ( select a.warehouse_id,a.created farmer_created,z.*
FROM workbench.crm_farmer a
inner join z ON a.id = z.farmer_id
)

select * from final
