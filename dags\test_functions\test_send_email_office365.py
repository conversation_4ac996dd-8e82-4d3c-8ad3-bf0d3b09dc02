import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from dotenv import load_dotenv
load_dotenv()
import os

def send_email(sender_email, sender_password, recipient_email, subject, message_body):
    # Set up the SMTP server
    smtp_server = "smtp.office365.com"
    smtp_port = 587  # For TLS

    # Create the email message
    msg = MIMEMultipart()
    msg['From'] = sender_email
    msg['To'] = recipient_email
    msg['Subject'] = subject

    # Attach the message body
    msg.attach(MIMEText(message_body, 'plain'))

    try:
        # Establish a connection to the server
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()  # Enable TLS encryption
        server.login(sender_email, sender_password)  # Login to your email account

        # Send the email
        server.sendmail(sender_email, recipient_email, msg.as_string())
        print("Email sent successfully!")

    except Exception as e:
        print(f"Error: {e}")

    finally:
        server.quit()  

send_email(
    sender_email=AIRFLOW__SMTP__SMTP_MAIL_FROM,
    sender_password=AIRFLOW__SMTP__SMTP_PASSWORD,  
    recipient_email="<EMAIL>",
    subject="Testingggg",
    message_body="Testinggg"
)