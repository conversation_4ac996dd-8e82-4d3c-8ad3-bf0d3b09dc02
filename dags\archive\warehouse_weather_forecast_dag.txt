from datetime import datetime,timedelta
from airflow import DAG
from airflow.operators.bash  import Ba<PERSON><PERSON>perator
from email_util import failure_email

PATH_TO_SCRIPT_FOLDER = "/usr/local/airflow/include/scripts"

default_args  = {
     'owner' : '<PERSON>',
     'retries' : 0,
     'retry_dalay' : timedelta(minutes=2),
     'on_failure_callback': failure_email,
}
with DAG(
    dag_id = 'warehouse_weather_forecast',
    default_args = default_args,
    description =  'Warehouses Location Weekly Weather Forecast',
    start_date =  datetime(2024, 10, 23),
    catchup=False,
    schedule_interval =  '00 00 * * *' ### this uses utc time : this pipeline runs at 12:00AM WAT
) as dag:
    
    task1 =  BashOperator(
        task_id = 'weather_forecast',
        bash_command = 'python3 forecast.py',
        cwd=PATH_TO_SCRIPT_FOLDER,
    )


task1
