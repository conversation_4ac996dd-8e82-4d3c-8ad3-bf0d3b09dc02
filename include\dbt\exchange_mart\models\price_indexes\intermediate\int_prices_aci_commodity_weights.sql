{{ config (materialized = 'ephemeral') }}

with
seasonal_cumm_volumes as (
	select *
		
			, sum(three_yr_moving_avg_ex_vol_mt) over (partition by season, commodity order by date) prior_seas_3yr_avg_ex_com_cumm_vol_mt
			, sum(three_yr_moving_avg_ex_vol_mt) over (partition by season order by date) prior_seas_3yr_avg_ex_cumm_vol_mt
	
			, sum(three_yr_moving_avg_nat_vol_mt) over (partition by season, commodity order by date) prior_seas_3yr_avg_nat_com_cumm_vol_mt
			, sum(three_yr_moving_avg_nat_vol_mt) over (partition by season order by date) prior_seas_3yr_avg_nat_cumm_vol_mt
	
	from {{ ref ('int_prices_aci_aei_volumes') }}
    where commodity in ('Maize Feed Grade - White'
						, 'Paddy Rice Long Grain'
						, 'Soybean'
						, 'Sorghum')
	),
	
commodity_wt  as ( -- Commodity Weight 50:50 for National & Exchange volumes
	select *
		, ((prior_seas_3yr_avg_ex_com_cumm_vol_mt * 0.5) / (nullif(prior_seas_3yr_avg_ex_cumm_vol_mt, 0))) exchange_weight_3yr_avg
		, ((prior_seas_3yr_avg_nat_com_cumm_vol_mt * 0.5) / (nullif(prior_seas_3yr_avg_nat_cumm_vol_mt, 0))) national_weight_3yr_avg
		, ((prior_seas_3yr_avg_ex_com_cumm_vol_mt * 0.5) / (nullif(prior_seas_3yr_avg_ex_cumm_vol_mt, 0))) + ((prior_seas_3yr_avg_nat_com_cumm_vol_mt * 0.5) / (nullif(prior_seas_3yr_avg_nat_cumm_vol_mt, 0))) commodity_weight_3yr_avg
	
		, extract(dow from date::timestamp) dow
		, concat(extract(year from date::timestamp), '_', extract(week from date::timestamp)) woy
		, concat('Q', extract(quarter from date::timestamp), ' ', extract(year from date::timestamp)) quarter
		
	from seasonal_cumm_volumes
	)

select *
from commodity_wt