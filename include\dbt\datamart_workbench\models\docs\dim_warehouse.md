{% docs dim_warehouse_id %}
The primary key for the Warehouse table.
{% enddocs %}

{% docs dim_warehouse_location_id %}
location_id ||FK|| from workbench.location_location
The primary key assigned to the specific warehouse location in the location_location table.
{% enddocs %}

{% docs dim_warehouse_tenant_id %}
tenant_id ||FK|| from workbench.tenant_tenant
The primary key assigned to the specific tenant in the tenant table.
{% enddocs %}

{% docs dim_warehouse_tenant_name %}
The name of the tenant with the corresponding tenant_id.
{% enddocs %}

{% docs dim_warehouse_warehouse_name %}
The name of the warehouse with the corresponding warehouse_id.
{% enddocs %}

{% docs dim_warehouse_warehouse_code %}
The code assigned to the specific warehouse.
{% enddocs %}

{% docs dim_warehouse_warehouse_manager_id %}
The identification number assigned to the warehouse manager.
{% enddocs %}

{% docs dim_warehouse_address %}
The full address of the warehouse.
{% enddocs %}

{% docs dim_warehouse_longitude %}
The longitudinal co-ordinate of the warehouse.
{% enddocs %}


{% docs dim_warehouse_latitude %}
The latitudinal co-ordinate of the warehouse.
{% enddocs %}

{% docs dim_warehouse_warehouse_email %}
The email address of the warehouse.
{% enddocs %}

{% docs dim_warehouse_location %}
The base location of the warehouse, as characterised by the AFEX nomenclature system. This is not the state in which the warehouse is located.
{% enddocs %}

{% docs dim_warehouse_state %}
The state where the warehouse is located.
{% enddocs %}

{% docs dim_warehouse_capital %}
The state capital where the warehouse is located.
{% enddocs %}

{% docs dim_warehouse_region_code %}
The region code of where the warehouse is located.
{% enddocs %}

{% docs dim_warehouse_region_name %}
The name of the region where the warehouse is located.
{% enddocs %}

{% docs dim_warehouse_country %}
The country where the warehouse is located.
{% enddocs %}

{% docs dim_warehouse_country_capital %}
The capital of the country where the warehouse is located.
{% enddocs %}

{% docs dim_warehouse_continent %}
The continent where the warehouse is located.
{% enddocs %}

{% docs dim_warehouse_continent_subregion %}
The subregion of the continent subregion where the warehouse is located
{% enddocs %}
