{{ config(
	materialized= 'table'
	) }}

with

original as (
    
    select 
        cid,
        account_type,
        user_account_type,
        COALESCE(client_tenure_months,0) client_tenure_months,
        COALESCE(trade_freq,0) trade_freq,
        COALESCE(last90days_trade_freq,0) last90days_trade_freq,
        COALESCE(buy_trade_freq,0) buy_trade_freq,
        COALESCE(sell_trade_freq,0) sell_trade_freq,
        COALESCE(trade_recency_months,0) trade_recency_months,
        COALESCE(trade_tenure_months,0) trade_tenure_months,
        COALESCE(trade_value,0) trade_value,
        COALESCE(available_balance,0) available_balance,
        COALESCE(cash_advance_balance,0) cash_advance_balance,
        COALESCE(deposit_freq,0) deposit_freq,
        COALESCE(last90days_deposit_freq,0) last90days_deposit_freq,
        COALESCE(deposit_recency_months,0) deposit_recency_months,
        COALESCE(deposit_tenure_months,0) deposit_tenure_months,
        COALESCE(deposit_amount,0) deposit_amount,
        COALESCE(withdrawal_freq,0) withdrawal_freq,
        COALESCE(last90days_withdrawal_freq,0) last90days_withdrawal_freq,
        COALESCE(withdrawal_recency_months,0) withdrawal_recency_months,
        COALESCE(withdrawal_tenure_months,0) withdrawal_tenure_months,
        COALESCE(withdrawal_amount,0) withdrawal_amount,
        COALESCE(loan_freq,0) loan_freq,
        COALESCE(loan_recency_months,0) loan_recency_months,
        COALESCE(loan_tenure_months,0) loan_tenure_months,
        COALESCE(loan_value_collected,0) loan_value_collected,
        row_number() over(partition by cid) row_number_index

    from {{ ref('stg_clients_transactions') }}

    where user_account_type = 'Individual'
),

final as(

    select cid,
            account_type,
            user_account_type,
            client_tenure_months,
            trade_freq,
            last90days_trade_freq,
            buy_trade_freq,
            sell_trade_freq,
            trade_recency_months,
            trade_tenure_months,
            trade_value,
            available_balance,
            cash_advance_balance,
            deposit_freq,
            last90days_deposit_freq,
            deposit_recency_months,
            deposit_tenure_months,
            deposit_amount,
            withdrawal_freq,
            last90days_withdrawal_freq,
            withdrawal_recency_months,
            withdrawal_tenure_months,
            withdrawal_amount,
            loan_freq,
            loan_recency_months,
            loan_tenure_months,
            loan_value_collected

    from original
    where row_number_index = 1
)

select * from final