version: 2
sources:
  - name: ovs
    description: ovs data
    tables:
      - name: crm_client
        description: One record per registered client on the exchange with a system
          generated identification number
        columns:
          - name: id
            description: This is the table's unique identifier and primary key
            tests:
              - unique
              - not_null
          - name: cid
            description: Are you seeing this?
            tests:
              - unique
              - not_null
      - name: trade_matchedorder
        description: contains only matched transaction records
      - name: trade_clientorderrequest
        description: contains both matched and failed transaction records
      - name: ecn_currency
      - name: crm_omsprovider
      - name: crm_clientwallet
        description: One record per latest wallet balance, per exchange clients with
          wallet accounts
        columns:
          - name: id
            description: This is the table's unique identifier and primary key
            tests:
              - unique
              - not_null
          - name: total_balance
            description: ''
            tests:
              - not_null
          - name: available_balance
            description: ''
            tests:
              - not_null
          - name: lien_balance
            description: ''
            tests:
              - not_null
          - name: cash_advance_limit
            description: ''
          - name: cash_advance_spent
            description: ''
            # tests: expect to be value
          - name: created
            description: This is the creation timestamp
            tests:
              - not_null
          - name: updated
            description: This is the updated timestamp
            tests:
              - not_null
          - name: client_id
            description: ''
            tests:
              - unique
              - not_null
          - name: cash_advance_balance
            description: ''
          - name: is_deleted
            description: ''
            tests:
              - not_null
          - name: agent_id
            description: ''
          - name: currency_id
            description: ''
            tests:
              - not_null
      - name: crm_board
      - name: crm_security
      - name: crm_commodity
      - name: otc_trade_contract
      - name: otc_trade_initialotcdispatchlog
      - name: otc_trade_otcdispatchlog

  - name: comx
    description: comx data
    tables:
      - name: crm_client
      - name: trading_trade
      - name: crm_community
      - name: crm_security
      - name: trading_matchorder
      - name: account_country
      - name: crm_location

  - name: workbench
    description: workbench data
    tables:
      - name: location_state
      - name: location_region
      - name: dispatch_physicaldispatch

  - name: trade_mart
    description: datateam's analytics schema for africa exchange trades data
    tables:
      - name: dim_date
      - name: fact_matchedorder