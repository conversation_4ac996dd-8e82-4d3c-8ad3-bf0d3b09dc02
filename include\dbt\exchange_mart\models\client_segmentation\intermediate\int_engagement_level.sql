{{ config(materialized='ephemeral') }}


-- Merge the clients login actions with the combined activities
WITH client_active_status AS (
 SELECT 
  xm.cid,
  xm.created,
  ll.last_seen,

   --Standardize intervals to only include years, months and days
   (
   EXTRACT(YEAR FROM ll.time_since_last_seen) || ' years, ' ||
     EXTRACT(MONTH FROM ll.time_since_last_seen) || ' mons, ' ||
     EXTRACT(DAY FROM ll.time_since_last_seen) || ' days' 
   )::INTERVAL AS time_since_last_seen,
 
  lca.last_action_time,
  lca.last_action_done,
   (
   EXTRACT(YEAR FROM lca.time_since_last_action) || ' years, ' ||
     EXTRACT(MONTH FROM lca.time_since_last_action) || ' mons, ' ||
     EXTRACT(DAY FROM lca.time_since_last_action) || ' days' 
   )::INTERVAL AS time_since_last_action
 FROM
  {{ ref('stg_exchange_members') }} xm
 LEFT JOIN
  {{ ref('int_client_last_login') }} ll
 ON
  xm.cid = ll.cid
 LEFT JOIN
  {{ ref('int_client_last_action') }} lca
 ON 
  xm.cid = lca.cid
 
 ),


-- Segment the client into ('Active', 'Passive', 'Domant')
client_segments AS (
 SELECT
  cid,
  COALESCE(COALESCE(last_seen, last_action_time), created) last_seen,
  COALESCE(time_since_last_seen, time_since_last_action) time_since_last_seen,
  COALESCE(last_action_time,last_seen) last_action_time,
  CASE
		WHEN COALESCE(last_seen, last_action_time) IS NULL
		THEN 'Register'  -- When client never logged in then their last action was when they registered
		ELSE COALESCE(last_action_done,'Login') 
  END last_action_done,
  COALESCE(time_since_last_action,time_since_last_seen) time_since_last_action,
  CASE 
		WHEN 
			time_since_last_action <= '30 days'::interval THEN 'Active'
		WHEN time_since_last_action > '30 days'::interval 
			AND time_since_last_seen <= '90 days'::interval THEN 'Passive'
		ELSE
			'Dormant'
  END active_status
  -- active_status
 FROM client_active_status
)

-- Final Select Statement
SELECT 
	 *
FROM client_segments