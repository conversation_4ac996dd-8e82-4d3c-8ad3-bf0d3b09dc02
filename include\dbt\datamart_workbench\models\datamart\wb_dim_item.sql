



{{ config(materialized='table') }}

with item as (
    select * from {{source('workbench','inventory_item')}}
),
product as (
    select * from {{source('workbench','workbench_product')}}
),
final as (
    select item.id,item.tenant_id,item.created,item.updated,
item.grade_one_deduction,item.grade_two_deduction,item.grade_three_deduction,
product.name,product.code,product.product_type
from item
inner join  product
on item.product_id = product.id
)

select * from final
where  tenant_id in (241,216,64,7)