# This was generated using dbt_codegen package. Install then run the below code in terminal.
# dbt run-operation generate_model_yaml --args '{"model_names": ["fact_clientwallet", "fact_closingprice", "fact_matchedorder", "fact_trade_individual_transactions", "fact_transactions"], "upstream_descriptions": true}'

# NOTE: If The Model(s) is(are) updated, the command can be rerun for the particular model(s), note that except the descriptions of columns are in preceeding source tables, any maually entered description here will not be auto generated.
#       As a result, it is advisable to add new documnetation one by one.
#                     it is also advisable to enter descriptions in source tables first before running these which will then inherit the descriptions from the preceeding table(s) descriptions

version: 2

models:
  - name: fact_clientwallet
    description: "One record per client wallet balance for clients with a wallet"
    columns:
      - name: id
        data_type: bigint
        description: "This is the table's unique identifier and primary key"
        tests:
        - unique
        - not_null
        
      - name: cid
        data_type: text
        description: Unique identifier for each client
        tests:
          - unique
          - not_null
          - dbt_expectations.expect_column_value_lengths_to_equal:
              value: 10
              row_condition: "id is not null" # (Optional)

      - name: created
        data_type: timestamp with time zone
        description: "Timestamp of the record's creation date"
        tests:
          - not_null

      - name: updated
        data_type: timestamp with time zone
        description: "Timestamp of the record's latest update timestamp"
        tests:
          - not_null

      - name: total_balance
        data_type: double precision
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: available_balance
        data_type: double precision
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: lien_balance
        data_type: double precision
        description: ""
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: cash_advance_limit
        data_type: double precision
        description: ""
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: cash_advance_spent
        data_type: double precision
        description: ""
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: cash_advance_balance
        data_type: double precision
        description: ""
        tests:
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: currency_name
        data_type: text
        description: ""
        tests:
          - not_null

      - name: currency_code
        data_type: text
        description: ""
        tests:
          - not_null

      - name: oms_name
        data_type: text
        description: ""
        tests:
          - not_null

      - name: oms_code
        data_type: text
        description: ""
        tests:
          - not_null

      - name: is_deleted
        data_type: boolean
        description: ""
        tests:
          - not_null

  - name: fact_closingprice
    description: One record per closing price per security per day
    columns:
      - name: id
        data_type: bigint
        description: "This is the table's unique identifier and primary key"
        tests:
          - unique
          - not_null

      - name: calc_trans_date
        data_type: timestamp with time zone
        description: ""
        tests:
          - not_null

      - name: board_type
        data_type: text
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['OTC', 'Spot']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)

      - name: security_code
        data_type: text
        description: ""
        tests:
          - not_null

      - name: security_name
        data_type: text
        description: ""
        tests:
          - not_null

      - name: security_type
        data_type: text
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['OTC', 'Spot', 'Dawa', 'FI']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)

      - name: daily_value
        data_type: numeric
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: total_daily_matched_unit
        data_type: numeric
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: total_daily_matched_unit_kg
        data_type: numeric
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: daily_min_price
        data_type: double precision
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: daily_max_price
        data_type: double precision
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: prev_min_price
        data_type: double precision
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: prev_max_price
        data_type: double precision
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: opening_price_per_unit
        data_type: numeric
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: closing_price_per_unit
        data_type: numeric
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: percentage_change_per_unit
        data_type: numeric
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: min_price_kg
        data_type: double precision
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: max_price_kg
        data_type: double precision
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: prev_min_price_kg
        data_type: double precision
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: prev_max_price_kg
        data_type: double precision
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: opening_price_kg
        data_type: numeric
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: closing_price_kg
        data_type: numeric
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

      - name: percentage_change_kg
        data_type: numeric
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_values_to_be_of_type:
              column_type: float

  - name: fact_matchedorder
    description: "One row per matched buy and sell client order"
    columns:
      - name: id
        data_type: bigint
        description: "This is the table's unique identifier and primary key"
        tests:
          - not_null
          - unique

      - name: created
        data_type: timestamp with time zone
        description: ""
        tests:
          - not_null

      - name: updated
        data_type: timestamp with time zone
        description: ""
        tests:
          - not_null

      - name: processed_on
        data_type: timestamp with time zone
        description: ""
        tests:
          - not_null

      - name: tid
        data_type: text
        description: ""
        tests:
          - not_null

      - name: matched_id
        data_type: text
        description: ""
        tests:
          - not_null
          - unique

      - name: buy_tid
        data_type: text
        description: ""
        tests:
          - not_null

      - name: sell_tid
        data_type: text
        description: ""
        tests:
          - not_null

      - name: buyer_cid
        data_type: text
        description: ""
        tests:
          - not_null

      - name: seller_cid
        data_type: text
        description: ""
        tests:
          - not_null

      - name: order_units
        data_type: bigint
        description: ""
        tests:
          - not_null

      - name: order_price
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: matched_units
        data_type: bigint
        description: ""
        tests:
          - not_null

      - name: matched_price
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: invoiced_units
        data_type: bigint
        description: ""
        tests:
          - not_null

      - name: volume_per_unit
        data_type: bigint
        description: ""
        tests:
          - not_null

      - name: calc_volume_per_unit
        data_type: bigint
        description: ""
        tests:
          - not_null

      - name: security_location_code
        data_type: text
        description: ""

      - name: order_type
        data_type: text
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['Buy', 'Sell']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)

      - name: board_type
        data_type: text
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['OTC', 'Spot']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)

      - name: security_name
        data_type: text
        description: ""
        tests:
          - not_null

      - name: security_code
        data_type: text
        description: ""
        tests:
          - not_null

      - name: security_type
        data_type: text
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['OTC', 'Spot', 'Dawa', 'FI']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)

      - name: community
        data_type: text
        description: ""

      - name: brokerage_community_name
        data_type: text
        description: ""

      - name: brokerage_community_code
        data_type: text
        description: ""

      - name: promoter_community_name
        data_type: text
        description: ""

      - name: promoter_community_code
        data_type: text
        description: ""

      - name: oms_name
        data_type: text
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['ComX']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)

      - name: oms_code
        data_type: text
        description: ""
        tests:
          - not_null

      - name: is_on_behalf
        data_type: boolean
        description: ""
        tests:
          - not_null

      - name: comx_created_by_id
        data_type: bigint
        description: ""
        tests:
          - not_null

      - name: total_order_price
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: total_order_price_with_fees
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: total_afex_fees
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: total_oms_fees
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: sec_fee
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: exchange_fee
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: cm_fee
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: brokerage_fee
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: vat_value
        data_type: double precision
        description: ""
        tests:
          - not_null

      - name: location_breakdown
        data_type: text
        description: ""
        tests:
          - not_null

      - name: is_contract_note_sent
        data_type: boolean
        description: ""
        tests:
          - not_null

      - name: is_audit_cancelled
        data_type: boolean
        description: ""
        tests:
          - not_null

      - name: processed_for_inventory
        data_type: boolean
        description: ""
        tests:
          - not_null

      - name: is_deleted
        data_type: boolean
        description: ""
        tests:
          - not_null

      - name: discount
        data_type: double precision
        description: ""
        tests:
          - not_null

  - name: fact_trade_individual_transactions
    description: ""
    columns:
      - name: execution_id
        data_type: text
        description: ""
        tests:
        #  - not_null
         - unique

      - name: deal_id
        data_type: text
        description: ""
        # tests:
        #  - not_null
        #  - unique

      - name: executed_date
        data_type: date
        description: ""

      - name: executed_volume_kg
        data_type: double precision
        description: ""

      - name: execution_created_at
        data_type: timestamp with time zone
        description: ""

      - name: execution_updated_at
        data_type: timestamp with time zone
        description: ""

      - name: trade_created_at
        data_type: timestamp with time zone
        description: ""

      - name: trade_updated_at
        data_type: timestamp with time zone
        description: ""

      - name: deal_created_at
        data_type: timestamp with time zone
        description: ""

      - name: adjusted_deal_created_at
        data_type: date
        description: ""

      - name: deal_updated_at
        data_type: timestamp with time zone
        description: ""

      - name: deal_processed_at
        data_type: timestamp with time zone
        description: ""

      - name: trade_is_on_behalf
        data_type: boolean
        description: ""

      - name: trade_status
        data_type: text
        description: ""

      - name: is_order_cancelled
        data_type: boolean
        description: ""

      - name: trade_is_rejected
        data_type: boolean
        description: ""

      - name: tid
        data_type: text
        description: ""

      - name: order_type
        data_type: text
        description: ""
        tests:
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['Buy', 'Sell']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)

      - name: matched_id
        data_type: text
        description: ""

      - name: sell_tid
        data_type: text
        description: ""

      - name: buy_tid
        data_type: text
        description: ""

      - name: seller_cid
        data_type: text
        description: ""

      - name: buyer_cid
        data_type: text
        description: ""

      - name: volume_per_unit
        data_type: bigint
        description: ""
        tests:
          - not_null
        

      - name: calc_volume_per_unit
        data_type: bigint
        description: ""
        tests:
          - not_null
        

      - name: currency
        data_type: text
        description: ""
        tests:
          - not_null

      - name: order_units
        data_type: bigint
        description: ""

      - name: order_price
        data_type: double precision
        description: ""

      - name: matched_units
        data_type: bigint
        description: ""

      - name: matched_price
        data_type: double precision
        description: ""

      - name: cancelled_units
        data_type: bigint
        description: ""

      - name: commodity
        data_type: text
        description: ""

      - name: consolidated_commodity_name
        data_type: text
        description: ""

      - name: board_type
        data_type: text
        description: ""
        tests:
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
                value_set: ['OTC', 'Spot', 'Dawa', 'FI']
                quote_values: true # (Optional. Default is 'true'.)
                row_condition: "id is not null" # (Optional)

      - name: security_type
        data_type: text
        description: ""
        tests:
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
                value_set: ['OTC', 'Spot', 'Dawa', 'FI']
                quote_values: true # (Optional. Default is 'true'.)
                row_condition: "id is not null" # (Optional)

      - name: security_name
        data_type: text
        description: ""

      - name: security_code
        data_type: text
        description: ""

      - name: oms_name
        data_type: text
        description: ""
        tests:
          - not_null
          - dbt_expectations.expect_column_distinct_values_to_be_in_set:
              value_set: ['ComX']
              quote_values: true # (Optional. Default is 'true'.)
              row_condition: "id is not null" # (Optional)

      - name: oms_code
        data_type: text
        description: ""
        tests:
          - not_null

      - name: total_order_price
        data_type: double precision
        description: ""

      - name: total_order_price_with_fees
        data_type: double precision
        description: ""

      - name: use_ecn_fees
        data_type: boolean
        description: ""

      - name: total_afex_fees
        data_type: double precision
        description: ""

      - name: total_oms_fees
        data_type: double precision
        description: ""

      - name: sec_fee
        data_type: double precision
        description: ""

      - name: exchange_fee
        data_type: double precision
        description: ""

      - name: cm_fee
        data_type: double precision
        description: ""

      - name: brokerage_fee
        data_type: double precision
        description: ""

      - name: vat_value
        data_type: double precision
        description: ""

      - name: fees_breakdown
        data_type: text
        description: ""

      - name: discount
        data_type: double precision
        description: ""

      - name: is_contract_note_sent
        data_type: boolean
        description: ""

      - name: processed_for_inventory
        data_type: boolean
        description: ""

      - name: trade_tenure
        data_type: timestamp with time zone
        description: ""

      - name: ovs_validation
        data_type: text
        description: ""

      - name: is_manually_matched
        data_type: boolean
        description: ""

      - name: contract_created_at
        data_type: timestamp with time zone
        description: ""

      - name: contract_updated_at
        data_type: timestamp with time zone
        description: ""

      - name: contract_price
        data_type: double precision
        description: ""

      - name: contract_tenure
        data_type: timestamp with time zone
        description: ""

      - name: contract_is_deleted
        data_type: boolean
        description: ""

      - name: contract_is_cancelled
        data_type: boolean
        description: ""

      - name: contract_delivery_status
        data_type: text
        description: ""

      - name: contract_buy_match_order_id
        data_type: bigint
        description: ""

      - name: security_location
        data_type: text
        description: ""

      - name: security_location_state
        data_type: text
        description: ""

      - name: contract_seller_location_code
        data_type: text
        description: ""

      - name: seller_region
        data_type: text
        description: ""

      - name: contract_buyer_location_code
        data_type: text
        description: ""

      - name: buyer_region
        data_type: text
        description: ""

      - name: logistic_differential
        data_type: double precision
        description: ""

      - name: contract_id
        data_type: text
        description: ""

      - name: delivery_id
        data_type: text
        description: ""

      - name: dispatch_created_at
        data_type: timestamp with time zone
        description: ""

      - name: dispatch_date
        data_type: timestamp with time zone
        description: ""

      - name: adjusted_dispatch_created_at
        data_type: date
        description: ""

      - name: dispatch_updated_at
        data_type: timestamp with time zone
        description: ""

      - name: dispatch_bag
        data_type: bigint
        description: ""

      - name: dispatch_is_deleted
        data_type: boolean
        description: ""

      - name: dispatch_is_reverted
        data_type: boolean
        description: ""

      - name: dispatch_is_buyer_pickup
        data_type: boolean
        description: ""

      - name: delivered_volume
        data_type: double precision
        description: ""

      - name: actual_delivered_volume
        data_type: double precision
        description: ""

      - name: dispatchlog_pk
        data_type: bigint
        description: ""

      - name: reverted_from_id
        data_type: bigint
        description: ""

      - name: initial_dispatched_volume
        data_type: double precision
        description: ""

      - name: dispatched_bags
        data_type: bigint
        description: ""

      - name: wb_phy_dispatch_weight
        data_type: double precision
        description: ""

      - name: wb_phy_dispatch_bags
        data_type: bigint
        description: ""

      - name: dispatch_id
        data_type: text
        description: ""

      - name: physical_dispatch_log_id
        data_type: text
        description: ""

      - name: is_settlement_confirmed
        data_type: boolean
        description: ""

      - name: delivery_company
        data_type: text
        description: ""

      - name: truck_number
        data_type: text
        description: ""

      - name: drivers_name
        data_type: text
        description: ""

      - name: drivers_phone
        data_type: text
        description: ""

      - name: dispatch_status
        data_type: text
        description: ""

      - name: warehouse_code
        data_type: text
        description: ""

      - name: warehouse_name
        data_type: text
        description: ""

      - name: wb_tenant_id
        data_type: bigint
        description: ""

      - name: initial_dispatch_id
        data_type: bigint
        description: ""

  # - name: fact_transactions
  #   description: ""
  #   columns:
  #     - name: actual_trade_value
  #       data_type: double precision
  #       description: ""

  #     - name: actual_trade_vol_mt
  #       data_type: double precision
  #       description: ""

  #     - name: order_id
  #       data_type: bigint
  #       description: ""

  #     - name: trade_creation_date
  #       data_type: timestamp with time zone
  #       description: ""

  #     - name: tid
  #       data_type: text
  #       description: ""

  #     - name: oms_provider_id
  #       data_type: bigint
  #       description: ""

  #     - name: trade_status
  #       data_type: text
  #       description: ""

  #     - name: ovs_validation
  #       data_type: text
  #       description: ""

  #     - name: trade_status_summary
  #       data_type: text
  #       description: ""

  #     - name: order_type
  #       data_type: text
  #       description: ""

  #     - name: is_on_behalf
  #       data_type: boolean
  #       description: ""

  #     - name: is_rejected
  #       data_type: boolean
  #       description: ""

  #     - name: is_deleted
  #       data_type: boolean
  #       description: ""

  #     - name: is_order_cancelled
  #       data_type: boolean
  #       description: ""

  #     - name: volume_per_unit
  #       data_type: bigint
  #       description: ""

  #     - name: calc_volume_per_unit
  #       data_type: bigint
  #       description: ""

  #     - name: trade_order_units
  #       data_type: bigint
  #       description: ""

  #     - name: trade_order_vol_mt
  #       data_type: double precision
  #       description: ""

  #     - name: trade_order_vol_kg
  #       data_type: bigint
  #       description: ""

  #     - name: matched_units
  #       data_type: bigint
  #       description: ""

  #     - name: matchedorder_units
  #       data_type: numeric
  #       description: ""

  #     - name: invoiced_units
  #       data_type: numeric
  #       description: ""

  #     - name: matchedorder_vol_mt
  #       data_type: double precision
  #       description: ""

  #     - name: matchedorder_vol_kg
  #       data_type: numeric
  #       description: ""

  #     - name: cancelled_units
  #       data_type: bigint
  #       description: ""

  #     - name: currency
  #       data_type: text
  #       description: ""

  #     - name: currency_code
  #       data_type: text
  #       description: ""

  #     - name: currency_sign
  #       data_type: text
  #       description: ""

  #     - name: order_price
  #       data_type: double precision
  #       description: ""

  #     - name: order_base_price
  #       data_type: double precision
  #       description: ""

  #     - name: complete_charged_fees
  #       data_type: text
  #       description: ""

  #     - name: fee_per_unit
  #       data_type: double precision
  #       description: ""

  #     - name: use_vat
  #       data_type: boolean
  #       description: ""

  #     - name: use_ecn_fees
  #       data_type: boolean
  #       description: ""

  #     - name: brokerage_fees_breakdown
  #       data_type: text
  #       description: ""

  #     - name: brokerage_fee_percent
  #       data_type: double precision
  #       description: ""

  #     - name: cm_fee
  #       data_type: double precision
  #       description: ""

  #     - name: sec_fee
  #       data_type: double precision
  #       description: ""

  #     - name: discount_type
  #       data_type: text
  #       description: ""

  #     - name: discount
  #       data_type: double precision
  #       description: ""

  #     - name: vat_value
  #       data_type: double precision
  #       description: ""

  #     - name: exchange_fee
  #       data_type: double precision
  #       description: ""

  #     - name: total_oms_fees
  #       data_type: double precision
  #       description: ""

  #     - name: total_afex_fees
  #       data_type: double precision
  #       description: ""

  #     - name: trade_value
  #       data_type: double precision
  #       description: ""

  #     - name: trade_value_with_fee
  #       data_type: double precision
  #       description: ""

  #     - name: calculated_trade_value
  #       data_type: double precision
  #       description: ""

  #     - name: otc_bags_delivered
  #       data_type: numeric
  #       description: ""

  #     - name: otc_volume_delivered
  #       data_type: double precision
  #       description: ""

  #     - name: otc_calculated_trade_value_delivered
  #       data_type: double precision
  #       description: ""

  #     - name: deduction_type
  #       data_type: text
  #       description: ""

  #     - name: deduction
  #       data_type: double precision
  #       description: ""

  #     - name: security_code
  #       data_type: text
  #       description: ""

  #     - name: security_name
  #       data_type: text
  #       description: ""

  #     - name: sec_security_type
  #       data_type: text
  #       description: ""

  #     - name: security_type
  #       data_type: text
  #       description: ""

  #     - name: board_type
  #       data_type: text
  #       description: ""

  #     - name: board_type_sec
  #       data_type: text
  #       description: ""

  #     - name: security_location_code
  #       data_type: text
  #       description: ""

  #     - name: client_creation_date
  #       data_type: timestamp with time zone
  #       description: ""

  #     - name: client_cid
  #       data_type: text
  #       description: ""

  #     - name: client_fullname
  #       data_type: text
  #       description: ""

  #     - name: client_account_type
  #       data_type: text
  #       description: ""

  #     - name: client_user_account_type
  #       data_type: text
  #       description: ""

  #     - name: client_email
  #       data_type: text
  #       description: ""

  #     - name: client_phone
  #       data_type: text
  #       description: ""

  #     - name: client_country
  #       data_type: text
  #       description: ""

  #     - name: client_country_code
  #       data_type: text
  #       description: ""

  #     - name: client_is_kyc_complete
  #       data_type: boolean
  #       description: ""

  #     - name: brokerage_community_id
  #       data_type: bigint
  #       description: ""

  #     - name: broker_community_code
  #       data_type: text
  #       description: ""

  #     - name: broker_community_name
  #       data_type: text
  #       description: ""

  #     - name: broker_community_city
  #       data_type: text
  #       description: ""

  #     - name: broker_community_state
  #       data_type: text
  #       description: ""

  #     - name: broker_community_is_viewable
  #       data_type: boolean
  #       description: ""

  #     - name: broker_community_is_approved
  #       data_type: boolean
  #       description: ""

  #     - name: broker_community_creation_date
  #       data_type: timestamp with time zone
  #       description: ""

  #     - name: broker_community_owner_id
  #       data_type: bigint
  #       description: ""

  #     - name: brocom_owner_cid
  #       data_type: text
  #       description: ""

  #     - name: brocom_owner_fullname
  #       data_type: text
  #       description: ""

  #     - name: brocom_owner_account_type
  #       data_type: text
  #       description: ""

  #     - name: brocom_owner_user_account_type
  #       data_type: text
  #       description: ""

  #     - name: brocom_owner_email
  #       data_type: text
  #       description: ""

  #     - name: brocom_owner_phone
  #       data_type: text
  #       description: ""

  #     - name: brocom_owner_country
  #       data_type: text
  #       description: ""

  #     - name: brocom_owner_country_code
  #       data_type: text
  #       description: ""

  #     - name: brocom_owner_is_kyc_complete
  #       data_type: boolean
  #       description: ""

  #     - name: brocom_owner_is_afex_broker
  #       data_type: boolean
  #       description: ""

  #     - name: promoter_community_id
  #       data_type: bigint
  #       description: ""

  #     - name: promoter_community_code
  #       data_type: text
  #       description: ""

  #     - name: promoter_community_name
  #       data_type: text
  #       description: ""

  #     - name: promoter_community_city
  #       data_type: text
  #       description: ""

  #     - name: promoter_community_state
  #       data_type: text
  #       description: ""

  #     - name: promoter_community_is_viewable
  #       data_type: boolean
  #       description: ""

  #     - name: promoter_community_is_approved
  #       data_type: boolean
  #       description: ""

  #     - name: promoter_community_creation_date
  #       data_type: timestamp with time zone
  #       description: ""

  #     - name: promoter_community_owner_id
  #       data_type: bigint
  #       description: ""

  #     - name: procom_owner_cid
  #       data_type: text
  #       description: ""

  #     - name: procom_owner_fullname
  #       data_type: text
  #       description: ""

  #     - name: procom_owner_account_type
  #       data_type: text
  #       description: ""

  #     - name: procom_owner_user_account_type
  #       data_type: text
  #       description: ""

  #     - name: procom_owner_email
  #       data_type: text
  #       description: ""

  #     - name: procom_owner_phone
  #       data_type: text
  #       description: ""

  #     - name: procom_owner_country
  #       data_type: text
  #       description: ""

  #     - name: procom_owner_country_code
  #       data_type: text
  #       description: ""

  #     - name: procom_owner_is_kyc_complete
  #       data_type: boolean
  #       description: ""

  #     - name: procom_owner_is_afex_broker
  #       data_type: boolean
  #       description: ""

  #     - name: trade_creator_type
  #       data_type: text
  #       description: ""

  #     - name: creator_cid
  #       data_type: text
  #       description: ""

  #     - name: creator_fullname
  #       data_type: text
  #       description: ""

  #     - name: creator_account_type
  #       data_type: text
  #       description: ""

  #     - name: creator_user_account_type
  #       data_type: text
  #       description: ""

  #     - name: creator_email
  #       data_type: text
  #       description: ""

  #     - name: creator_phone
  #       data_type: text
  #       description: ""

  #     - name: creator_country
  #       data_type: text
  #       description: ""

  #     - name: creator_country_code
  #       data_type: text
  #       description: ""

  #     - name: creator_is_kyc_complete
  #       data_type: boolean
  #       description: ""

  #     - name: creator_is_afex_broker
  #       data_type: boolean
  #       description: ""
