from datetime import datetime, timedelta

from airflow import DAG
from airflow.operators.bash import BashOperator


default_args = {
    'owner': 'Muhammad <PERSON>',
    'retries': 1,
    'retry_delay': timedelta(seconds=2),
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': True
}

with DAG(dag_id="dag_email_notification_v05",
        start_date= datetime(2024,6,20),
        schedule_interval = '@daily',
        default_args = default_args,
        catchup= False  ) as dag:
    task1 = BashOperator(
        task_id= 'simple_failed_bash_task',
        bash_command="cd non_exist_folder"
            )

dag
