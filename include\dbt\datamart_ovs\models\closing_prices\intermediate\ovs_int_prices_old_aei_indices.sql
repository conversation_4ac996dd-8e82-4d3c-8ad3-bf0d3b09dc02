{{ config (materialized = 'ephemeral') }}

with
old_aei_only_commodities as (
	-- Intermediate (int_old_aei_indices) 

	select date
			, commodity
			, price_bags * 10 price_mt
			, price_bags / 100 price_kg
			, index_points sub_index
			, concat(extract(year from date::timestamp), '_', extract(week from date::timestamp)) woy
	
	from {{ ref ('ovs_stg_old_closingpriceindices') }}
	
	where commodity in ('Cocoa', 'Ginger')
	),

old_aei_values as (
	select date
			, index_points aei
	
	from {{ ref ('ovs_stg_old_closingpriceindices') }}
	
	where commodity in ('AEI')
	),

old_aei_with_commodities as (
	select case when extract(month from comm.date) >= 10 
					then concat(extract(year from comm.date)::text, '/', extract(year from comm.date + INTERVAL '1 year')::text)
				else concat(extract(year from comm.date - INTERVAL '1 year')::text, '/', extract(year from comm.date)::text)
				end season
			, comm.date
			, case when comm.commodity = 'Cocoa' then 'Cocoa'
					when comm.commodity = 'Ginger' then 'Ginger Dried Split'
				end commodity
			, case when comm.commodity = 'Cocoa' then 'CCO'
					when comm.commodity = 'Ginger' then 'GNG'
				end commodity_code
			, comm.price_kg
			, comm.price_mt
			, comm.sub_index
			, null::double precision aei_index
			, aei.aei
			, case when extract(dow from comm.date::timestamp) = 0 then 
					lead(comm.woy, 1) over (partition by comm.commodity order by comm.date)
				when extract(dow from comm.date::timestamp) = 6 then 
					lead(comm.woy, 2) over (partition by comm.commodity order by comm.date)
			else comm.woy
			end adj_woy
	
	from old_aei_only_commodities comm
	left join old_aei_values aei
		on comm.date = aei.date
	)

    select *

    from old_aei_with_commodities