{% docs wb_fact_loan_breakdown_id %}
Unique identifier of the loan breakdown record.
{% enddocs %}

{% docs wb_fact_loan_breakdown_created %}
Timestamp indicating when the loan breakdown record was created.
{% enddocs %}

{% docs wb_fact_loan_breakdown_updated %}
Timestamp indicating the last update time for this loan breakdown record.
{% enddocs %}

{% docs wb_fact_loan_breakdown_maturity_date %}
The maturity date of the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_farmer_id %}
Unique identifier of the farmer associated with the loan breakdown (reference to wb_dim_farmer.id).
{% enddocs %}

{% docs wb_fact_loan_breakdown_project_id %}
Unique identifier of the project associated with the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_warehouse_id %}
Unique identifier of the warehouse associated with the loan breakdown (reference to dim_warehouse.id).
{% enddocs %}

{% docs wb_fact_loan_breakdown_item_id %}
Unique identifier of the item associated with the loan breakdown (reference to dim_item.id).
{% enddocs %}

{% docs wb_fact_loan_breakdown_tenant_id %}
Unique identifier of the tenant associated with the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_ln_id %}
The loan ID associated with the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_line_id %}
The line ID associated with the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_hectare %}
The area of the farm in hectares for which the loan breakdown is provided.
{% enddocs %}

{% docs wb_fact_loan_breakdown_units %}
The number of units associated with the loan.
{% enddocs %}

{% docs wb_fact_loan_breakdown_unit_price %}
The unit price associated with the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_total_price %}
The total price associated with the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_total_loan_value %}
The total value of the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_repayment_value %}
The repayment value associated with the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_amount_repaid %}
The amount repaid for the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_insurance %}
The insurance amount associated with the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_crg %}
The credit risk guarantee amount for the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_interest %}
The interest amount associated with the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_admin_fee %}
The administrative fee associated with the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_equity %}
The equity amount associated with the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_to_balance %}
The balance amount remaining on the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_loan_status %}
The status of the loan breakdown ('Not owing', 'Overage', 'Is owing').
{% enddocs %}

{% docs wb_fact_loan_breakdown_data_identification_verification %}
The verification status for data identification in the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_value_chain_management %}
The value chain management status in the loan breakdown.
{% enddocs %}

{% docs wb_fact_loan_breakdown_is_repaid %}
A flag indicating whether the loan breakdown is fully repaid.
{% enddocs %}

{% docs wb_fact_loan_breakdown_is_approved %}
A flag indicating whether the loan breakdown is approved.
{% enddocs %}

{% docs wb_fact_loan_breakdown_is_approval_completed %}
A flag indicating whether the approval process for the loan breakdown is completed.
{% enddocs %}

{% docs wb_fact_loan_breakdown_is_rejected %}
A flag indicating whether the loan breakdown is rejected.
{% enddocs %}

{% docs wb_fact_loan_breakdown_is_reverted %}
A flag indicating whether the loan breakdown status is reverted.
{% enddocs %}
