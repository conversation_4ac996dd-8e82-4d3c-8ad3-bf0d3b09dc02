from datetime import datetime,timedelta
from airflow import DAG
from airflow.operators.bash  import BashOperator
from custom_functions.airflow_email_util import failure_email


PATH_TO_DBT_VENV = "/usr/local/airflow/dbt_venv/bin/activate"
PATH_TO_DBT_PROJECT = "/usr/local/airflow/include/dbt/sherlock"
PATH_TO_DBT_PROFILE = "/usr/local/airflow/include/dbt/.dbt"


default_args  = {
     'owner' : 'Oluwatomisin Soetan',
     'retries' : 0,
     'retry_delay' : timedelta(minutes=2),
     'on_failure_callback': failure_email,
}

with DAG(
    dag_id = 'dbt_sherlock_dag',
    default_args = default_args,
    description = 'sherlock',
    start_date=datetime(2023, 10, 11),
    catchup=False,
    schedule_interval =  '0 12 * * *',
    tags =  ['dbt','sherlock']
) as dag:
    
    task1 =  BashOperator(
        task_id = 'dbt_deps__dbt_run',
        bash_command = 'source $PATH_TO_DBT_VENV  && export DBT_PROFILES_DIR=$PATH_TO_DBT_PROFILE && dbt deps && dbt run', 
        cwd=PATH_TO_DBT_PROJECT,
        env={"PATH_TO_DBT_VENV": PATH_TO_DBT_VENV,"PATH_TO_DBT_PROFILE":PATH_TO_DBT_PROFILE},
    )
   
task1
