

{{ config(materialized='table',
	indexes=[
      {'columns': ['farmer_id']},
      {'columns': ['warehouse_id']},
    ]
) }}

with final as (select loan.id,loan.created,loan.updated,bundle.name loan_bundlename,loan.created_offline,loan.approval_date,loan.farmer_id,
		loan.project_id,project.created project_start_date,project.name project_name,project.code project_code,project.maturity_date,loan.warehouse_id,
		wh.name warehouse_name,
		loan.data_identification_verification,
		loan.tenant_id,loan.ln_id,loan.hectare,
		loan.total_loan_value,
		loan.repayment_value,loan.amount_repaid,loan.insurance,loan.crg,loan.interest,loan.admin_fee,loan.equity,
		(CASE when loan.repayment_value > loan.amount_repaid then (loan.repayment_value - loan.amount_repaid)
		when (loan.repayment_value < loan.amount_repaid) then (loan.amount_repaid  - loan.repayment_value ) 	else 0 end) to_balance,
		(CASE when loan.repayment_value > loan.amount_repaid then 'Is owing' 
		when (loan.repayment_value < loan.amount_repaid) then 'Overage' else 'Not owing' end) loan_status,
		loan.is_repaid,
		loan.is_approved,loan.is_approval_completed,loan.is_rejected,loan.is_reverted
from   {{source('workbench','loan_loan')}}   loan
inner join {{source('workbench','project_project')}}   project
on loan.project_id = project.id
left join {{source('workbench','loan_loanbundle')}}  bundle
on loan.loan_bundle_id = bundle.id
left join {{source('workbench','workbench_warehouse')}}  wh
on loan.warehouse_id = wh.id
)

select * from final 

    