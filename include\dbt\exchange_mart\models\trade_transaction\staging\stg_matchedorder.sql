{{ config ( materialized = 'ephemeral')}}

with
match_ as (
	select 	id,
			matched_id,
			order_id,
			matched_with_order_id,
			created,
			updated,
			processed_on,
			total_order_price ,
			total_order_price_with_fees ,
			total_afex_fees ,
			total_oms_fees ,
			sec_fee ,
			exchange_fee ,
			cm_fee ,
			vat_value ,
			location_breakdown,
			matched_units,
			invoiced_units,
			matched_price,
			client_id,
			is_contract_note_sent,
			is_audit_cancelled ,
			processed_for_inventory ,
			is_deleted,
			discount
 	from  {{ source ('ovs','trade_matchedorder') }} match_
),

order1_ as (
	select	req.id order_id,
			req.tid tid1,
			req.order_type order_type1,
			req.volume_per_unit,
			req.brokerage_fee,
			req.board_type,
			req.security_id,
			req.client_id client_id1,
			-- oms.oms_code,
			-- oms.name oms_name,
			req.units order_units,
			req.order_price,
			req.security_location_code

		from {{ ref ('stg_clientorderrequest') }} req
		-- left join   {{ source ('ovs','crm_omsprovider') }} oms
		-- on req.oms_provider_id = oms.id
	
),

order2_ as (
	select	id order_id,
			tid tid2,
			order_type order_type2,
			client_id client_id2
	from  {{ ref ('stg_clientorderrequest') }}
	
),

match_order1 as (
	select * 
	from match_
	left join order1_
	on match_.order_id = order1_.order_id
),

match_order2 as (
	select *
	from match_
	left join order2_
	on match_.matched_with_order_id = order2_.order_id
),


match_order12_join as (
	select 	case when match_order1.order_type1 = 'Buy' then match_order1.tid1 else match_order2.tid2 end buy_tid, 
			case when match_order1.order_type1 = 'Sell' then match_order1.tid1 else match_order2.tid2 end sell_tid ,
			case when match_order1.order_type1 = 'Buy' then match_order1.client_id1 else match_order2.client_id2 end buyer_client_id, 
			case when match_order1.order_type1 = 'Sell' then match_order1.client_id1 else match_order2.client_id2 end seller_client_id ,
			match_order1.*
	
	from match_order1
	inner join match_order2
	on match_order1.matched_id = match_order2.matched_id
),	

final as (
	select 	match_order12_join.id,
			match_order12_join.created,
			match_order12_join.updated,
			match_order12_join.processed_on,
			match_order12_join.tid1 as tid,
			match_order12_join.matched_id,
			match_order12_join.buy_tid,
			match_order12_join.sell_tid,
			buy_client.cid buyer_cid,
			sell_client.cid seller_cid,
			match_order12_join.order_units,
			match_order12_join.order_price,
			match_order12_join.matched_units,
			match_order12_join.matched_price,
			match_order12_join.invoiced_units,
			match_order12_join.volume_per_unit,
			case 
				when match_order12_join.volume_per_unit = 0 then 1
				else match_order12_join.volume_per_unit
			end as calc_volume_per_unit,
			match_order12_join.security_location_code,
			match_order12_join.order_type1 order_type,
			match_order12_join.total_order_price ,
			match_order12_join.total_order_price_with_fees ,
			match_order12_join.total_afex_fees ,
			match_order12_join.total_oms_fees ,
			match_order12_join.sec_fee ,
			match_order12_join.exchange_fee ,
			match_order12_join.cm_fee ,
			match_order12_join.brokerage_fee,
			match_order12_join.vat_value ,
			match_order12_join.location_breakdown,
			match_order12_join.is_contract_note_sent,
			match_order12_join.is_audit_cancelled ,
			match_order12_join.processed_for_inventory ,
			match_order12_join.is_deleted,
			match_order12_join.discount
	from match_order12_join
	left join {{source('ovs','crm_client')}} buy_client
	on match_order12_join.buyer_client_id =  buy_client.id
	left join {{source('ovs','crm_client')}} sell_client
	on match_order12_join.seller_client_id = sell_client.id
			
)
select 
		id,
		created,
		updated,
		processed_on,
		tid,
		matched_id,
		buy_tid,
		sell_tid,
        buyer_cid,
		seller_cid,
		order_units,
		order_price,
		matched_units,
		matched_price,
		invoiced_units,
		volume_per_unit,
		calc_volume_per_unit,
		security_location_code,
		order_type,
		total_order_price ,
		total_order_price_with_fees ,
		total_afex_fees ,
		total_oms_fees ,
		sec_fee ,
		exchange_fee ,
		cm_fee ,
		brokerage_fee,
		vat_value ,
		location_breakdown,
		is_contract_note_sent,
		is_audit_cancelled ,
		processed_for_inventory ,
		is_deleted,
		discount
from  

	final 