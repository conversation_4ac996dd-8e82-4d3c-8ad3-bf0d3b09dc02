version: 2

sources:
  - name: comx
    description : comx data
    tables:
      - name: crm_client
      - name: trading_trade
      - name: crm_community
      - name: crm_security
      - name: trading_matchorder
      - name: account_country
      - name: crm_currency
      - name: crm_board
      - name: crm_commodity
      - name: collateral_mgmt_loaninformation
      - name: collateral_mgmt_loansecurity
      - name: crm_location
      - name: collateral_mgmt_loantranche
      - name: collateral_mgmt_loan

  - name: ovs
    description : ovs data
    tables:
      - name: trade_clientorderrequest


models:
  - name: cx_dim_client
    description: " This table contains records on clients' personal details, including registration and contact information for the comx platform"
    columns:
      - name: id
        description: "{{doc('cx_dim_client_id')}}"
      - name: cid
        description: "{{doc('cx_dim_client_cid')}}"
      - name: rnb
        description: "{{doc('cx_dim_client_rnb')}}"
      - name: email
        description: "{{doc('cx_dim_client_email')}}"
      - name: phone
        description: "{{doc('cx_dim_client_phone')}}"
      - name: address
        description: "{{doc('cx_dim_client_address')}}"
      - name: created
        description: "{{doc('cx_dim_client_created')}}"
      - name: updated
        description: "{{doc('cx_dim_client_updated')}}"
      - name: last_name
        description: "{{doc('cx_dim_client_last_name')}}"
      - name: first_name
        description: "{{doc('cx_dim_client_first_name')}}"
      - name: share_code
        description: "{{doc('cx_dim_client_share_code')}}"
      - name: is_approved
        description: "{{doc('cx_dim_client_is_approved')}}"
      - name: account_type
        description: "{{doc('cx_dim_client_account_type')}}"
      - name: country
        description: "{{doc('cx_dim_client_country')}}"
      - name: is_certified
        description: "{{doc('cx_dim_client_is_certified')}}"
      - name: is_synced_wb
        description: "{{doc('cx_dim_client_is_synced_wb')}}"
      - name: referral_code
        description: "{{doc('cx_dim_client_referral_code')}}"
      - name: verify_me_dob
        description: "{{doc('cx_dim_client_verify_me_dob')}}"
      - name: is_afex_broker
        description: "{{doc('cx_dim_client_is_afex_broker')}}"
      - name: is_id_verified
        description: "{{doc('cx_dim_client_is_id_verified')}}"
      - name: is_bvn_verified
        description: "{{doc('cx_dim_client_is_bvn_verified')}}"
      - name: is_kyc_complete
        description: "{{doc('cx_dim_client_is_kyc_complete')}}"
      - name: is_kyc_rejected
        description: "{{doc('cx_dim_client_is_kyc_rejected')}}"
      - name: is_kyc_verified
        description: "{{doc('cx_dim_client_is_kyc_verified')}}"
      - name: client_broker_id
        description: "{{doc('cx_dim_client_client_broker_id')}}"
      - name: is_kyc_submitted
        description: "{{doc('cx_dim_client_is_kyc_submitted')}}"
      - name: is_update_pending
        description: "{{doc('cx_dim_client_is_update_pending')}}"
      - name: user_account_type
        description: "{{doc('cx_dim_client_user_account_type')}}"
      - name: bvn_error_response
        description: "{{doc('cx_dim_client_bvn_error_response')}}"
      - name: used_referral_code
        description: "{{doc('cx_dim_client_used_referral_code')}}"
      - name: verify_me_lastname
        description: "{{doc('cx_dim_client_verify_me_lastname')}}"
      - name: verify_me_firstname
        description: "{{doc('cx_dim_client_verify_me_firstname')}}"
      - name: verify_me_middlename
        description: "{{doc('cx_dim_client_verify_me_middlename')}}"
      - name: id_verification_message
        description: "{{doc('cx_dim_client_id_verification_message')}}"
      - name: is_kyc_pending_approval
        description: "{{doc('cx_dim_client_is_kyc_pending_approval')}}"
      - name: bvn_verification_message
        description: "{{doc('cx_dim_client_bvn_verification_message')}}"
      - name: bank_verification_message
        description: "{{doc('cx_dim_client_bank_verification_message')}}"
      - name: is_brokerage_bank_account_verified
        description: "{{doc('cx_dim_client_is_brokerage_bank_account_verified')}}"
      - name: region
        description: "{{doc('cx_dim_client_region')}}"
      - name: subregion
        description: "{{doc('cx_dim_client_subregion')}}"
