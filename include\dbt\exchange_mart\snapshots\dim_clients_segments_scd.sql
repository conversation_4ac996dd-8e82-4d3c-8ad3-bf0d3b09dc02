{% snapshot dim_clients_segments_scd %}

{{
    config(
        target_schema='exchange_mart_snapshots',
        strategy='timestamp',
        unique_key='cid',
        updated_at='active_status',
        incremental_predicates=['dbt_valid_to is null']
        )
}}

SELECT 
    cid,
    last_seen,
    time_since_last_seen,
    last_action_time,
    last_action_done,
    time_since_last_action,
    active_status,
    best_notification_hour,
    activity_hour_frequency,
    avg_online_duration_within_hour,
    boards,
    commodities

FROM {{ ref('dim_clients_segments') }}

{% endsnapshot %}