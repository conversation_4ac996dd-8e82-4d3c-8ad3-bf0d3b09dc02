{{ config(materialized='table') }}

with country as (
    select  trim(both  'b\''' FROM name) country,
            trim(both  'b\''' FROM alpha2code) code,
            trim(both  'b\''' FROM region) region,
            trim(both  'b\''' FROM subregion) subregion
    from {{source('comx','account_country')}}         
),

a as (
    select client.*,
            country.country,
            country.region,
            country.subregion
    from {{source('comx','crm_client')}} client
    left join country
    on client.country_code = country.code
    

),
final as (
     select *
     from a
     where is_approved = 'true'

)

select * from final
