{{ config (materialized = 'ephemeral') }}

with
old_aci_only_commodities as (
	-- Intermediate (int_old_aci_indices) 

	select date
			, commodity
			, price_bags * 10 price_mt
			, price_bags / 100 price_kg
			, index_points sub_index
			, concat(extract(year from date::timestamp), '_', extract(week from date::timestamp)) woy
	
	from {{ ref ('ovs_stg_old_closingpriceindices') }}
	
	where commodity in ('Maize', 'Paddy Rice', 'Sorghum', 'Soyabean')
	),

old_aci_values as (
	select date
			, index_points aci
	
	from {{ ref ('ovs_stg_old_closingpriceindices') }}
	
	where commodity in ('ACI')
	),

old_aci_with_commodities as (
	select case when extract(month from comm.date) >= 10 
					then concat(extract(year from comm.date)::text, '/', extract(year from comm.date + INTERVAL '1 year')::text)
				else concat(extract(year from comm.date - INTERVAL '1 year')::text, '/', extract(year from comm.date)::text)
				end season
			, comm.date
			, case when comm.commodity = 'Sorghum' then 'Sorghum'
					when comm.commodity = 'Soyabean' then 'Soybean'
					when comm.commodity = 'Paddy Rice' then 'Paddy Rice Long Grain'
					when comm.commodity = 'Maize' then 'Maize Feed Grade - White'
				end commodity
			, case when comm.commodity = 'Sorghum' then 'SGM'
					when comm.commodity = 'Soyabean' then 'SBS'
					when comm.commodity = 'Paddy Rice' then 'PRL'
					when comm.commodity = 'Maize' then 'MAZ'
				end commodity_code
			, comm.price_kg
			, comm.price_mt
			, comm.sub_index
			, null::double precision aci_index
			, aci.aci
			, case when extract(dow from comm.date::timestamp) = 0 then 
					lead(comm.woy, 1) over (partition by comm.commodity order by comm.date)
				when extract(dow from comm.date::timestamp) = 6 then 
					lead(comm.woy, 2) over (partition by comm.commodity order by comm.date)
			else comm.woy
			end adj_woy
	
	from old_aci_only_commodities comm
	left join old_aci_values aci
		on comm.date = aci.date
	)

    select *

    from old_aci_with_commodities