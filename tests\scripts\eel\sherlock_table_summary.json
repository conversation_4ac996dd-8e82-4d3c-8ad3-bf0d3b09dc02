{"wb_fact_warehouse_dailyinventorybalance": "wb_fact_warehouse_dailyinventorybalance` table contains comprehensive records of stock held in warehouses, with entries stored daily since the system's inception. It includes detailed information about each inventory transaction, such as the date of recording, the warehouse and item identifiers, and the quality and quantity of items. The table tracks both the gross and net weights of the inventory items, before and after each transaction, and includes details about the source, creation and update timestamps, and the tenant associated with each record. Additionally, it captures the status of items under lien and provides flags for deleted records. The table also specifies the type of inventory transaction, such as debits, credits, and liens, offering a robust historical log for inventory management\nThis table is associated with the following: - **Inventory Position**: The total unliened volume of commodity available at each warehouse at the end of the specified period.\n", "wb_dim_crop": "The `wb_dim_crop` table provides detailed information about crops planted by farmers. This standalone table does not join with any other tables in the database. It records essential data such as the warehouse identifier, the registration timestamp of the farmer, the farmer's unique identifier, and details about the crops including their code and name. This table is crucial for tracking crop-related activities and identifying the crops associated with specific farmers and warehouses.", "wb_dim_item": "The `wb_dim_item` table stores detailed information about items for which goods received notes (GRN) and loans are raised. Each record in this table includes unique identifiers, timestamps for creation and updates, and various attributes related to the item. These attributes encompass the item name, code, tenant association, product type, and deduction values for different grades of the item. This table plays a crucial role in managing and categorizing items within the warehouse management and loan processing systems.", "wb_dim_client": "The `wb_dim_client` table contains personal details and contact information for clients on the Workbench platform. It is important to note that clients in this table are distinct from those in the `cx_dim_client` table. The table includes a wide array of client-specific data, such as unique identifiers, bank verification numbers (BVN), contact details (email, phone, address), identification types and statuses, and various flags indicating the client's activity and verification statuses. Additionally, it records timestamps for the creation, updates, and restoration of client records. This table is essential for maintaining a comprehensive profile of each client, ensuring accurate and up-to-date personal information for operational and communication purposes.\nThis table is associated with the following: - **AFTL Clients**: The distinct count of clients registered on the workbench platform.\n", "wb_dim_clientbank": "wb_dim_clientbank` table contains detailed bank information for clients on the Workbench platform. Each record in this table includes unique identifiers, bank verification numbers (BVNs), account numbers, and details about the banks such as names, codes, and countries. It also tracks the creation and update timestamps for each record, and records the association of each bank account with specific clients and tenants. The table includes various flags indicating the status of the bank account, such as whether it is preferred, approved, rejected, reverted, or verified. Additionally, it logs the approval and rejection dates, reasons for reversion or rejection, and identifies the users involved in these actions. This table is essential for managing and verifying the bank accounts of clients, ensuring accurate and secure financial transactions.", "wb_fact_loan": "The `wb_fact_loan` table stores records of loans issued to clients. It includes comprehensive details about each loan, such as unique identifiers, creation and update timestamps, and information about the associated farmer and project. Each loan record captures details like the project name, code, start date, and associated warehouse. Financial aspects of the loan are meticulously tracked, including total loan value, repayment value, amount repaid, insurance, credit risk guarantee (CRG), interest, administrative fees, equity, and the remaining balance. The table also contains flags to indicate the loan's status, such as whether it is fully repaid, approved, rejected, or reverted. This table plays a crucial role in monitoring the lifecycle and status of loans, ensuring precise tracking and management of financial obligations and repayments.\nThis table is associated with the following: - **Active Farmers**: Registered AFTL farmers on workbench who have engaged in GRN or loan transactions during the specified period, this could be year or season.\n - **Hectares for Input Loan **: The total area of land, in hectares, financed for each farmer's fair trade loans that are fully approved and not reverted.\n - **Total Amount Repaid**: The total monetary value repaid on loans.\n - **Total Loan Value**: The total monetary value of loans disbursed.\n - **Total Repayment Value**: The total monetary value of loans disbursed plus interest.\n - **Total AFTL Loan Transactions**: The distinct count of unique workbench LOAN IDs.\n", "tr_fact_clientwalletlog": "tr_fact_clientwalletlog` table maintains a comprehensive record of clients' wallet balances, capturing daily transactions since the system's inception. Each log entry includes the date and time of the transaction, the client's wallet identifier, transaction amount, and reference ID. It also records timestamps for the creation and last update of the log entry. Additional details captured include the agent involved, the currency used, and any extra notes or comments. The table tracks the lien amounts and total wallet amounts before and after transactions, along with the available balance changes. It flags entries as deleted when necessary and specifies the type of wallet transaction, such as credits, debits, liens, and adjustments. This table is essential for auditing and monitoring the financial activities within client wallets over time.", "tr_fact_clientwallet": "The `tr_fact_clientwallet` table provides a snapshot of the current position of client wallets for only the present day. Each record includes unique identifiers for the client wallet, timestamps for creation and updates, and detailed balance information. This includes the total balance, available balance, lien balance, cash advance limits, and the amounts spent and remaining from the cash advance. The table also contains information about the currency used in the wallet, identified by both name and code, as well as details about the associated Order Management System (OMS). Flags are used to indicate if a wallet record is marked as deleted. This table is crucial for real-time monitoring and management of client wallet positions.\nThis table is associated with the following: - **Client Wallet Balance**: The total unliened amount available in each client's wallet at the end of the specified period.\n", "wb_fact_loan_breakdown": "wb_fact_loan_breakdown table provides a detailed breakdown of loan components, expanding upon the data contained in the `wb_fact_loan` table. Each entry includes a unique identifier, timestamps for creation and updates, and the maturity date of the loan breakdown. It tracks the specific farmer, project, warehouse, and item associated with the loan, each identified by unique identifiers. The table includes critical financial details such as the total and unit prices, total loan value, repayment value, amounts repaid, insurance, credit risk guarantee (CRG), interest, administrative fees, equity, and remaining balance. It also records the loan status and various verification statuses. Flags indicate if the loan breakdown is repaid, approved, approval completed, rejected, or reverted. This table is vital for a comprehensive understanding and management of loan disbursements and repayments.\nThis table is associated with the following: - **Total Amount Repaid**: The total monetary value repaid on loans.\n - **Total Loan Value**: The total monetary value of loans disbursed.\n - **Total Repayment Value**: The total monetary value of loans disbursed plus interest.\n", "wb_dim_warehouse": "The `wb_dim_warehouse` table contains detailed information about warehouses, including their names, locations, capacities, and associated tenants or customers. Each warehouse is identified by a unique identifier and includes timestamps for creation and updates. The table records the specific location and address of each warehouse, including geographic coordinates (longitude and latitude). It also includes identifiers and names for the location, state, region, country, and continent where the warehouse is situated. Additional details include the tenant associated with the warehouse, the warehouse manager, and contact information such as the warehouse email. This table is essential for managing and tracking warehouse data and their geographical distribution, and it can be used to determine the location of associated farmers based on the warehouse they belong to.", "cx_dim_client": "The cx_dim_client table holds records of clients' personal details on the comx platform, including registration and contact details. This information does not pertain to the workbench platform. The table includes unique identifiers, timestamps, and various attributes such as the client's email, phone, address, and account type. It also tracks verification statuses for KYC, BVN, and identity, along with broker associations and referral codes. This table is essential for managing client information and ensuring compliance with regulatory requirements.\nThis table is associated with the following: - **Exchange Clients**: The distinct count of clients registered on the africa exchange platform.\n", "wb_dim_farmer": "The 'wb_dim_farmer' table stores comprehensive details about all farmers associated with AFEX. Each record includes information such as the farmer's personal identifiers (like name, gender, and marital status), contact details (phone number and address), agricultural specifics (farm size and crop details), and affiliations (cooperatives and field extension officers [FEOs] ). Additionally, it tracks administrative data such as creation and update timestamps, deletion status, and associations with client and warehouse identifiers for logistical purposes. This table contains information about FEOs (Field Extension Officers)\nThis table is associated with the following: - **Number of Farmers Reached **: The distinct count of registered farmers.\n", "tr_fact_matchedorder": "captures detailed records of trade transactions, encompassing the volume and value of both buy and sell activities since inception. Each record is timestamped with creation ('tr_fact_matchedorder.created') and last update ('tr_fact_matchedorder.updated') times, providing insights into transaction timing. Key identifiers such as 'tr_fact_matchedorder.tid' and 'tr_fact_matchedorder.matched_id' distinguish individual trade and matched order IDs, crucial for tracking transaction history. The table includes essential transaction specifics such as buyer and seller details ('tr_fact_matchedorder.buyer_cid,' 'tr_fact_matchedorder.seller_cid'), alongside order characteristics like units ('tr_fact_matchedorder.order_units'), unit prices ('tr_fact_matchedorder.order_price'), and matched volumes ('tr_fact_matchedorder.matched_units'). Financial elements such as fees ('tr_fact_matchedorder.exchange_fee' 'tr_fact_matchedorder.brokerage_fee') and VAT ('tr_fact_matchedorder.vat_value') complement transactional data, ensuring comprehensive financial analysis. Overall, this table serves as a vital repository for analyzing trade performance and financial metrics within the specified trading environment.\nThis table is associated with the following: - **FI Sales Value**: The total transaction value of 'Buy' Matched Fixed Income units sold by Exchange Issuance.\n - **FI Units Issued**: The total units of 'Sell' Fixed Income orders introduced to the exchange by Exchange Issuance.\n - **FI Units Sold**: The total units of 'Buy' Matched Fixed Income units bought, which were sold by Exchange Issuance.\n - **Matched Volume **: The product of matched units and volume per unit, considering the unit conversion for different types of securities.\n - **Volume Sold **: The sum of volume of commodities (OTC, Dawa, and Spot) sold on the exchange by AFTL NG and Physical Market's Clients, for Buy orders only.\n - **Weighted Aggregation Cost**: The calculated average cost of purchased commodities based on the total volume purchased.\n", "wb_fact_grn": "The table 'wb_fact_grn' catalogs detailed records of Goods Receipt Note (GRN) transactions, encompassing various aspects of goods reception and transactional details. Each GRN record is uniquely identified by 'wb_fact_grn.grn_id' and includes timestamps for creation ('wb_fact_grn.created') and updates ('wb_fact_grn.updated'), providing a timeline of GRN processing. Key attributes such as 'wb_fact_grn.bags' (number of bags), 'wb_fact_grn.net_weight' (net weight), and 'wb_fact_grn.moisture' (moisture percentage) detail physical characteristics of received goods. Financial aspects like 'wb_fact_grn.total_commodity_price' and 'wb_fact_grn.transaction_fees' offer insights into transactional costs associated with each GRN. Operational flags such as 'wb_fact_grn.is_approved,' 'wb_fact_grn.is_processed,' and 'wb_fact_grn.is_traded' provide operational status indicators, facilitating efficient management of inventory and transaction processing.\nThis table is associated with the following: - **Active AFTL Clients**: Registered AFTL clients on workbench who have engaged in GRN transactions during the specified period, this could be year or season, etc.\n - **Active Farmers**: Registered AFTL farmers on workbench who have engaged in GRN or loan transactions during the specified period, this could be year or season.\n - **Aggregation Cost**: This is a direct cost which is the transaction value paid to farmers and clients for GRN transactions, not accounting for logistics cost.\n - **Total GRN Transactions**: The distinct count of unique GRN IDs.\n - **Volume Aggregated **: The total weight of all approved GRN transactions on WorkBench related to loan repayment, storage, storage to trade, trade, and broker payment, excluding reverted transactions.\n", "tr_fact_transactions": "serves as a comprehensive repository for transaction details within the AFEX trading system. It meticulously captures various aspects of each transaction, including financial specifics, order details, trade identifiers, and client information. Each transaction record includes essential fields such as actual trade values and volumes, order IDs, creation timestamps, and unique identifiers for different entities involved in the transaction process. Notably, it tracks trade statuses, validation statuses within external systems like OVS, and summarizes trade status updates. Additional attributes cover transaction types, flags indicating trade status (e.g., rejected, deleted), and detailed metrics such as trade volumes in both metric tons and kilograms. Financial components are extensively logged, including order prices, fees, discounts, and taxes, with breakdowns provided for each transaction's fee structure. Moreover, the table captures client-specific information, including client identifiers, account types, contact details, and KYC completion statuses. This detailed transactional data is crucial for auditing, performance analysis, and regulatory compliance within the AFEX trading environment, offering comprehensive insights into trade activities and client interactions.\nThis table is associated with the following: - **Active Exchange Clients**: Clients registered on the exchnage who have engaged in buy or sell transactions on the exchange, and or wallet withdrawal or deposit, and or cia to csd conversion transactions during the specified period, this could be year or season, etc.\n - **Delivered Volume**: The weight of OTC security logged by the logistics officer as delivered, measured in metric tons or 100 kilograms. trade_mart.fact_trade_individual_transactions where security_type = 'OTC' and execution_date is not null\n - **Market Turnover **: The sum of the product of price and quantity for all non-OTC buy transactions, excluding transactions involving exchange issuance. Assumes all exchange issuance transactions equate to FI issuance and buy backs.\n - **Total Trade Transactions**: The distinct count of unique buyer's MATCHED IDs.\n - **Transaction Value / Order Price**: The product of price and quantity for each transaction.\n - **Transaction Value with Fees / Order Price with Fees**: The total transaction value including all associated fees.\n - **Volume Sold **: The sum of volume of commodities (OTC, Dawa, and Spot) sold on the exchange by AFTL NG and Physical Market's Clients, for Buy orders only.\n - **Weighted Aggregation Cost**: The calculated average cost of purchased commodities based on the total volume purchased.\n", "tr_fact_trade_individual_transactions": " The table description is: The table `tr_fact_trade_individual_transactions` captures detailed records of individual trade transactions, including execution details, trade statuses, and associated fees. It encompasses various attributes related to trades, contracts, and dispatches, reflecting the comprehensive lifecycle of a trade from execution to delivery, while also tracking logistical information and any relevant operational updates. The table supports analysis of trading activities and performance metrics within the trading system. \nThis table is associated with the following: - **Active Exchange Clients**: Clients registered on the exchnage who have engaged in buy or sell transactions on the exchange, and or wallet withdrawal or deposit, and or cia to csd conversion transactions during the specified period, this could be year or season, etc.\n - **Delivered Volume**: The weight of OTC security logged by the logistics officer as delivered, measured in metric tons or 100 kilograms. trade_mart.fact_trade_individual_transactions where security_type = 'OTC' and execution_date is not null\n - **Market Turnover **: The sum of the product of price and quantity for all non-OTC buy transactions, excluding transactions involving exchange issuance. Assumes all exchange issuance transactions equate to FI issuance and buy backs.\n - **Total Trade Transactions**: The distinct count of unique buyer's MATCHED IDs.\n - **Transaction Value / Order Price**: The product of price and quantity for each transaction.\n - **Transaction Value with Fees / Order Price with Fees**: The total transaction value including all associated fees.\n - **Volume Sold **: The sum of volume of commodities (OTC, Dawa, and Spot) sold on the exchange by AFTL NG and Physical Market's Clients, for Buy orders only.\n - **Weighted Aggregation Cost**: The calculated average cost of purchased commodities based on the total volume purchased.", "tr_fact_closingprice": "serves as a comprehensive repository for daily price data across various securities. It captures essential details such as unique identifiers for each record, the date and time of price calculation, and the type of board associated with each security, which can range from 'Virtual' to 'OTC'. Each record also includes specifics of the security itself, including its code, name, and type, which categorizes securities into types like 'Virtual', 'SCIT', 'FI', and others. Financial metrics such as daily closing values, total daily matched units, and their equivalents in kilograms are meticulously logged, along with price ranges for the current and previous days, opening and closing prices per unit and in kilograms, and percentage changes from previous closing prices. This structured data facilitates detailed analysis of daily trading patterns, price movements, and performance metrics crucial for understanding the dynamics of the AFEX trading ecosystem. Overall, the `tr_fact_closingprice` table plays a pivotal role in monitoring and analyzing the daily performance of securities within AFEX. It consolidates a wealth of data points essential for tracking market trends, evaluating price volatility, and assessing the overall health of various securities. By documenting not only closing prices but also associated metrics like trading volumes, price ranges, and percentage changes, this table supports informed decision-making and strategic planning for investors, traders, and analysts operating within AFEX's trading environment.", "ovs_fact_prices_securities_prices": "The 'ov_fact_prices_securities_prices' table contains information on the daily prices of securities traded on the Africa Commodity Exchange Limited, Africa Investment Limited, and Africa Fair Trade Limited. It includes the date of the price, the day of the week, the week of the year, and the season during which the price was recorded. The security code, name, and type are also included, as well as the location of the trade.\n\nThe table also contains information on the closing, opening, maximum, and minimum prices for each security, both in kilograms and per unit. The changes in prices from the previous day and from the start of the week, season, and year are also recorded. Additionally, the table includes the week's starting and ending prices, the previous week's price, and the change in price from the previous week.\n\nThis data is useful for analyzing the trends and performance of different securities over time, as well as for monitoring changes in prices and trading patterns. It can also be used for forecasting future prices and making informed investment decisions.", "ovs_fact_prices_commodities_prices": "The 'ov_fact_prices_commodities_prices' table contains information on the daily prices of various commodities traded on the African Commodity Exchange. It includes the date, day of the week, week of the year, season, commodity code, commodity name, and various price values such as closing price per kilogram, opening price per kilogram, maximum price per kilogram, and minimum price per kilogram. It also includes the difference in price from the previous day, the percentage change in price from the previous day, the starting price for the week, the ending price for the week, the previous week's price, the percentage change in price from the previous week, the starting price for the season, the standard deviation of price change, the starting price for the year, and the year-to-date percentage change in price. These values are important for analyzing trends and making informed decisions in commodity trading.", "ovs_fact_prices_aei": "The 'ov_fact_prices_aei' table contains data on the prices of various commodities traded on the Africa Exchange. It includes the date and time of the recorded price, the code and name of the commodity, the closing price index for the current trading day, the points for the current day, the points from the previous day, the change in points from the previous day, the points from the previous week, the change in points from the previous week, the starting and ending points for the current week, the starting point for the current season, the change in standard deviation from the previous day, the starting point for the current year, and the year-to-date change in points. These data points are essential for traders and investors to track the performance of commodities on the Africa Exchange.", "ovs_fact_prices_aci": "The 'ov_fact_prices_aci' database table contains information on the daily closing prices and market trends of various commodities traded on Africa Commodity Exchange Limited, Africa Investment Limited, and Africa Fair Trade Limited. This table includes the following columns:\n\n- date: the date of the price index (nullable: Yes, default: None)\n- commodity_code: a unique code assigned to each commodity (nullable: Yes, default: None)\n- commodity: the name of the commodity (nullable: Yes, default: None)\n- closing_price_index_mt: the daily closing price index for the commodity (nullable: Yes, default: None)\n- points: the number of points gained or lost by the commodity on the given date (nullable: Yes, default: None)\n- prev_day_point: the number of points gained or lost by the commodity on the previous day (nullable: Yes, default: None)\n- dod_change: the percentage change in points from the previous day (nullable: Yes, default: None)\n- prev_week_point: the number of points gained or lost by the commodity in the previous week (nullable: Yes, default: None)\n- wow_change: the percentage change in points from the previous week (nullable: Yes, default: None)\n- week_start", "ovs_dim_security": "The 'ov_dim_security' table is a collection of data related to securities on the Africa Exchange (AFEX). It contains information such as the unique identifier (id), security code, security name, type of security (sec_security_type), board type (board_type_sec), and general security type (security_type). The table is designed to assist users in identifying and organizing various securities traded on AFEX, including cash crops (through Africa Commodity Exchange Limited), investments (through Africa Investment Limited), and fair trade products (through Africa Fair Trade Limited). The id column is a numeric identifier for each security, while the remaining columns provide descriptive information about the security. These columns may contain null values, indicating that the information is not available or has not been entered. The default value for all columns is None, meaning that no default value has been set and the user must input the necessary information.\nThis table is associated with the following: - **Securities Listed**: The distinct count of securities sold on the exchange during the specified period.\n"}