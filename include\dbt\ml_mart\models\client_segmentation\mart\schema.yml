version: 2
models:
  - name: clients_transactions_corporates
    description: ""
    columns:
      - name: cid
        data_type: text
        description: "Unique Identifier of the client"
        tests:
          - not_null
          - unique
      - name: account_type
        data_type: text
        description: ""
        tests:
          - not_null
      - name: user_account_type
        data_type: text
        description: ""
        tests:
          - not_null
      - name: client_tenure_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: trade_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: last90days_trade_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: buy_trade_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: sell_trade_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: trade_recency_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: trade_tenure_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: trade_value
        data_type: double precision
        description: ""
        tests:
          - not_null
      - name: available_balance
        data_type: double precision
        description: ""
        tests:
          - not_null
      - name: cash_advance_balance
        data_type: double precision
        description: ""
        tests:
          - not_null
      - name: deposit_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: last90days_deposit_freq
        data_type: bigint
        description: ""
      - name: deposit_recency_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: deposit_tenure_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: deposit_amount
        data_type: double precision
        description: ""
        tests:
          - not_null
      - name: withdrawal_freq
        data_type: bigin
        tests:
          - not_nullt
        description: ""
      - name: last90days_withdrawal_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: withdrawal_recency_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: withdrawal_tenure_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: withdrawal_amount
        data_type: double precision
        description: ""
        tests:
          - not_null
      - name: loan_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: loan_recency_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: loan_tenure_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: loan_value_collected
        data_type: double precision
        description: ""
        tests:
          - not_null
  - name: clients_transactions_individuals
    description: ""
    columns:
      - name: cid
        data_type: text
        description: ""
        tests:
          - not_null
          - unique
      - name: account_type
        data_type: text
        description: ""
        tests:
          - not_null
      - name: user_account_type
        data_type: text
        description: ""
        tests:
          - not_null
      - name: client_tenure_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: trade_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: last90days_trade_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: buy_trade_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: sell_trade_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: trade_recency_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: trade_tenure_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: trade_value
        data_type: double precision
        description: ""
        tests:
          - not_null
      - name: available_balance
        data_type: double precision
        description: ""
        tests:
          - not_null
      - name: cash_advance_balance
        data_type: double precision
        description: ""
        tests:
          - not_null
      - name: deposit_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: last90days_deposit_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: deposit_recency_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: deposit_tenure_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: deposit_amount
        data_type: double precision
        description: ""
        tests:
          - not_null
      - name: withdrawal_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: last90days_withdrawal_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: withdrawal_recency_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: withdrawal_tenure_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: withdrawal_amount
        data_type: double precision
        description: ""
        tests:
          - not_null
      - name: loan_freq
        data_type: bigint
        description: ""
        tests:
          - not_null
      - name: loan_recency_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: loan_tenure_months
        data_type: numeric
        description: ""
        tests:
          - not_null
      - name: loan_value_collected
        data_type: double precision
        description: ""
        tests:
          - not_null