{% docs wb_dim_farmer_id %}
The unique identifier of the farmer.
{% enddocs %}

{% docs wb_dim_farmer_created %}
Timestamp indicating when the farmer record was created.
{% enddocs %}

{% docs wb_dim_farmer_updated %}
Timestamp indicating the last update time for this farmer record.
{% enddocs %}

{% docs wb_dim_farmer_is_deleted %}
A flag indicating whether the farmer record is marked as deleted.
{% enddocs %}

{% docs wb_dim_farmer_folio_id %}
The folio ID associated with the farmer.
{% enddocs %}

{% docs wb_dim_farmer_title %}
The title of the farmer.
{% enddocs %}

{% docs wb_dim_farmer_first_name %}
The first name of the farmer.
{% enddocs %}

{% docs wb_dim_farmer_last_name %}
The last name of the farmer.
{% enddocs %}

{% docs wb_dim_farmer_middle_name %}
The middle name of the farmer.
{% enddocs %}

{% docs wb_dim_farmer_address %}
The address of the farmer.
{% enddocs %}

{% docs wb_dim_farmer_gender %}
The gender of the farmer (Male, Female).
{% enddocs %}

{% docs wb_dim_farmer_marital_status %}
The marital status of the farmer.
{% enddocs %}

{% docs wb_dim_farmer_dob %}
The date of birth of the farmer.
{% enddocs %}

{% docs wb_dim_farmer_phone %}
The phone number of the farmer.
{% enddocs %}

{% docs wb_dim_farmer_village %}
The village associated with the farmer.
{% enddocs %}

{% docs wb_dim_farmer_farm_size %}
The size of the farm owned by the farmer.
{% enddocs %}

{% docs wb_dim_farmer_passport_type %}
The type of passport held by the farmer ('Driver's License', 'Others', 'University ID Card', 'Drivers License', 'International Passport', 'National ID Card', 'Cooperative ID Card', 'BVN', 'Voters Card').
{% enddocs %}

{% docs wb_dim_farmer_passport_number %}
The passport number of the farmer.
{% enddocs %}

{% docs wb_dim_farmer_nok_phone %}
The phone number of the next of kin (NOK) of the farmer.
{% enddocs %}

{% docs wb_dim_farmer_nok_name %}
The name of the next of kin (NOK) of the farmer.
{% enddocs %}

{% docs wb_dim_farmer_nok_relationship %}
The relationship of the next of kin (NOK) to the farmer.
{% enddocs %}

{% docs wb_dim_farmer_bvn %}
The Bank Verification Number (BVN) of the farmer.
{% enddocs %}

{% docs wb_dim_farmer_farm_coordinates %}
The coordinates of the farmer's farm.
{% enddocs %}

{% docs wb_dim_farmer_farm_coordinates_polygon %}
The polygon coordinates of the farmer's farm.
{% enddocs %}

{% docs wb_dim_farmer_is_blacklist %}
A flag indicating whether the farmer is blacklisted.
{% enddocs %}

{% docs wb_dim_farmer_languages %}
The languages spoken by the farmer.
{% enddocs %}

{% docs wb_dim_farmer_client_id %}
The unique identifier of the client associated with the farmer.
{% enddocs %}

{% docs wb_dim_farmer_warehouse_id %}
The unique identifier of the warehouse associated with the farmer. You can join with the dim_warehouse table using warehouse_id to get warehouse and location details of a farmer.
{% enddocs %}

{% docs wb_dim_farmer_crop_name %}
The name of the crop planted by the farmer.
{% enddocs %}

{% docs wb_dim_farmer_crop_code %}
The code representing the crop planted by the farmer.
{% enddocs %}

{% docs wb_dim_farmer_feo_name %}
The name of the Field Extension Officer (FEO) associated with the farmer.
{% enddocs %}

{% docs wb_dim_farmer_feo_code %}
The code representing the Field Extension Officer (FEO) associated with the farmer.
{% enddocs %}

{% docs wb_dim_farmer_feo_phone_number %}
The phone number of the Field Extension Officer (FEO) associated with the farmer.
{% enddocs %}

{% docs wb_dim_farmer_cooperative_id %}
The unique identifier of the cooperative associated with the farmer.
{% enddocs %}

{% docs wb_dim_farmer_cooperative_name %}
The name of the cooperative associated with the farmer.
{% enddocs %}

{% docs wb_dim_farmer_cooperative_code %}
The code representing the cooperative associated with the farmer.
{% enddocs %}

{% docs wb_dim_farmer_account_numbers %}
The account numbers associated with the farmer.
{% enddocs %}

{% docs wb_dim_farmer_tenant_id %}
The unique identifier of the tenant associated with the farmer.
{% enddocs %}

{% docs wb_dim_farmer_phone_invalid %}
A flag indicating whether the phone number of the farmer is invalid.
{% enddocs %}

{% docs wb_dim_farmer_phone_number_status %}
The status of the phone number.
{% enddocs %}

{% docs wb_dim_farmer_coordinate_status %}
The coordinate status ('Pending Check', 'Invalid').
{% enddocs %}

{% docs wb_dim_farmer_id_status %}
The ID status ('Pending Check', 'Confirmed Valid', 'Valid', 'Invalid', 'Confirmed Invalid').
{% enddocs %}
