{{ config (materialized = 'table') }}

with recursive
start_price as (
	select date, commodity_code, commodity, closing_price_kg
	from ovs_mart.fact_prices_commodities_prices
	where date = (select min(date_actual) from all_prices)
	),

all_prices_with_start_price as (
	select all_prices.*, start_price.closing_price_kg,
			case when all_prices.board_price is null and start_price.closing_price_kg is not null then start_price.closing_price_kg
			else all_prices.final_board_price
			end adj_final_board_price
	from all_prices
	left join start_price
		on all_prices.date_actual = start_price.date
			and all_prices.commodity_code = start_price.commodity_code
	
	),


closing_price as (
    select date_actual, 
            board_code, 
            commodity_code, 
            fixed_board_weight,
        fixed_board_weight adjusted_board_weight,
            -- 0.00::double precision assumed_market_price,
        final_board_price,
            adj_final_board_price,
            case when final_board_price is not null then 0 
                else 1 
            end as days_without_trade,
            
            1.00 daily_weight,
        adj_final_board_price * fixed_board_weight testing,
            SUM(adj_final_board_price * fixed_board_weight) over (partition by commodity_code, date_actual order by date_actual) as commodity_acp_fixed_weight
    
    from all_prices_with_start_price
    
    where date_actual = (select min(date_actual) from all_prices_with_start_price)
    
    UNION ALL
    
    SELECT main.date_actual, 
            main.board_code, 
            main.commodity_code, 
            main.fixed_board_weight, 
        CASE WHEN main.final_board_price IS NULL AND cp.days_without_trade + 1 > 5 THEN 0
        ELSE main.fixed_board_weight
        END adjusted_board_weight,
            -- assumed_mkt_price.closing_price_kg assumed_market_price,
        main.final_board_price, 
            CASE WHEN main.final_board_price IS NOT NULL THEN main.final_board_price
                WHEN main.final_board_price IS NULL AND cp.days_without_trade + 1 <= 5 THEN cp.commodity_acp_fixed_weight
                WHEN main.final_board_price IS NULL AND cp.days_without_trade + 1 > 5 THEN 0
            END AS adj_final_board_price,
        
            CASE WHEN main.final_board_price IS NOT NULL THEN 0 
                ELSE cp.days_without_trade + 1 
            END AS days_without_trade,
        
            SUM(CASE WHEN main.final_board_price IS NULL AND cp.days_without_trade + 1 > 5 THEN 0
                    ELSE main.fixed_board_weight
                END) OVER (PARTITION BY cp.commodity_code, cp.date_actual ORDER BY cp.date_actual) daily_weight,

            (CASE WHEN main.final_board_price IS NOT NULL THEN main.final_board_price
                    WHEN main.final_board_price IS NULL AND cp.days_without_trade + 1 <= 5 THEN cp.commodity_acp_fixed_weight
                    WHEN main.final_board_price IS NULL AND cp.days_without_trade + 1 > 5 THEN 0
                END) * 
            
                    (CASE WHEN main.final_board_price IS NULL AND cp.days_without_trade + 1 > 5 THEN 0
                        ELSE main.fixed_board_weight
                    END) testing,
            
            SUM(
                (CASE WHEN main.final_board_price IS NOT NULL THEN main.final_board_price
                    WHEN main.final_board_price IS NULL AND cp.days_without_trade + 1 <= 5 THEN cp.commodity_acp_fixed_weight
                    WHEN main.final_board_price IS NULL AND cp.days_without_trade + 1 > 5 THEN 0
                END)
                * 
                (CASE WHEN main.final_board_price IS NULL AND cp.days_without_trade + 1 > 5 THEN 0
                    ELSE main.fixed_board_weight
                END)
                ) OVER (PARTITION BY cp.commodity_code, cp.date_actual ORDER BY cp.date_actual) 
            /
            CASE WHEN SUM(
                        CASE WHEN main.final_board_price IS NULL AND cp.days_without_trade + 1 > 5 THEN 0
                        ELSE main.fixed_board_weight
                        END
                        ) OVER (PARTITION BY cp.commodity_code, cp.date_actual ORDER BY cp.date_actual) = 0 
                        
                        THEN 1 -- or any other value that makes sense in your context

            ELSE SUM(
                    CASE WHEN main.final_board_price IS NULL AND cp.days_without_trade + 1 > 5 THEN 0
                    ELSE main.fixed_board_weight
                    END) OVER (PARTITION BY cp.commodity_code, cp.date_actual ORDER BY cp.date_actual)
            END AS commodity_acp_fixed_weight

	from all_prices_with_start_price main
    join closing_price cp
        on main.board_code = cp.board_code AND main.date_actual = cp.date_actual + INTERVAL '1 day'

    where main.date_actual <= (select max(date_actual) from all_prices_with_start_price)
    ),

final_price_at_board_level AS (
    SELECT
        cp.date_actual, 
        f.consolidated_commodity_name, 
        cp.commodity_code, 
        cp.board_code, 
        cp.fixed_board_weight,
		-- cp.assumed_market_price,
        f.final_board_price, 
        cp.adj_final_board_price, 
        cp.days_without_trade,
		cp.testing,
        cp.commodity_acp_fixed_weight
    FROM 
        closing_price cp
    JOIN 
        all_prices_with_start_price f ON cp.date_actual = f.date_actual AND cp.board_code = f.board_code
    ),

commodity_closing_price as (
	select distinct date_actual, commodity_code, consolidated_commodity_name, commodity_acp_fixed_weight 
	
	from final_price_at_board_level
	)

select *
from commodity_closing_price