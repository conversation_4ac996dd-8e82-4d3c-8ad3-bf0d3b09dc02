
import configparser
import ast
from datetime import datetime
import pandas as pd
import os
from credentials import  db_conn
from datetime import datetime, timedelta
from dwh_cleanup_class import Cleanup

dir_path = os.path.dirname(os.path.realpath(__file__))
config = configparser.ConfigParser()
config.read(f"{dir_path}/config.ini")
config_source = 'SOURCE_DB'
source_schema = config[config_source]["schema"]

# connection
engine,connection =  db_conn(conn_param=config_source)
if engine == None :
    print('Unable to load data - engine not connected')

else:    
    # load ddl
    cleanup = Cleanup(engine)
    schema_names =  cleanup.fetch_schema()
    staging_schema = [schema for schema in schema_names if schema.startswith('_')]
    data_schema = [schema for schema in   schema_names if schema not in staging_schema]

    # clean up staging schema
    for schema in staging_schema:
        schema_tables = cleanup.fetch_table(schema_name=schema)
        cleanup.truncate(schema_name=schema,tables=schema_tables)

    # clean up data schema
    #data_schema = ['csd']
    for schema in data_schema:
        print('pass')
        schema_tables = cleanup.fetch_table(schema_name=schema)
        # Generate temp table names
        temp_tables =  [table for table in schema_tables if '_airbyte_tmp' in table]
        # Generate raw json table names
        raw_json_tables = [table for table in schema_tables if '_airbyte_raw' in table]

        #clean up temp tables   
        cleanup.delete(schema_name=schema,tables=temp_tables)

        # Specify days_to_keep
        days_to_keep = 7
        print('pass2')

        # Truncate till date raw_json tables
        cleanup.truncate_with_date(schema_name=schema,tables=raw_json_tables,days_to_keep=days_to_keep)

cleanup.close_engine()