{{ config (
	materialized = 'table',
	post_hook = [
		"GRANT USAGE ON SCHEMA exchange_mart TO af_exchange_reader;"
		"GRANT SELECT ON exchange_mart.fact_prices_aci TO af_exchange_reader"
	]
	) }}

with

index_price as(	
	
	select *
		, case when dow = 0 then -- Saturday
					lead(woy, 1) over (partition by commodity order by date)
				when dow = 6 then -- Sunday
					lead(woy, 2) over (partition by commodity order by date)
			else woy
			end adj_woy -- To ensure that the saturday and sunday belong to the new week
		
		, case when closing_price_commodity_mt is null or closing_price_commodity_mt = 0 then null
				else commodity_weight_3yr_avg
			end commodity_weight
		, sum(coalesce(closing_price_commodity_mt, 0) * 
				case when closing_price_commodity_mt is null or closing_price_commodity_mt = 0 then null
				else commodity_weight_3yr_avg
				end) over (partition by date order by date) 
			/
				sum(case when closing_price_commodity_mt is null or closing_price_commodity_mt = 0 then null
					else commodity_weight_3yr_avg
					end) over (partition by date order by date) index_price
		-- The above line is to ensure the weighted average is by only commodities with prices on the concerned day
	
	from {{ ref ('int_prices_aci_commodity_weights') }}
	),

all_aci as (
    -- Chose to use old_aei up till  03/11/2024

	select season, date, commodity, commodity_code, 
			case when commodity = 'Maize Feed Grade - White' then 0.5750 
					when commodity = 'Paddy Rice Long Grain' then 0.2046
					when commodity = 'Soybean' then 0.1377
					when commodity = 'Sorghum' then 0.0827
					end commodity_weight, 
			price_kg, price_mt, sub_index initial_sub_index, aci_index index_price, 
			aci initial_aci, adj_woy, quarter, 
			case when date < '2024-10-01' then 'First Base Period'
					when date between '2024-10-01' and current_date then 'Second Base Period'
				end index_period_identifier
	
	from {{source ('exchange_mart', 'historical_aci_upto_2024_11_03')}}
	where date < '2024-11-04'
	
	UNION
	
	select season, date, commodity, commodity_code, commodity_weight, closing_price_commodity_kg price_kg, closing_price_commodity_mt price_mt
			, null initial_sub_index, index_price, null initial_aci, adj_woy, quarter
			, case when date < '2024-10-01' then 'First Base Period'
					when date between '2024-10-01' and current_date then 'Second Base Period'
				end index_period_identifier

	from index_price 
	where date >= '2024-11-04'
	),

new_aci as ( 
	select *
			, case when date < '2024-11-04' then initial_aci
					when (date between '2024-11-04' and current_date)
						then ((index_price / first_value(index_price) over (partition by index_period_identifier order by date)) * 100) -- Second Base period = Oct 1, 2024
				end aci
			
	
			, case when date < '2024-11-04' then initial_sub_index
					when (date between '2024-11-04' and current_date) 
						then ((price_mt / first_value(price_mt) over (partition by index_period_identifier, commodity order by date)) * 100) -- Second Base period = Oct 1, 2024
				end sub_index

			, concat(adj_woy, '_', case when extract(dow from date::timestamp) = 6 then 0 
										when extract(dow from date::timestamp) = 0 then 1
										when extract(dow from date::timestamp) = 1 then 2
										when extract(dow from date::timestamp) = 2 then 3
										when extract(dow from date::timestamp) = 3 then 4
										when extract(dow from date::timestamp) = 4 then 5
										when extract(dow from date::timestamp) = 5 then 6 
									end) wow_identifier-- Week starts on Sat & ends on Fri and by default dow is labeled from Mon - Sun (1 - 0)
			, concat(extract(month from date::timestamp), '_', extract(year from date::timestamp)) month_identifier-- Week starts on Sat & ends on Fri and by default dow is labeled from Mon - Sun (1 - 0)

	from all_aci
		),

change_values as (
	select *
			, (lag(aci, 1) over (partition by commodity order by date)) previous_day_aci
			, (lag(sub_index, 1) over (partition by commodity order by date)) previous_day_sub_index
			
			, (lag(aci, 7) over (partition by commodity order by date)) previous_week_aci
			, (lag(sub_index, 7) over (partition by commodity order by date)) previous_week_sub_index

			, first_value(aci) over (partition by commodity, adj_woy order by date) week_start_aci
			, first_value(aci) over (partition by commodity, adj_woy order by date desc) week_end_aci
			, first_value(sub_index) over (partition by commodity, adj_woy order by date) week_start_sub_index
			, first_value(sub_index) over (partition by commodity, adj_woy order by date desc) week_end_sub_index

			, first_value(aci) over (partition by commodity, month_identifier order by date) month_start_aci
			, first_value(aci) over (partition by commodity, month_identifier order by date desc) month_end_aci
			, first_value(sub_index) over (partition by commodity, month_identifier order by date) month_start_sub_index
			, first_value(sub_index) over (partition by commodity, month_identifier order by date desc) month_end_sub_index

			, first_value(aci) over (partition by commodity, quarter order by date) quarter_start_aci
			, first_value(aci) over (partition by commodity, quarter order by date desc) quarter_end_aci
			, first_value(sub_index) over (partition by commodity, quarter order by date) quarter_start_sub_index
			, first_value(sub_index) over (partition by commodity, quarter order by date desc) quarter_end_sub_index
	
			, (first_value(aci) over (partition by season, commodity order by date)) season_start_aci
			, (first_value(sub_index) over (partition by season, commodity order by date)) season_start_sub_index
			, (first_value(aci) over (partition by extract('year' from date), commodity order by date)) year_start_aci
			, (first_value(sub_index) over (partition by extract('year' from date), commodity order by date)) year_start_sub_index
	
	from new_aci
	),

last_period_values as (
	select *

			, lag(month_end_aci) over (partition by commodity order by date) last_month_end_aci
			, lag(month_end_sub_index) over (partition by commodity order by date) last_month_end_sub_index
	
			, lag(quarter_end_aci) over (partition by commodity order by date) last_quarter_end_aci
			, lag(quarter_end_sub_index) over (partition by commodity order by date) last_quarter_end_sub_index

	from change_values
	
	),

previous_period_value as (
	select *

			, first_value(last_month_end_aci) over (partition by commodity, month_identifier order by date) previous_month_end_aci 
			, first_value(last_month_end_sub_index) over (partition by commodity, month_identifier order by date) previous_month_end_sub_index 
	
			, first_value(last_quarter_end_aci) over (partition by commodity, quarter order by date) previous_quarter_end_aci 
			, first_value(last_quarter_end_sub_index) over (partition by commodity, quarter order by date) previous_quarter_end_sub_index 

	from last_period_values
	),
	
changes as (
	select *
			, ((aci / previous_day_aci) - 1 ) dod_aci_change
			, ((sub_index / previous_day_sub_index) - 1 ) dod_sub_index_change
	
			, ((aci / previous_week_aci) - 1 ) wow_aci_change
			, ((sub_index / previous_week_sub_index) - 1 ) wow_sub_index_change

			, ((aci / month_start_aci) - 1) mtd_aci_change
			, ((sub_index / month_start_sub_index) - 1) mtd_sub_index_change
	
			, ((month_end_aci / previous_month_end_aci) - 1) mom_aci_change
			, ((month_end_sub_index / previous_month_end_sub_index) - 1) mom_sub_index_change

			, ((aci / quarter_start_aci) - 1) qtd_aci_change
			, ((sub_index / quarter_start_sub_index) - 1) qtd_sub_index_change
	
			, ((quarter_end_aci / previous_quarter_end_aci) - 1) qoq_aci_change
			, ((quarter_end_sub_index / previous_quarter_end_sub_index) - 1) qoq_sub_index_change
	
			, ((aci / season_start_aci) - 1 ) std_aci_change
			, ((sub_index / season_start_sub_index) - 1 ) std_sub_index_change
	
			, ((aci / year_start_aci) - 1 ) ytd_aci_change
			, ((sub_index / year_start_sub_index) - 1 ) ytd_sub_index_change
	
	from previous_period_value
	),
	
final_aci as (

	select
		distinct date
			, 'ACI' commodity_code
			, 'AFEX Commodities Index' commodity
			, null :: double precision commodity_weight
			, index_price closing_price_index_mt
			, aci points
			, previous_day_aci prev_day_point
			, dod_aci_change dod_change
			, previous_week_aci prev_week_point
			, wow_aci_change wow_change
			, week_start_aci week_start
			, week_end_aci week_end
			, month_start_aci month_start
			, month_end_aci month_end
			, mtd_aci_change mtd_change
			, previous_month_end_aci previous_month_end
			, mom_aci_change mom_change
			, quarter_start_aci quarter_start
			, quarter_end_aci quarter_end
			, qtd_aci_change qtd_change
			, previous_quarter_end_aci previous_quarter_end
			, qoq_aci_change qoq_change
			, season_start_aci season_start
			, std_aci_change std_change
			, year_start_aci year_start
			, ytd_aci_change ytd_change
	
	from changes
	
	UNION
	
	select date
			, commodity_code
			, commodity
			, commodity_weight
			, price_mt closing_price_index_mt
			, sub_index points
			, previous_day_sub_index prev_day_point
			, dod_sub_index_change dod_change
			, previous_week_sub_index prev_week_point
			, wow_sub_index_change wow_change
			, week_start_sub_index week_start
			, week_end_sub_index week_end
			, month_start_sub_index month_start
			, month_start_sub_index month_end
			, mtd_sub_index_change mtd_change
			, previous_month_end_sub_index previous_month_end
			, mom_sub_index_change mom_change
			, quarter_start_sub_index quarter_start
			, quarter_end_sub_index quarter_end
			, qtd_sub_index_change qtd_change
			, previous_quarter_end_sub_index previous_quarter_end
			, qoq_sub_index_change qoq_change
			, season_start_sub_index season_start
			, std_sub_index_change std_change
			, year_start_sub_index year_start
			, ytd_sub_index_change ytd_change


	from changes
	)

select 
			date
			, commodity_code
			, commodity
			, commodity_weight
			, closing_price_index_mt
			, points
			, prev_day_point
			, dod_change
			, prev_week_point
			, wow_change
			, week_start
			, week_end
			, month_start
			, month_end
			, mtd_change
			, previous_month_end
			, mom_change
			, quarter_start
			, quarter_end
			, qtd_change
			, previous_quarter_end
			, qoq_change
			, season_start
			, std_change
			, year_start
			, ytd_change

from final_aci
order by date desc, commodity 