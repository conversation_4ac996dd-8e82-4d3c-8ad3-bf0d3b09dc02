{{ config (
	materialized = 'table',
	post_hook = [
		"GRANT USAGE ON SCHEMA exchange_mart TO af_exchange_reader;"
		"GRANT SELECT ON exchange_mart.fact_prices_aei TO af_exchange_reader"
	]
	) }}

with

index_price as(	
	
	select *
		, case when dow = 0 then -- Saturday
					lead(woy, 1) over (partition by commodity order by date)
				when dow = 6 then -- Sunday
					lead(woy, 2) over (partition by commodity order by date)
			else woy
			end adj_woy -- To ensure that the saturday and sunday belong to the new week
		
		, case when closing_price_commodity_mt is null or closing_price_commodity_mt = 0 then null
				else commodity_weight_3yr_avg
			end commodity_weight
		, sum(coalesce(closing_price_commodity_mt, 0) * 
				case when closing_price_commodity_mt is null or closing_price_commodity_mt = 0 then null
				else commodity_weight_3yr_avg
				end) over (partition by date order by date) 
			/
				sum(case when closing_price_commodity_mt is null or closing_price_commodity_mt = 0 then null
					else commodity_weight_3yr_avg
					end) over (partition by date order by date) index_price
		-- The above line is to ensure the weighted average is by only commodities with prices on the concerned day

	from {{ ref ('int_prices_aei_commodity_weights') }}
	),

all_aei as (
    -- Chose to use old_aei up till  03/11/2024

	select season, date, commodity, commodity_code, 
			case when commodity = 'Cocoa' then 0.6219 
					when commodity = 'Ginger Dried Split' then 0.3781
					end commodity_weight, 
			price_kg, price_mt, sub_index initial_sub_index, aei_index index_price, 
			aei initial_aei, adj_woy, quarter, 
			case when date < '2024-10-01' then 'First Base Period'
					when date between '2024-10-01' and current_date then 'Second Base Period'
				end index_period_identifier
	
	from {{source ('exchange_mart', 'historical_aei_upto_2024_11_03')}}
	where date < '2024-11-04'
	
	UNION
	
	select season, date, commodity, commodity_code, commodity_weight, closing_price_commodity_kg price_kg, closing_price_commodity_mt price_mt
			, null initial_sub_index, index_price, null initial_aei, adj_woy, quarter
			, case when date < '2024-10-01' then 'First Base Period'
					when date between '2024-10-01' and current_date then 'Second Base Period'
				end index_period_identifier

	from index_price 
	where date >= '2024-11-04'
	),

new_aei as ( 
	select *
			, case when date < '2024-11-04' then initial_aei
					when (date between '2024-11-04' and current_date)
						then ((index_price / first_value(index_price) over (partition by index_period_identifier order by date)) * 100) -- Second Base period = Oct 1, 2024
				end aei
			
	
			, case when date < '2024-11-04' then initial_sub_index
					when (date between '2024-11-04' and current_date) 
						then ((price_mt / first_value(price_mt) over (partition by index_period_identifier, commodity order by date)) * 100) -- Second Base period = Oct 1, 2024
				end sub_index

			, concat(adj_woy, '_', case when extract(dow from date::timestamp) = 6 then 0 
										when extract(dow from date::timestamp) = 0 then 1
										when extract(dow from date::timestamp) = 1 then 2
										when extract(dow from date::timestamp) = 2 then 3
										when extract(dow from date::timestamp) = 3 then 4
										when extract(dow from date::timestamp) = 4 then 5
										when extract(dow from date::timestamp) = 5 then 6 
									end) wow_identifier-- Week starts on Sat & ends on Fri and by default dow is labeled from Mon - Sun (1 - 0)
			, concat(extract(month from date::timestamp), '_', extract(year from date::timestamp)) month_identifier-- Week starts on Sat & ends on Fri and by default dow is labeled from Mon - Sun (1 - 0)

	from all_aei
		),

change_values as (
	select *
			, (lag(aei, 1) over (partition by commodity order by date)) previous_day_aei
			, (lag(sub_index, 1) over (partition by commodity order by date)) previous_day_sub_index
			
			, (lag(aei, 7) over (partition by commodity order by date)) previous_week_aei
			, (lag(sub_index, 7) over (partition by commodity order by date)) previous_week_sub_index

			, first_value(aei) over (partition by commodity, adj_woy order by date) week_start_aei
			, first_value(aei) over (partition by commodity, adj_woy order by date desc) week_end_aei
			, first_value(sub_index) over (partition by commodity, adj_woy order by date) week_start_sub_index
			, first_value(sub_index) over (partition by commodity, adj_woy order by date desc) week_end_sub_index

			, first_value(aei) over (partition by commodity, month_identifier order by date) month_start_aei
			, first_value(aei) over (partition by commodity, month_identifier order by date desc) month_end_aei
			, first_value(sub_index) over (partition by commodity, month_identifier order by date) month_start_sub_index
			, first_value(sub_index) over (partition by commodity, month_identifier order by date desc) month_end_sub_index

			, first_value(aei) over (partition by commodity, quarter order by date) quarter_start_aei
			, first_value(aei) over (partition by commodity, quarter order by date desc) quarter_end_aei
			, first_value(sub_index) over (partition by commodity, quarter order by date) quarter_start_sub_index
			, first_value(sub_index) over (partition by commodity, quarter order by date desc) quarter_end_sub_index
	
			, (first_value(aei) over (partition by season, commodity order by date)) season_start_aei
			, (first_value(sub_index) over (partition by season, commodity order by date)) season_start_sub_index
			, (first_value(aei) over (partition by extract('year' from date), commodity order by date)) year_start_aei
			, (first_value(sub_index) over (partition by extract('year' from date), commodity order by date)) year_start_sub_index
	
	from new_aei
	),

last_period_values as (
	select *

			, lag(month_end_aei) over (partition by commodity order by date) last_month_end_aei
			, lag(month_end_sub_index) over (partition by commodity order by date) last_month_end_sub_index
	
			, lag(quarter_end_aei) over (partition by commodity order by date) last_quarter_end_aei
			, lag(quarter_end_sub_index) over (partition by commodity order by date) last_quarter_end_sub_index

	from change_values
	
	),

previous_period_value as (
	select *

			, first_value(last_month_end_aei) over (partition by commodity, month_identifier order by date) previous_month_end_aei 
			, first_value(last_month_end_sub_index) over (partition by commodity, month_identifier order by date) previous_month_end_sub_index 
	
			, first_value(last_quarter_end_aei) over (partition by commodity, quarter order by date) previous_quarter_end_aei 
			, first_value(last_quarter_end_sub_index) over (partition by commodity, quarter order by date) previous_quarter_end_sub_index 

	from last_period_values
	),

adj_change_values as (
	select a.season, a.date, a.commodity, a.commodity_code, a.commodity_weight, a.price_kg, a.price_mt, a.sub_index, a.index_price,
			a.aei, a.adj_woy, a.quarter, b.previous_day_aei, a.previous_day_sub_index, b.previous_week_aei, a.previous_week_sub_index,
			b.week_start_aei, b.week_end_aei, a.week_start_sub_index, a.week_end_sub_index,
			b.month_start_aei, b.month_end_aei, a.month_start_sub_index, a.month_end_sub_index,
			b.last_month_end_aei, a.last_month_end_sub_index, b.previous_month_end_aei, a.previous_month_end_sub_index,
			b.quarter_start_aei, b.quarter_end_aei, a.quarter_start_sub_index, a.quarter_end_sub_index,
			b.last_quarter_end_aei, a.last_quarter_end_sub_index, b.previous_quarter_end_aei, a.previous_quarter_end_sub_index,
			b.season_start_aei, a.season_start_sub_index, b.year_start_aei, a.year_start_sub_index

	from previous_period_value a
	left join previous_period_value b
	 on a.date = b.date

	where b.commodity = 'Cocoa'
	),
	
changes as (
	select *
			, ((aei / previous_day_aei) - 1 ) dod_aei_change
			, ((sub_index / previous_day_sub_index) - 1 ) dod_sub_index_change
	
			, ((aei / previous_week_aei) - 1 ) wow_aei_change
			, ((sub_index / previous_week_sub_index) - 1 ) wow_sub_index_change

			, ((aei / month_start_aei) - 1) mtd_aei_change
			, ((sub_index / month_start_sub_index) - 1) mtd_sub_index_change
	
			, ((month_end_aei / previous_month_end_aei) - 1) mom_aei_change
			, ((month_end_sub_index / previous_month_end_sub_index) - 1) mom_sub_index_change

			, ((aei / quarter_start_aei) - 1) qtd_aei_change
			, ((sub_index / quarter_start_sub_index) - 1) qtd_sub_index_change
	
			, ((quarter_end_aei / previous_quarter_end_aei) - 1) qoq_aei_change
			, ((quarter_end_sub_index / previous_quarter_end_sub_index) - 1) qoq_sub_index_change
	
			, ((aei / season_start_aei) - 1 ) std_aei_change
			, ((sub_index / season_start_sub_index) - 1 ) std_sub_index_change
	
			, ((aei / year_start_aei) - 1 ) ytd_aei_change
			, ((sub_index / year_start_sub_index) - 1 ) ytd_sub_index_change
	
	from adj_change_values
	),
	
final_aei as (

	select
		distinct date
			, 'AEI' commodity_code
			, 'AFEX Export Index' commodity
			, null :: double precision commodity_weight
			, index_price closing_price_index_mt
			, aei points
			, previous_day_aei prev_day_point
			, dod_aei_change dod_change
			, previous_week_aei prev_week_point
			, wow_aei_change wow_change
			, week_start_aei week_start
			, week_end_aei week_end
			, month_start_aei month_start
			, month_end_aei month_end
			, mtd_aei_change mtd_change
			, previous_month_end_aei previous_month_end
			, mom_aei_change mom_change
			, quarter_start_aei quarter_start
			, quarter_end_aei quarter_end
			, qtd_aei_change qtd_change
			, previous_quarter_end_aei previous_quarter_end
			, qoq_aei_change qoq_change
			, season_start_aei season_start
			, std_aei_change std_change
			, year_start_aei year_start
			, ytd_aei_change ytd_change
	
	from changes
	
	UNION
	
	select date
			, commodity_code
			, commodity
			, commodity_weight
			, price_mt closing_price_index_mt
			, sub_index points
			, previous_day_sub_index prev_day_point
			, dod_sub_index_change dod_change
			, previous_week_sub_index prev_week_point
			, wow_sub_index_change wow_change
			, week_start_sub_index week_start
			, week_end_sub_index week_end_index
			, month_start_sub_index month_start
			, month_start_sub_index month_end
			, mtd_sub_index_change mtd_change
			, previous_month_end_sub_index previous_month_end
			, mom_sub_index_change mom_change
			, quarter_start_sub_index quarter_start
			, quarter_end_sub_index quarter_end
			, qtd_sub_index_change qtd_change
			, previous_quarter_end_sub_index previous_quarter_end
			, qoq_sub_index_change qoq_change
			, season_start_sub_index season_start
			, std_sub_index_change std_change
			, year_start_sub_index year_start
			, ytd_sub_index_change ytd_change


	from changes
	)

select 
			date
		, commodity_code
		, commodity
		, commodity_weight
		, closing_price_index_mt
		, points
		, prev_day_point
		, dod_change
		, prev_week_point
		, wow_change
		, week_start
		, week_end
		, month_start
		, month_end
		, mtd_change
		, previous_month_end
		, mom_change
		, quarter_start
		, quarter_end
		, qtd_change
		, previous_quarter_end
		, qoq_change
		, season_start
		, std_change
		, year_start
		, ytd_change

from final_aei

order by date desc, commodity 