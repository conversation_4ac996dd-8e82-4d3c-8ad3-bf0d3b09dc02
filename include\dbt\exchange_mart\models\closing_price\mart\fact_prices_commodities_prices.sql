{{ config (materialized = 'table') }}

with
pre_changes_values as(
	select nnm.*
			, case when nnm.dow = 0 then 
						lead(nnm.woy, 1) over (partition by nnm.commodity_name order by nnm.date)
					when nnm.dow = 6 then 
						lead(nnm.woy, 2) over (partition by nnm.commodity_name order by nnm.date)
				else nnm.woy
				end adj_woy
			, case when extract('year' from date) < 2022 and extract('month' from date) >= 12 
					then concat(extract('year' from date)::text, '/', extract('year' from date + INTERVAL '1 year')::text)
				when extract('year' from date) >= 2022 and extract('month' from date) >= 10 
					then concat(extract('year' from date)::text, '/', extract('year' from date + INTERVAL '1 year')::text)
				else concat(extract('year' from date - INTERVAL '1 year')::text, '/', extract('year' from date)::text)
				end season
			, concat('Q', extract(quarter from date::timestamp), ' ', extract(year from date::timestamp)) quarter
			, concat(extract(month from date::timestamp), '_', extract(year from date::timestamp)) month_identifier
	
	from {{ ref ('int_prices_commodities_prices') }} nnm
	),
	
changes_values as(
	select *
			, (lag(closing_price, 1) over (partition by commodity_name order by date)) previous_day_price

			, (lag(closing_price, 7) over (partition by commodity_name order by date)) previous_week_price

			, (lag(closing_price, 1) over (partition by commodity_name, day_of_month  order by date)) previous_month_price

			, (lag(closing_price, 1) over (partition by commodity_name, day_of_quarter  order by date)) previous_quarter_price

			, first_value(closing_price) over (partition by commodity_name, adj_woy order by date) week_start_price
			, first_value(closing_price) over (partition by commodity_name, adj_woy order by date desc) week_end_price
	
			, first_value(closing_price) over (partition by commodity_name, month_identifier order by date) month_start_price
			, first_value(closing_price) over (partition by commodity_name, month_identifier order by date desc) month_end_price
			
			, first_value(closing_price) over (partition by commodity_name, quarter order by date) quarter_start_price
			, first_value(closing_price) over (partition by commodity_name, quarter order by date desc) quarter_end_price

			, (first_value(closing_price) over (partition by season, commodity_name order by date)) season_start_price_

			, (first_value(closing_price) over (partition by extract('year' from date), commodity_name order by date)) year_start_price_	
	from pre_changes_values
	),
	
adjusted_changes_for_year_season_start_prices as(
	select *
			, case when season_start_price_ is null
						and closing_price is not null					
					then (first_value(closing_price) over (partition by season, commodity_name 
																order by (case when closing_price is not null then date end)))
					else season_start_price_
				end season_start_price
			
			, case when year_start_price_ is null
						and closing_price is not null					
					then (first_value(closing_price) over (partition by extract('year' from date), commodity_name 
																order by (case when closing_price is not null then date end)))
					else year_start_price_
				end year_start_price
	from changes_values
	),

last_period_values as (
	select *

			, lag(month_end_price) over (partition by commodity_name order by date) last_month_end_price
			, lag(quarter_end_price) over (partition by commodity_name order by date) last_quarter_end_price

	from adjusted_changes_for_year_season_start_prices
	
	),

previous_period_value as (
	select *

			, first_value(last_month_end_price) over (partition by commodity_name, month_identifier order by date) previous_month_end_price 
			, first_value(last_quarter_end_price) over (partition by commodity_name, quarter order by date) previous_quarter_end_price

	from last_period_values
	),

changes as(
	select *
			, (closing_price - opening_price) dod_price_diff
			, ((closing_price / nullif(opening_price, 0)) - 1) dod_price_change

			, ((closing_price / nullif(week_start_price, 0)) - 1 ) wtd_price_change
			, ((closing_price / nullif(previous_week_price, 0)) - 1 ) wow_price_change

			, ((closing_price / nullif(month_start_price, 0)) - 1 ) mtd_price_change
			, ((closing_price / nullif(previous_month_price, 0)) - 1 ) mom_price_change
			, ((month_end_price / nullif(previous_month_end_price, 0)) - 1) month_end_price_change

			, ((closing_price / nullif(quarter_start_price, 0)) - 1 ) qtd_price_change
			, ((closing_price / nullif(previous_quarter_price, 0)) - 1 ) qoq_price_change
			, ((quarter_end_price / nullif(previous_quarter_end_price, 0)) - 1) quarter_end_price_change

			-- , case when extract('year' from date) < 2022 and extract('month' from date) = 12 and extract('day' from date) = 1 -- December 1 (Pre Oct 2022)
			-- 		then ((closing_price / (nullif((lag(season_start_price, 1) over (partition by commodity_name order by date)), 0))) - 1 )
			-- 		when extract('year' from date) >= 2022 and extract('month' from date) = 10 and extract('day' from date) = 1 -- October 1
			-- 		then ((closing_price / (nullif((lag(season_start_price, 1) over (partition by commodity_name order by date)), 0))) - 1 )
			-- 	else ((closing_price / (nullif(season_start_price, 0))) - 1 )
			-- 	end std_price_change
			, ((closing_price / (nullif(season_start_price, 0))) - 1 ) std_price_change

			-- , case when extract('month' from date) = 1 and extract('day' from date) = 1 -- January 1
			-- 		then ((closing_price / (nullif((lag(year_start_price, 1) over (partition by commodity_name order by date)), 0))) - 1 )
			-- 	else ((closing_price / (nullif(year_start_price, 0))) - 1 )
				-- end ytd_price_change
			, ((closing_price / (nullif(year_start_price, 0))) - 1 ) ytd_price_change
	
	from previous_period_value
	),
	
final as(
	select date, dow, adj_woy woy, season, quarter
			, commodity_code, commodity_name
			, closing_price closing_price_kg, opening_price opening_price_kg
			, max_price_kg, min_price_kg
			, dod_price_diff, dod_price_change
			, week_start_price, week_end_price, wtd_price_change, previous_week_price, wow_price_change
			, month_start_price, month_end_price, mtd_price_change, previous_month_price, mom_price_change, previous_month_end_price, month_end_price_change
			, quarter_start_price, quarter_end_price, qtd_price_change, previous_quarter_price, qoq_price_change, previous_quarter_end_price, quarter_end_price_change
			, season_start_price, std_price_change
			, year_start_price, ytd_price_change
	
	from changes
	)
	
select 
	date, 
	dow, 
	woy, 
	season, 
	quarter, 
	commodity_code, 
	commodity_name, 
	closing_price_kg,
	opening_price_kg,
	max_price_kg, 
	min_price_kg, 
	dod_price_diff, 
	dod_price_change, 
	week_start_price, 
	week_end_price, 
	wtd_price_change, 
	previous_week_price, 
	wow_price_change, 
	month_start_price, 
	month_end_price, 
	mtd_price_change, 
	previous_month_price, 
	mom_price_change, 
	previous_month_end_price, 
	month_end_price_change, 
	quarter_start_price, 
	quarter_end_price, 
	qtd_price_change, 
	previous_quarter_price, 
	qoq_price_change, 
	previous_quarter_end_price, 
	quarter_end_price_change, 
	season_start_price, 
	std_price_change, 
	year_start_price, 
	ytd_price_change
from final
order by date desc, commodity_name