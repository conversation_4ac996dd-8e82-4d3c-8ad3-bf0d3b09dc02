from datetime import datetime,timed<PERSON>ta
import pendulum
from airflow import DAG
from airflow.operators.bash  import BashOperator
from custom_functions.airflow_email_util import failure_email


PATH_TO_DBT_VENV = "/usr/local/airflow/dbt_venv/bin/activate"
PATH_TO_DBT_PROJECT = "/usr/local/airflow/include/dbt"
PATH_TO_DBT_PROFILE = "/usr/local/airflow/include/dbt/.dbt"

default_args  = {
     'owner' : 'Oluwatomisin Soetan',
     'retries' : 0,
     'retry_delay' : timedelta(minutes=2),
     'on_failure_callback': failure_email,
}
with DAG(
    dag_id = 'dbt_snapshot_dag',
    default_args = default_args,
    description =  'This dag runs dbt snapshot for all dbt models',
    start_date= pendulum.datetime(2025, 4, 9, tz="Africa/Lagos"),
    catchup=False,
    schedule_interval = '0 0 * * *',
    tags =  ['dbt','snapshots']
) as dag:
    
    task1 =  BashOperator(
        task_id = 'dbt_snapshots',
        bash_command = 'source $PATH_TO_DBT_VENV  && export DBT_PROFILES_DIR=$PATH_TO_DBT_PROFILE  && dbt snapshot',
        cwd=f"{PATH_TO_DBT_PROJECT}/exchange_mart",
        env={"PATH_TO_DBT_VENV": PATH_TO_DBT_VENV,"PATH_TO_DBT_PROFILE":PATH_TO_DBT_PROFILE},
    )

  
task1
