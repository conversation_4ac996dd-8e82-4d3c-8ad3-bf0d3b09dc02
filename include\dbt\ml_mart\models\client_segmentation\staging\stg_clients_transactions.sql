WITH 

transactions AS
	(
	SELECT trans.client_cid,
		EXTRACT(YEAR FROM AGE(CURRENT_DATE, MIN(trans.trade_creation_date))) client_transaction_tenure_years,
		(EXTRACT(YEAR FROM AGE(CURRENT_DATE, MIN(trans.trade_creation_date))) * 12) + 
			(EXTRACT(MONTH FROM AGE(CURRENT_DATE, MIN(trans.trade_creation_date)))) trade_tenure_months,
		COUNT(trans.tid) AS trade_freq, 
		COUNT(CASE WHEN (CURRENT_DATE - DATE(trans.trade_creation_date)) <= 90 THEN trans.tid END) AS last90days_trade_freq,
		COUNT(CASE WHEN trans.order_type = 'Buy' THEN trans.tid END) AS buy_trade_freq,
		COUNT(CASE WHEN trans.order_type = 'Sell' THEN trans.tid END) AS sell_trade_freq,
		COUNT(DISTINCT trans.order_type) trade_type,
		SUM(trans.trade_value) AS trade_value,
		CURRENT_DATE - MAX(DATE(trans.trade_creation_date)) days_since_last_trade,
		(EXTRACT(YEAR FROM AGE(CURRENT_DATE, MAX(trans.trade_creation_date))) * 12) + 
			(EXTRACT(MONTH FROM AGE(CURRENT_DATE, MAX(trans.trade_creation_date)))) AS trade_recency_months,
		COUNT(DISTINCT trans.security_type) distinct_boards_traded
		
	
	FROM {{ source('trade_mart', 'fact_transactions') }} trans
	
	WHERE trans.trade_status_summary = 'Matched' AND (trans.oms_provider_id IS null OR trans.oms_provider_id = '4')
	
	GROUP BY trans.client_cid
	),

loan AS
	(
	SELECT client.cid, SUM(loan_value) loan_value_collected, COUNT(DISTINCT loan.reference_id) loan_freq,
		(EXTRACT(YEAR FROM AGE(CURRENT_DATE, MIN(loan.created))) * 12) + 
			(EXTRACT(MONTH FROM AGE(CURRENT_DATE, MIN(loan.created)))) loan_tenure_months,
		(EXTRACT(YEAR FROM AGE(CURRENT_DATE, MAX(loan.created))) * 12) + 
			(EXTRACT(MONTH FROM AGE(CURRENT_DATE, MAX(loan.created)))) loan_recency_months
	
	FROM {{ source('comx_mart', 'fact_collateralmgtloan') }} loan
	LEFT JOIN {{ source('comx_mart', 'dim_client') }} client
	ON loan.client_id = client.id
	
	
	GROUP BY client.cid
	),
	
deposit_withdrawal AS
	(
	SELECT cli.cid,
		SUM(CASE WHEN transaction_origin = 'Deposit' THEN amount END) deposit_amount,
		COUNT(CASE WHEN transaction_origin = 'Deposit' THEN cid END) deposit_freq,
		COUNT(CASE WHEN (transaction_origin = 'Deposit' AND ((CURRENT_DATE - DATE(wal.created)) <= 90)) THEN cid END) last90days_deposit_freq,
		(EXTRACT(YEAR FROM AGE(CURRENT_DATE, MIN(CASE WHEN transaction_origin = 'Deposit' THEN wal.created END))) * 12) + 
		(EXTRACT(MONTH FROM AGE(CURRENT_DATE, MIN(CASE WHEN transaction_origin = 'Deposit' THEN wal.created END)))) deposit_tenure_months,
		(EXTRACT(YEAR FROM AGE(CURRENT_DATE, MAX(CASE WHEN transaction_origin = 'Deposit' THEN wal.created END))) * 12) + 
		(EXTRACT(MONTH FROM AGE(CURRENT_DATE, MAX(CASE WHEN transaction_origin = 'Deposit' THEN wal.created END)))) deposit_recency_months,
		SUM(CASE WHEN transaction_origin = 'WithdrawalRequest' THEN amount END) withdrawal_amount,
		COUNT(CASE WHEN transaction_origin = 'WithdrawalRequest' THEN cid END) withdrawal_freq,
		COUNT(CASE WHEN (transaction_origin = 'WithdrawalRequest' AND ((CURRENT_DATE - DATE(wal.created)) <= 90)) THEN cid END) last90days_withdrawal_freq,
		(EXTRACT(YEAR FROM AGE(CURRENT_DATE, MIN(CASE WHEN transaction_origin = 'WithdrawalRequest' THEN wal.created END))) * 12) + 
		(EXTRACT(MONTH FROM AGE(CURRENT_DATE, MIN(CASE WHEN transaction_origin = 'WithdrawalRequest' THEN wal.created END)))) withdrawal_tenure_months,
		(EXTRACT(YEAR FROM AGE(CURRENT_DATE, MAX(CASE WHEN transaction_origin = 'WithdrawalRequest' THEN wal.created END))) * 12) + 
		(EXTRACT(MONTH FROM AGE(CURRENT_DATE, MAX(CASE WHEN transaction_origin = 'WithdrawalRequest' THEN wal.created END)))) withdrawal_recency_months
		

	FROM {{ source('comx', 'crm_wallettransactionhistory') }} wal
	LEFT JOIN {{ source('comx_mart', 'dim_client') }} cli
	ON wal.client_id = cli.id

	WHERE ((transaction_origin = 'Deposit') OR (transaction_origin = 'WithdrawalRequest' AND transaction_type = 'UnLien Debit'))
		
	GROUP BY cli.cid
	),
	
wallet_balance AS
	(
	SELECT wal.cid, wal.available_balance, wal.cash_advance_balance
		
	FROM {{ source('trade_mart', 'fact_clientwallet') }} wal
	
	WHERE oms_name = 'COMX' AND currency_code = 'NGN'
	),

compiled AS
	(
	SELECT cli.cid, cli.account_type, cli.user_account_type,
		(EXTRACT(YEAR FROM AGE(CURRENT_DATE, cli.created)) * 12) + 
		(EXTRACT(MONTH FROM AGE(CURRENT_DATE, cli.created))) AS client_tenure_months,

		trans.trade_freq, 
		trans.last90days_trade_freq,
		trans.buy_trade_freq,
		trans.sell_trade_freq,
		trans.trade_recency_months,
		trans.trade_tenure_months,
		trans.trade_value,
		wal.available_balance,
		wal.cash_advance_balance,

		dpw.deposit_freq,
		dpw.last90days_deposit_freq,
		dpw.deposit_recency_months,
		dpw.deposit_tenure_months,
		dpw.deposit_amount,

		dpw.withdrawal_freq,
		dpw.last90days_withdrawal_freq,
		dpw.withdrawal_recency_months,
		dpw.withdrawal_tenure_months,
		dpw.withdrawal_amount,

		loan.loan_freq,
		loan.loan_recency_months,
		loan_tenure_months,
		loan.loan_value_collected


	FROM {{ source('comx_mart', 'dim_client') }} cli
	LEFT JOIN transactions trans
	ON cli.cid = trans.client_cid
	LEFT JOIN deposit_withdrawal dpw
	ON cli.cid = dpw.cid
	LEFT JOIN wallet_balance wal
	ON cli.cid = wal.cid
	LEFT JOIN loan loan
	ON cli.cid = loan.cid

	ORDER BY cli.cid
	)

SELECT * FROM compiled