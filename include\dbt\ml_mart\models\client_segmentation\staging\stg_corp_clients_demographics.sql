WITH

corporate_clients AS(
    SELECT *
    FROM {{ source('comx_mart', 'dim_client') }}
    
    WHERE user_account_type = 'Corporate'
),

all_information AS(
	SELECT cli.*,corp.city,corp.state,corp.rc_number,
		corp.company_name,corp.contact_name,
		corp.director_bvn,corp.director_dob,
		corp.company_email,corp.company_phone,
		corp.contact_email,corp.contact_phone,
		corp.office_address,corp.prompt_message,
		corp.approval_status,corp.company_website,
		corp.director_last_name,corp.nature_of_business,
		corp.director_first_name,corp.politically_exposed,
		corp.political_experience,corp.date_of_incorporation,
		corp.brokerage_community_id,corp.place_of_incorporation,
		row_number() over(partition by cli.cid) row_number_index

	FROM corporate_clients cli

	LEFT JOIN {{ source('comx', 'crm_corporateclient') }} corp
	ON cli.rnb = corp.rnb

	ORDER BY cli.created DESC
),

required_columns AS(
	SELECT 
		cid,created, account_type, is_synced_wb,
		is_afex_broker, is_kyc_complete,
		user_account_type, used_referral_code, country,
		region, subregion, 
		
		CASE WHEN state = '.'
			THEN NULL
		ELSE state
		END AS state,
		
		rc_number,
		company_website, politically_exposed,
		date_of_incorporation, nature_of_business,
		city, political_experience, place_of_incorporation
		
	FROM all_information
	WHERE row_number_index = 1
),

new_columns AS(
	SELECT cid,created, account_type, is_synced_wb,
		is_afex_broker, is_kyc_complete,
		user_account_type, used_referral_code, country,
		region, subregion, state, rc_number,
		company_website, date_of_incorporation, nature_of_business,
		city, political_experience, place_of_incorporation,
		
		COALESCE(politically_exposed, False) politically_exposed,

		CASE WHEN company_website::text ~ '^(http[s]?://|www)[^\s]+[.]\w{2,}[/]?$'
			THEN 'has website'
		ELSE 'no website'
		END AS has_website,

		CASE WHEN LEFT(rc_number,2) ILIKE '%rc%'
			THEN 'has rc number'
		ELSE 'does not have rc number'
		END AS has_rc_number,

		CASE WHEN LEFT(used_referral_code,3) ILIKE '%COMX%'
			THEN 'referred'
		ELSE 'not referred'
		END AS is_referred,

		EXTRACT(
			YEAR FROM AGE(
				CURRENT_DATE, date_of_incorporation)) age,
		EXTRACT(
			YEAR FROM created) created_year,
		EXTRACT(
			MONTH FROM created) created_month,
		EXTRACT(
			DAY FROM created) created_dayofmonth,
		EXTRACT(
			DOW FROM created) created_dayofweek

	FROM required_columns
),

all_client_info AS(
	SELECT
		cid,
		created,
	
		CASE WHEN account_type IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY account_type) FROM new_columns)
		ELSE account_type
		END AS account_type,
	
		is_synced_wb,
		is_afex_broker,
		is_kyc_complete,
	
		CASE WHEN user_account_type IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY user_account_type) FROM new_columns)
		ELSE user_account_type
		END AS user_account_type,
		
		CASE WHEN country IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY country) FROM new_columns)
		ELSE country
		END AS country,
	
		CASE WHEN region IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY region) FROM new_columns)
		ELSE region
		END AS region,

		CASE WHEN state IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY state) FROM new_columns)
		ELSE state
		END AS state,
		
		politically_exposed,
		has_website,
		has_rc_number,
		is_referred,
	
		CASE WHEN age IS NULL
			THEN (SELECT percentile_cont(0.5) WITHIN GROUP (ORDER BY age) FROM new_columns)
		ELSE age
		END AS age,
	
		created_year,
		created_month,
		created_dayofmonth,
		created_dayofweek
	
	FROM new_columns
)

select * from all_client_info