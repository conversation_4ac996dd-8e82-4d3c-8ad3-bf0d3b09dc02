from datetime import datetime,timedelta
import pendulum
from airflow import DAG
from airflow.operators.bash  import BashOperator
from airflow.operators.python import PythonOperator
from airflow.utils.trigger_rule import TriggerRule
from custom_functions.airflow_email_util import failure_email, warning_email

PATH_TO_DBT_VENV="/usr/local/airflow/dbt_venv/bin/activate"
PATH_TO_DBT_PROJECT="/usr/local/airflow/include/dbt/exchange_mart"
PATH_TO_DBT_PROFILE="/usr/local/airflow/include/dbt/.dbt"

default_args  = {
     'owner' : 'Oluwatomisin Soetan',
     'retries' : 0,
     'retry_delay' : timedelta(minutes=2),
     'on_failure_callback': failure_email
}
with DAG(
    dag_id = 'dbt_exchange_mart_dag',
    default_args = default_args,
    description =  'ACEL Analytics Schema Update',
    start_date= pendulum.datetime(2025, 4, 9, tz="Africa/Lagos"),
    catchup=False,
    schedule_interval =   '20 6,15 * * *',
    tags =  ['dbt','exchange_mart']
) as dag:

    task0 =  BashOperator(
        task_id = 'dbt_deps',
        bash_command = 'source $PATH_TO_DBT_VENV  && export DBT_PROFILES_DIR=$PATH_TO_DBT_PROFILE  && dbt deps',
        cwd=PATH_TO_DBT_PROJECT,
        env={"PATH_TO_DBT_VENV": PATH_TO_DBT_VENV,"PATH_TO_DBT_PROFILE":PATH_TO_DBT_PROFILE},
    )

    task1 =  BashOperator(
        task_id = 'dbt_run',
        bash_command = 'source $PATH_TO_DBT_VENV  && export DBT_PROFILES_DIR=$PATH_TO_DBT_PROFILE  && dbt run', 
        cwd=PATH_TO_DBT_PROJECT,
        env={"PATH_TO_DBT_VENV": PATH_TO_DBT_VENV,"PATH_TO_DBT_PROFILE":PATH_TO_DBT_PROFILE},
    )

    task2 =  BashOperator(
        task_id = 'dbt_test',
        bash_command = 'source $PATH_TO_DBT_VENV  && export DBT_PROFILES_DIR=$PATH_TO_DBT_PROFILE  && dbt test || exit 0',
        cwd=PATH_TO_DBT_PROJECT,
        env={"PATH_TO_DBT_VENV": PATH_TO_DBT_VENV,"PATH_TO_DBT_PROFILE":PATH_TO_DBT_PROFILE},
    )

    task3 =  BashOperator(
        task_id = 'dbt_docs_generate',
        bash_command = 'source $PATH_TO_DBT_VENV  && export DBT_PROFILES_DIR=$PATH_TO_DBT_PROFILE  && dbt docs generate',
        cwd=PATH_TO_DBT_PROJECT,
        env={"PATH_TO_DBT_VENV": PATH_TO_DBT_VENV,"PATH_TO_DBT_PROFILE":PATH_TO_DBT_PROFILE},
    )
    
     # Run missing data tests separately first
    test_missing__closing_price = BashOperator(
        task_id="dbt_test_missing_closing_price_data",
        bash_command="source $PATH_TO_DBT_VENV  && export DBT_PROFILES_DIR=$PATH_TO_DBT_PROFILE && dbt test --select missing_closing_price_commodities missing_commodities_in_closing_price missing_closing_price_securities",
        cwd=PATH_TO_DBT_PROJECT,
        env={"PATH_TO_DBT_VENV": PATH_TO_DBT_VENV,"PATH_TO_DBT_PROFILE":PATH_TO_DBT_PROFILE},
        do_xcom_push=True,
    )

    # Alert only if missing data test fails
    alert_on_missing_data = PythonOperator(
        task_id="alert_on_missing_data",
        python_callable=warning_email,
        trigger_rule=TriggerRule.ONE_FAILED,  # Runs ONLY if test_missing_data fails,
        provide_context=True 
    )

  

task0 >> task1 >> task2 >> task3
task2 >> test_missing__closing_price  >> alert_on_missing_data
