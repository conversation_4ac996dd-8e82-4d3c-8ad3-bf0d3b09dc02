from datetime import datetime, timed<PERSON>ta
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from gxoperator import G<PERSON><PERSON>perator
from credentials import db_conn

#define great expectations operator
gx = GXOperator()
source_engine, conn_source = db_conn()
# Define your Python function to run
def dim_client():    
    json_result  = gx.run_expectations(data_asset_name='dim_client',
                        expectation_suite_name='dim_client_expectations')
    gx.expectations_to_db(checkpoint_jsonresult=json_result,source_engine=source_engine)
    

def fact_collateralmgtloan_breakdown():    
    json_result  = gx.run_expectations(data_asset_name='fact_collateralmgtloan_breakdown',
                        expectation_suite_name='fact_collateralmgtloan_breakdown_expectations')
    gx.expectations_to_db(checkpoint_jsonresult=json_result,source_engine=source_engine)
    

def fact_collateralmgtloan():    
    json_result  = gx.run_expectations(data_asset_name='fact_collateralmgtloan',
                        expectation_suite_name='fact_collateralmgtloan_expectations')
    gx.expectations_to_db(checkpoint_jsonresult=json_result,source_engine=source_engine)
#   
# Define your DAG
dag = DAG(
    'gx_comx_dag',
    description='Run a Python script in a DAG',
    start_date=datetime(2023, 10, 11),  # Specify the start date
    schedule_interval= '0 0 * * 3',
    tags =  ['great_expectations','comx_mart']
)

# Create a PythonOperator that runs your Python function
dim_client_script = PythonOperator(
    task_id='dim_client_expectations',
    python_callable=dim_client,
    dag=dag,
)


fact_collateralmgtloan_script = PythonOperator(
    task_id='fact_collateralmgtloan_expectations',
    python_callable=fact_collateralmgtloan,
    dag=dag,
)

fact_collateralmgtloan_breakdown_script = PythonOperator(
    task_id='fact_collateralmgtloan_breakdown_expectations',
    python_callable=fact_collateralmgtloan_breakdown,
    dag=dag,
)

# Set up the task dependency, if necessary
fact_collateralmgtloan_breakdown_script >> dim_client_script  >> fact_collateralmgtloan_script

