{{ config (materialized = 'table') }}

with
upto_nov3_2024_commodity_prices_and_weight as (
	select price.date
			, price.commodity_code
			, price.commodity_name commodity
			, price.commodity_closing_price closing_price_commodity_kg
			, price.commodity_closing_price * 1000 closing_price_commodity_mt
			, case when commodity_name = 'Maize Feed Grade - White' then 0.575
					when commodity_name = 'Paddy Rice Long Grain' then 0.2046
					when commodity_name = 'Soybean' then 0.1377
					when commodity_name = 'Sorghum' then 0.0827
				else 0
				end as commodity_weight
			, case when extract(month from date) >= 10 
					then concat(extract(year from date)::text, '/', extract(year from date + INTERVAL '1 year')::text)
				else concat(extract(year from date - INTERVAL '1 year')::text, '/', extract(year from date)::text)
				end as season
			, extract(dow from date::timestamp) dow
			, concat(extract(year from date::timestamp), '_', extract(week from date::timestamp)) woy
			, concat('Q', extract(quarter from date::timestamp), ' ', extract(year from date::timestamp)) quarter

	from {{ ref ('stg_commodities_closing_prices') }} price

	where commodity_name in ('Maize Feed Grade - White'
								, 'Paddy Rice Long Grain'
								, 'Soybean'
								, 'Sorghum')

	),

upto_nov3_2024_index as(	
	
	select *
		, case when dow = 0 then -- Saturday
					lead(woy, 1) over (partition by commodity order by date)
				when dow = 6 then -- Sunday
					lead(woy, 2) over (partition by commodity order by date)
			else woy
			end adj_woy -- To ensure that the saturday and sunday belong to the new week
		, case when extract(dow from date::timestamp) = 6 then 0 
				when extract(dow from date::timestamp) = 0 then 1
				when extract(dow from date::timestamp) = 1 then 2
				when extract(dow from date::timestamp) = 2 then 3
				when extract(dow from date::timestamp) = 3 then 4
				when extract(dow from date::timestamp) = 4 then 5
				when extract(dow from date::timestamp) = 5 then 6 
				end adj_dow -- Week starts on Sat & ends on Fri and by default dow is labeled from Mon - Sun (1 - 0)
		, sum(closing_price_commodity_mt * commodity_weight) over (partition by date order by date) index
	
	from upto_nov3_2024_commodity_prices_and_weight
	),

upto_nov3_2024_new_aci as (
	select *
			, ((index / 125240.44611) * 100) aci --Base period = Dec 1, 2016 (12524.044611 per bag) for ACI
			, case when commodity = 'Maize Feed Grade - White' then ((closing_price_commodity_mt / 123803.5) * 100) 
					when commodity = 'Paddy Rice Long Grain' then ((closing_price_commodity_mt / 130000.0) * 100)
					when commodity = 'Soybean' then ((closing_price_commodity_mt / 151249.3) * 100)
					when commodity = 'Sorghum' then ((closing_price_commodity_mt / 80150.0) * 100) 
					end sub_index
			, concat(adj_woy, '_', adj_dow) wow_identifier
	
	from upto_nov3_2024_index
	
	),

upto_nov3_2024_all_aci as (
    -- Chose to use old_aci up till the beginning of 2023/2024 season which started on 01/10/2023

	select season, date, commodity, commodity_code, price_kg, price_mt, sub_index, aci_index, aci, adj_woy, quarter
	
	from {{ ref ('historical_aci_upto_2023_09_30') }}
	where date < '2023-10-01'
	
	UNION
	
	select season, date, commodity, commodity_code, closing_price_commodity_kg price_kg, closing_price_commodity_mt price_mt
			, sub_index, index aci_index, aci, adj_woy, quarter
			
	from upto_nov3_2024_new_aci 
	where date between '2023-10-01' and '2024-11-03' -- Changed new period start date to first Monday in November
	)
	
select *
from upto_nov3_2024_all_aci