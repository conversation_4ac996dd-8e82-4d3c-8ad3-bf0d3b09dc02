
-- CLIENT DAILY PORTFOLIO BALANCE
-- *** NOTE This query is to recreate the historic table only and is not meant to update the table. There is another script to achieve this incremental updates***

-- -- Create fact_clientdailyportfoliobalance Table
DROP TABLE exchange_mart.fact_clientdailyportfoliobalance;

CREATE TABLE exchange_mart.fact_clientdailyportfoliobalance (
				portfolio_date DATE, 
				cid VARCHAR(25), 
				security_name TEXT, 
				security_code TEXT, 
				security_type TEXT, 
				location_code TEXT, 
				location_name TEXT, 
				location_state TEXT, 
				latest_lien_units DOUBLE PRECISION, 
				latest_available_units DOUBLE PRECISION, 
				latest_total_units DOUBLE PRECISION,
				latest_trans_at TIMESTAMP
	);
	

-- -- Insert Historic Data Into The Table
INSERT INTO exchange_mart.fact_clientdailyportfoliobalance (portfolio_date, cid, security_name, security_code, 
															security_type, location_code, location_name, 
															location_state, latest_lien_units, latest_available_units, latest_total_units,
															latest_trans_at)


-- Adjust the location_id, so that we have id = 0 where the id is null, to enable us join properly as joins will not work on null
with base_table as (

	select sec.security_type, sec.security_code, DATE(logg.created) transaction_date, logg.client_id,
			logg.transaction_type, logg.security_id, coalesce(logg.location_id, 0) as adj_location_id,
			logg.units, logg.lien_units_before, logg.lien_units_after, logg.available_units_before, logg.available_units_after,
			logg.total_units_before, logg.total_units_after, logg.created, logg.updated
	from ovs.operation_portfoliologging logg
	left join ovs_mart.dim_security sec
	on logg.security_id = sec.id
	where not (sec.security_type = 'Dawa' and logg.location_id is null )
	
	),

	
-- Create a index to identify each transaction of a client per day and per security and per security location
transaction_index as (
	select row_number() over (partition by transaction_date, client_id, security_id, adj_location_id order by created desc) daily_index, *
	from base_table
	),

	
-- Because we are only concerned with the portfolio balance per day,
	-- we need to retrieve only the last transaction of a client per day and per security and per security location
indexed_table as (
	select *
	from transaction_index
	where daily_index = 1
	),

	
-- We need the balance per day, so we need a date field that caters to every single day, that would be the portfolio_date
	-- Generate a table with all dates for each client and joined to all the fields that differentiate each portfolio value (dlient_id, security, location)
all_dates_fields as ( 
	select date(date_dim.date_actual) as portfolio_date,
			portfolio_fields.client_id, portfolio_fields.security_id, portfolio_fields.adj_location_id

	from afex_gen_mart.dim_date date_dim
	cross join (select distinct client_id, security_id, adj_location_id from indexed_table) portfolio_fields

	where date_dim.date_actual between '2013-01-01' and (current_date - 1)
	),

	
-- Join all dates cte with the indexed_table for each client. This should result in days with some null values for that client's security and security location
	-- Create a identifier in the form of running total, to identify the latest balance for each day including days without any transaction
everyday_and_transaction_day_only as (
	select adf.portfolio_date, adf.client_id, adf.security_id, sec.security_name, sec.security_code, sec.security_type, adf.adj_location_id,
			client.cid, loc.code location_code, loc.name location_name, loc.state location_state,
			indexed_table.transaction_date, indexed_table.transaction_type,  
			indexed_table.units, indexed_table.lien_units_before, indexed_table.lien_units_after, indexed_table.available_units_before, indexed_table.available_units_after,
			indexed_table.total_units_before, indexed_table.total_units_after, indexed_table.created, indexed_table.updated,

			case when units is null then 0 else 1 end transaction_boolean_identifier,
			sum(case when units is null then 0 else 1 end) 
				over (partition by adf.client_id, adf.security_id, adf.adj_location_id order by adf.portfolio_date) running_total_trans_day

	from all_dates_fields adf
	left join indexed_table 
	on adf.portfolio_date = indexed_table.transaction_date
		and adf.client_id = indexed_table.client_id
		and adf.security_id = indexed_table.security_id
		and adf.adj_location_id = indexed_table.adj_location_id
	left join exchange_mart.dim_security sec
	on adf.security_id = sec.id
	left join ovs.crm_client client
	on adf.client_id = client.id
	left join ovs.crm_location loc
	on adf.adj_location_id = loc.id
	),


-- Carry forward the last known balance
final_data as (
	select *,
			first_value(lien_units_after) over (partition by cid, security_id, adj_location_id, running_total_trans_day order by cid, portfolio_date) latest_lien_units,
			first_value(available_units_after) over (partition by cid, security_id, adj_location_id, running_total_trans_day order by cid, portfolio_date) latest_available_units,
			first_value(total_units_after) over (partition by cid, security_id, adj_location_id, running_total_trans_day order by cid, portfolio_date) latest_total_units,
			coalesce(created, (first_value(created) over (partition by cid, security_id, adj_location_id, running_total_trans_day order by cid, portfolio_date))) latest_trans_at

	from everyday_and_transaction_day_only
	
	)

select portfolio_date, cid, security_name, security_code, security_type, location_code, location_name, location_state, 
		latest_lien_units, latest_available_units, latest_total_units, latest_trans_at
from final_data
	order by portfolio_date desc, client_id, security_id, adj_location_id