from datetime import datetime,timedelta
import pendulum
from airflow import DAG
from airflow.operators.bash  import BashOperator
from custom_functions.airflow_email_util import failure_email


PATH_TO_DBT_VENV = "/usr/local/airflow/dbt_venv/bin/activate"
PATH_TO_DBT_PROJECT = "/usr/local/airflow/include/dbt/ml_mart"
PATH_TO_DBT_PROFILE = "/usr/local/airflow/include/dbt/.dbt"

default_args  = {
     'owner' : 'Oluwatomisin Soetan',
     'retries' : 0,
     'retry_delay' : timedelta(minutes=2),
     'on_failure_callback': failure_email
}
with DAG(
    dag_id = 'dbt_ml_mart_dag',
    default_args = default_args,
    description =  'ml_mart_dag',
    start_date= pendulum.datetime(2023, 10, 11, tz="Africa/Lagos"),
    catchup=False,
    schedule_interval =  '0 19 * * *',
    tags =  ['dbt','ml_mart']
) as dag:
    task1 =  BashOperator(
        task_id = 'first_task',
        bash_command = 'source $PATH_TO_DBT_VENV  && export DBT_PROFILES_DIR=$PATH_TO_DBT_PROFILE && dbt deps  && dbt run', 
        cwd=PATH_TO_DBT_PROJECT,
        env={"PATH_TO_DBT_VENV": PATH_TO_DBT_VENV,"PATH_TO_DBT_PROFILE":PATH_TO_DBT_PROFILE},
    )


   
task1
