"""
Description: <PERSON><PERSON>t to load datawarehouse metatada into vectore databases for sherlock consumption
Author: <PERSON>
Created: 13 March, 2025

"""
from sqlalchemy import create_engine
from urllib.parse import quote_plus
from dotenv import load_dotenv, find_dotenv
import os
import vecs

load_dotenv(find_dotenv())


def create_engine_to_source_datawarehouse():
    POSTGRES_ADDRESS= os.getenv('POSTGRES_ADDRESS')
    POSTGRES_DBNAME=os.getenv("POSTGRES_DBNAME")
    POSTGRES_USERNAME=os.getenv("POSTGRES_USERNAME")
    POSTGRES_PASSWORD=os.getenv("POSTGRES_PASSWORD")
    POSTGRES_PORT=os.getenv("POSTGRES_PORT")
   
    con_str = f"postgresql://{POSTGRES_USERNAME}:{quote_plus(POSTGRES_PASSWORD)}@{POSTGRES_ADDRESS}:{POSTGRES_PORT}/{POSTGRES_DBNAME}"
    engine = create_engine(con_str, connect_args={"options": "-csearch_path=sherlock"})

    return engine

def get_vector_database_collection(collection):
    SUPABASE_PASSWORD= os.getenv("SUPABASE_PASSWORD")
    SUPABASE_CONN_STRING = os.getenv("SUPABASE_CONN_STRING")
    DIMENSION = 1536
    connection_string = SUPABASE_CONN_STRING.format(SUPABASE_PASSWORD=SUPABASE_PASSWORD)
    vx =  vecs.create_client(connection_string)
    docs = vx.get_or_create_collection(name=collection, dimension=DIMENSION)
    return docs