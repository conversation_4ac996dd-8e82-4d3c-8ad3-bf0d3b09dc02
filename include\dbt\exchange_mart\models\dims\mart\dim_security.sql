{{ config (materialized = 'table') }}

-- MOVE THIS DESCRIPTION TO A DESCRIPTION DOCUMENT: This is necessary, because it was discovered that some securities were wrongly matched with security types in ovs.crm_security
-- In an attempt to correct for this, this dim table has been created and will be further upgraded as dimmed necessary.
With
dim_security as (
	select sec.id, sec.code security_code, sec.name security_name,
			case when board.name = 'SCIT' or board.name = 'Virtual' then board.name
				when sec.code like 'O%' or sec.code like 'S%' then board.name
				when sec.code ilike 'D%' then sec.security_type
				when sec.name ilike '%commodities%' or sec.name like '%ETC%' then 'ETC'
			else 'FI' 
			end as security_type,
	
			case when board.name = 'Spot' and 
						not (sec.code like 'S%' or sec.code like 'D%' or sec.name like '%ETC%' or sec.name ilike '%commodities%')
					then 'Fixed Income'
			else board.name
			end as board,
	
			sec.volume_per_unit,
			com.code commodity_code, com.name commodity_name,
			cur.code currency_code, cur.name currency_name,
			sec.is_virtual_security,
			sec.created
			   		
	from {{ source('ovs', 'crm_security') }} 
	sec
	left join {{ source('ovs', 'crm_board') }} 
	board
		on sec.board_id = board.id
	left join {{ source('ovs', 'crm_commodity') }} 
	com
		on sec.commodity_id = com.id
	left join {{ source('ovs', 'ecn_currency') }} 
	cur
		on sec.currency_id = cur.id
)

select 

	id, 
	security_code, 
	security_name,
	security_type,
	board,
	volume_per_unit,
	commodity_code, 
	commodity_name,
	currency_code, 
	currency_name,
	is_virtual_security,
	created

from dim_security 
order by board, security_type, security_name
