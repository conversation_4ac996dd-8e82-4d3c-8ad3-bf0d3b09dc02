
import configparser
import ast
from datetime import datetime, date, timedelta
import pandas as pd
from sqlalchemy import text
import os
from credentials import  db_conn


dir_path = os.path.dirname(os.path.realpath('__file__'))

config = configparser.ConfigParser()
config.read(f"{dir_path}/config.ini")

config_source = 'MODELED_SOURCE_DB'
config_target = 'MODELED_TARGET_DB'
# connection
source_engine,conn_source =  db_conn(conn_param=config_source)
#target_engine,conn_target =  db_conn(conn_param=config_source)

# Define the query to check for existing records for today's date
check_query = """
SELECT COUNT(*) as count
FROM exchange_mart.fact_exchange_client_walletbalance
WHERE DATE(wallet_date) = (CURRENT_DATE - 1)"""


# Execute the check query
existing_count = pd.read_sql_query(check_query, source_engine).iloc[0]['count']


# If there are existing records for today, delete them
if existing_count > 0:
    delete_query = """
    DELETE FROM exchange_mart.fact_exchange_client_walletbalance
    WHERE DATE(wallet_date) = (CURRENT_DATE - 1)
    """
    with source_engine.connect() as connection:
        connection.execute(text(delete_query))
        

# Get the latest wallet balance for all clients
latest_balance = """
with base_table as (
	select client.cid,
			DATE(trans.created) wallet_date,
		    trans.*
                
    from csd.wallet_walletoperationlogging trans
    left join csd.crm_client client 
    on trans.client_id = client.id

    WHERE trans.is_deleted is false

	),
indexed_table as (
    select *, row_number() over (partition by cid, currency order by created desc) daily_index
	from base_table )

select wallet_date, cid, currency,  
		total_after as total_wallet_balance, lien_after as lien_wallet_balance,
		available_after as available_wallet_balance, created as latest_trans_at
	
from indexed_table
where daily_index = 1 """
hot_data = pd.read_sql_query(latest_balance,source_engine)

fin_data = hot_data[[ 'wallet_date',
                        'cid',
                        'currency',
                        'total_wallet_balance',
                        'lien_wallet_balance',
                        'available_wallet_balance',
                        'latest_trans_at' ]]

# make the current date the inventory date
yesterday = (date.today()) - timedelta(days=1)
fin_data.wallet_date = yesterday.strftime("%Y-%m-%d")
print('\n\n\ntoday_date :',yesterday)

fin_data.to_sql('fact_exchange_client_walletbalance',source_engine, schema= 'exchange_mart',index=False, if_exists="append")