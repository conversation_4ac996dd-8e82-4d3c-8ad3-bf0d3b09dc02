{{ config(materialized='ephemeral') }}

-- Rank the client action based on their activity date - The most recent action is ranked first
WITH ranked_activities AS (
	SELECT 
		action_date,
		cid,
		action_type,
		ROW_NUMBER() OVER (PARTITION BY cid ORDER BY action_date DESC) action_rank
	FROM {{ ref('stg_client_wallet_trade_stream') }}
	ORDER BY cid, action_rank
	
	),


-- Extract clients most recent activity
client_last_action AS (
	SELECT 
		cid,
		action_date last_action_time,
		action_type last_action_done,
		AGE(NOW(), action_date) time_since_last_action
	FROM ranked_activities
	WHERE action_rank = 1
	)
	
SELECT * FROM client_last_action
