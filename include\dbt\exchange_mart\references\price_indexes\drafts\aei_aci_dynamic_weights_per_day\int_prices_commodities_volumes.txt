/*
with
agg_vol as(
	-- Goal for this intermediate model is to derive the volume per commodity in connection to a date_dim table,
    -- to ensure a value is got for everyday, whether there was a transaction or not
	select executed_date
			, commodity
			, sum(executed_volume_kg) executed_volume_kg
	
	from {{ ref ('stg_aci_aei_fact_tradetransactions') }}
	
	group by executed_date, commodity
	order by executed_date, commodity
	),

vol_and_date as(
	select dim_date.date_actual date_
			, comm.commodity commodity_
			, agg_vol.*
	
	from {{ source ('trade_mart', 'dim_date') }} dim_date
	cross join (select distinct commodity from agg_vol) comm
	left join agg_vol
		on dim_date.date_actual = agg_vol.executed_date
		and comm.commodity = agg_vol.commodity
	
	where dim_date.date_actual between '2020-01-01' and current_date
	),

price_and_vol as (
	-- The goal here is to derive the weights that would be used in estimating the index values
	-- Join the volumes and prices and get proper commodity code labels.
	select vol_and_date.date_
		, vol_and_date.commodity_
		, comm.code
		, price.commodity_closing_price closing_price_commodity_kg
		, coalesce(vol_and_date.executed_volume_kg, 0) executed_volume_kg
		, case 
				when date_ = first_value(date_) over (order by date) and commodity_ = 'Maize Feed Grade - White' then 12700000
				when date_ = first_value(date_) over (order by date) and commodity_ = 'Paddy Rice Long Grain' then 8400000
				when date_ = first_value(date_) over (order by date) and commodity_ = 'Soybean' then 1050000
				when date_ = first_value(date_) over (order by date) and commodity_ = 'Sorghum' then 6670000
                when date_ = first_value(date_) over (order by date) and commodity_ = 'Cocoa' then 248450
				when date_ = first_value(date_) over (order by date) and commodity_ = 'Ginger Dried Split' then 645000
				
				when date_ = '2020-10-01' and commodity_ = 'Maize Feed Grade - White' then 11900000
				when date_ = '2020-10-01' and commodity_ = 'Paddy Rice Long Grain' then 8010000
				when date_ = '2020-10-01' and commodity_ = 'Soybean' then 1106000
				when date_ = '2020-10-01' and commodity_ = 'Sorghum' then 6590000
                when date_ = '2020-10-01' and commodity_ = 'Cocoa' then 340160
				when date_ = '2020-10-01' and commodity_ = 'Ginger Dried Split' then 726000
	
				when date_ = '2021-10-01' and commodity_ = 'Maize Feed Grade - White' then 12300000
				when date_ = '2021-10-01' and commodity_ = 'Paddy Rice Long Grain' then 8810000
				when date_ = '2021-10-01' and commodity_ = 'Soybean' then 1194000
				when date_ = '2021-10-01' and commodity_ = 'Sorghum' then 6810000
                when date_ = '2021-10-01' and commodity_ = 'Cocoa' then 333360
				when date_ = '2021-10-01' and commodity_ = 'Ginger Dried Split' then 768000
				
				when date_ = '2022-10-01' and commodity_ = 'Maize Feed Grade - White' then 10800000
				when date_ = '2022-10-01' and commodity_ = 'Paddy Rice Long Grain' then 8020000
				when date_ = '2022-10-01' and commodity_ = 'Soybean' then 1279000
				when date_ = '2022-10-01' and commodity_ = 'Sorghum' then 6320000
                when date_ = '2022-10-01' and commodity_ = 'Cocoa' then 310020
				when date_ = '2022-10-01' and commodity_ = 'Ginger Dried Split' then 837120
				
				when date_ = '2023-10-01' and commodity_ = 'Maize Feed Grade - White' then 11600000
				when date_ = '2023-10-01' and commodity_ = 'Paddy Rice Long Grain' then 8310000
				when date_ = '2023-10-01' and commodity_ = 'Soybean' then 1266000
				when date_ = '2023-10-01' and commodity_ = 'Sorghum' then 6190000
                when date_ = '2023-10-01' and commodity_ = 'Cocoa' then 296380
				when date_ = '2023-10-01' and commodity_ = 'Ginger Dried Split' then 75300
	
			else null
			end prior_national_vol_mt 
            -- these volumes are the national volume from the previous period.
            -- This was gotten from the market research team, with date sourced from FAOSTAT (The statistics arm of the Food and Agriculture Organization of the United Nations.)

	from vol_and_date
	left join {{ ref ('int_prices_ranked_fornulldays_closing_prices') }} price
		on vol_and_date.date_ = price.date
		and vol_and_date.commodity_ = price.commodity_name 
	left join {{ source ('ovs', 'crm_commodity') }} comm
		on vol_and_date.commodity_ = comm.name


	),
	
adj_vol as (
	select case when extract(month from date_) >= 10 
					then concat(extract(year from date_)::text, '/', extract(year from date_ + INTERVAL '1 year')::text)
				else concat(extract(year from date_ - INTERVAL '1 year')::text, '/', extract(year from date_)::text)
				end season
			, date_ date
			, commodity_ commodity
			, code commodity_code
			, closing_price_commodity_kg
			, closing_price_commodity_kg * 1000 closing_price_commodity_mt
			, executed_volume_kg/1000 exchange_executed_vol_mt		
			, coalesce(prior_national_vol_mt, 0) prior_national_vol_mt 
	
	from price_and_vol
	)

select * 

from adj_vol
*/
