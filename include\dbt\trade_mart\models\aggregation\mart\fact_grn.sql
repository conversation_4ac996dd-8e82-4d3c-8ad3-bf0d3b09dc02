



{{ config(materialized='table') }}

with grn_farmer as  -- farmer grn from wb2
	(
	select 
			 concat('001-',farmer.folio_id) cid,
			 grn_f.bags,
			 case 	when grn_f.grade = 'G1' then '1'
					when grn_f.grade = 'G2' then '2'
					when grn_f.grade = 'G3' then '3'
			 else grn_f.grade
			 end as grade,
			 grn_f.grn_id,
			 grn_f.receipt_id,
			 grn_f.created,
			 grn_f.updated,
			 grn_f.gross_weight * 1000 as "gross_weight",
			 grn_f.net_weight * 1000 as "net_weight",
			 grn_f.deduction,
			 grn_f.total_deduction,
			 grn_f.moisture,
			 grn_f.total_commodity_price ,
			 grn_f.price_per_tonne,
			 grn_f.transaction_type,
			 grn_f.approval_permissions,
			 grn_f.approval_done,
			 grn_f.is_approved,
			 grn_f.is_approval_completed,
			 grn_f.approval_date,
			 grn_f.is_received_at_warehouse,
			 grn_f.is_reverted,
			 grn_f.rejection_reason,
			 grn_f.total_payable_price,
			 grn_f.transaction_fees,
			 grn_f.is_processed,
			 null as is_disabled_for_listing ,
			 grn_f.spot_payment,
			 grn_f.employee_id,
			 grn_f.cash_advance_account_pk,
			 item.item_name,
			 item.item_code,
			 item.item_type,
			 concat('001-',grn_f.warehouse_id) as warehouse_code ,
			 'wb2' as source

	from {{source('workbench2','inventory_goodsreceiptline')}}  grn_f   
	left join {{source('workbench2','workbench_farmer')}}   farmer
	on grn_f.farmer_id = farmer.id
	left join {{source('workbench2','inventory_item')}}   item
	on grn_f.item_id = item.id),

 grn_client as  -- client grn from wb2
	(
	select 	 client.client_id cid,
			 grn_c.bags,
			 case 	when grn_c.grade = 'G1' then '1'
					when grn_c.grade = 'G2' then '2'
					when grn_c.grade = 'G3' then '3'
			 else grn_c.grade
			 end as grade,
			 grn_c.grn_id,
			 grn_c.receipt_id,
			 grn_c.created,
			 grn_c.updated,
			 grn_c.gross_weight * 1000 as "gross_weight", -- weight in wb2 was in metric tonnes
			 grn_c.net_weight * 1000 as "net_weight",
			 grn_c.deduction,
			 grn_c.total_deduction,
			 grn_c.moisture,
			 grn_c.total_commodity_price,
			 grn_c.price_per_tonne,
			 grn_c.transaction_type,
			 grn_c.approval_permissions,
			 grn_c.approval_done,
			 grn_c.is_approved,
			 grn_c.is_approval_completed,
			 grn_c.approval_date,
			 grn_c.is_received_at_warehouse , 
			 grn_c.is_reverted,
			 grn_c.rejection_reason,
			 grn_c.total_payable_price,
			 grn_c.transaction_fees,
			 grn_c.is_processed,
			 null as is_disabled_for_listing ,
			 grn_c.spot_payment,
			 grn_c.employee_id,
			 grn_c.cash_advance_account_pk,
			 item.item_name,
			 item.item_code,
			 item.item_type, 
			 concat('001-',grn_c.warehouse_id) as warehouse_code,
			 'wb2' as source

	from  {{source('workbench2','inventory_goodsreceiptlineclient')}}     grn_c
	left join {{source('workbench2','crm_client')}}  client
	on grn_c.client_id = client.id
	left join {{source('workbench2','inventory_item')}} item
	on grn_c.item_id = item.id
),

grn_wb2 as ( -- merged farmer and client grn from wb2
	select *
	from grn_farmer   --4675  -- 37518
	union all
	select *
	from grn_client 
	),
		
item_table as (  -- Item table from wb3
	select	item.id,
			product.name item_name ,
			product.code item_code,
			product.product_type item_type
	
	from {{source('workbench','inventory_item')}} item
	inner join  {{source('workbench','workbench_product')}}  product
	on item.product_id = product.id),

grn3_tab as (  -- grn from wb3 merged with wb3 item table
	select 	client.cid,
			grn3.bags ,
			grn3.grade ,
			grn3.grn_id ,
			grn3.receipt_id ,
			grn3.created ,
			grn3.updated ,
			grn3.gross_weight ,
			grn3.net_weight ,
			grn3.deduction ,
			grn3.total_deduction ,
			grn3.moisture ,
			grn3.total_commodity_price ,
			grn3.price_per_tonne ,
			grn3.transaction_type ,
			grn3.approval_permissions ,
			grn3.approval_done ,
			grn3.is_approved ,
			grn3.is_approval_completed ,
			grn3.approval_date,
			grn3.is_received_at_warehouse ,
			grn3.is_reverted,
			grn3.rejection_reason ,
			grn3.total_payable_price ,
			grn3.transaction_fees ,
			grn3.is_processed ,
			grn3.is_disabled_for_listing ,
			grn3.spot_payment,
			null as employee_id ,
			null as cash_advance_account_pk ,
			item_table.item_name,
			item_table.item_code,
			item_table.item_type, 
			wh.code warehouse_code,
			grn3.is_deleted ,
			grn3.next_approval ,
			grn3.is_rejected ,
			grn3.rejected_date ,
			grn3.created_offline ,
			grn3.is_traded ,
			grn3.raised_for_farmer ,
			grn3.cash_advance_account_paid ,
			grn3.truck_no ,
			grn3.created_by_id ,
			grn3.goods_receipt_id ,
			grn3.rejected_by_id ,
			grn3.cash_advance_account_id ,
			grn3.additional_fees ,
			grn3.wm_updated ,
			grn3.is_clean ,
			grn3.revert_reason ,
			grn3.is_accounting_posted ,
			grn3.tenant_id ,
			grn3.is_uploaded ,
			grn3.certified ,
			grn3.is_edited ,
			grn3.payment_option ,
			grn3.preferred_bank_account_id ,
			grn3.discount ,
			'wb3' as source

	from  {{source('workbench','goods_receipt_goodsreceiptline')}}   grn3
	left join {{source('workbench','crm_client')}} client
	on grn3.client_id = client.id
	left join item_table 
	on grn3.item_id = item_table.id
	left join {{source('workbench','workbench_warehouse')}}     wh
	on grn3.warehouse_id = wh.id
 ),

grn_all as ( -- merge of wb2 and wb3 grn 
	
	select 
			grn_wb2.cid,
			grn_wb2.bags ,
			grn_wb2.grade ,
			grn_wb2.grn_id ,
			grn_wb2.receipt_id ,
			grn_wb2.created ,
			grn_wb2.updated ,
			grn_wb2.gross_weight ,
			grn_wb2.net_weight ,
			grn_wb2.deduction ,
			grn_wb2.total_deduction ,
			grn_wb2.moisture ,
			grn_wb2.total_commodity_price ,
			grn_wb2.price_per_tonne ,
			grn_wb2.transaction_type ,
			grn_wb2.approval_permissions ,
			grn_wb2.approval_done ,
			grn_wb2.is_approved ,
			grn_wb2.is_approval_completed ,
			grn_wb2.approval_date,
			grn_wb2.is_received_at_warehouse ,
			grn_wb2.is_reverted,
			grn_wb2.rejection_reason ,
			grn_wb2.total_payable_price ,
			grn_wb2.transaction_fees ,
			grn_wb2.is_processed ,
			null as is_disabled_for_listing ,
			grn_wb2.spot_payment,
			grn_wb2.employee_id ,
			grn_wb2.cash_advance_account_pk ,
			grn_wb2.item_name,
			grn_wb2.item_code,
			grn_wb2.item_type, 
			grn_wb2.warehouse_code,
			null as  is_deleted ,
			null as  next_approval ,
			null as  is_rejected ,
			null as  rejected_date ,
			null as  created_offline ,
			null as  is_traded ,
			null as  raised_for_farmer ,
			null as  cash_advance_account_paid ,
			null as  truck_no ,
			null as  created_by_id ,
			null as  goods_receipt_id ,
			null as  rejected_by_id ,
			null as  cash_advance_account_id ,
			null as  additional_fees ,
			null as  wm_updated ,
			null as  is_clean ,
			null as  revert_reason ,
			null as  is_accounting_posted ,
			null as  tenant_id ,
			null as  is_uploaded ,
			null as  certified ,
			null as  is_edited ,
			null as  payment_option ,
			null as  preferred_bank_account_id ,
			null as  discount ,
			grn_wb2.source
	
	from grn_wb2
	union all
	select 
			grn3_tab.cid,
			grn3_tab.bags ,
			grn3_tab.grade ,
			grn3_tab.grn_id ,
			grn3_tab.receipt_id ,
			grn3_tab.created ,
			grn3_tab.updated ,
			grn3_tab.gross_weight ,
			grn3_tab.net_weight ,
			grn3_tab.deduction ,
			grn3_tab.total_deduction ,
			grn3_tab.moisture ,
			grn3_tab.total_commodity_price ,
			grn3_tab.price_per_tonne ,
			grn3_tab.transaction_type ,
			grn3_tab.approval_permissions ,
			grn3_tab.approval_done ,
			grn3_tab.is_approved ,
			grn3_tab.is_approval_completed ,
			grn3_tab.approval_date,
			grn3_tab.is_received_at_warehouse ,
			grn3_tab.is_reverted,
			grn3_tab.rejection_reason ,
			grn3_tab.total_payable_price ,
			grn3_tab.transaction_fees ,
			grn3_tab.is_processed ,
			grn3_tab.is_disabled_for_listing ,
			grn3_tab.spot_payment,
			null as employee_id ,
			null as cash_advance_account_pk ,
			grn3_tab.item_name,
			grn3_tab.item_code,
			grn3_tab.item_type, 
			grn3_tab.warehouse_code,
			grn3_tab.is_deleted ,
			grn3_tab.next_approval ,
			grn3_tab.is_rejected ,
			grn3_tab.rejected_date ,
			grn3_tab.created_offline ,
			grn3_tab.is_traded ,
			grn3_tab.raised_for_farmer ,
			grn3_tab.cash_advance_account_paid ,
			grn3_tab.truck_no ,
			grn3_tab.created_by_id ,
			grn3_tab.goods_receipt_id ,
			grn3_tab.rejected_by_id ,
			grn3_tab.cash_advance_account_id ,
			grn3_tab.additional_fees ,
			grn3_tab.wm_updated ,
			grn3_tab.is_clean ,
			grn3_tab.revert_reason ,
			grn3_tab.is_accounting_posted ,
			grn3_tab.tenant_id ,
			grn3_tab.is_uploaded ,
			grn3_tab.certified ,
			grn3_tab.is_edited ,
			grn3_tab.payment_option ,
			grn3_tab.preferred_bank_account_id ,
			grn3_tab.discount ,
			grn3_tab.source
	from grn3_tab
)
	
	
	select *
	from grn_all
