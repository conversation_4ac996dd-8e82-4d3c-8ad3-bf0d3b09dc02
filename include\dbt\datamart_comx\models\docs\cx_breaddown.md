{% docs cx_fact_collateralmgtloan_breakdown_created %}
The timestamp when the collateral management loan breakdown record was created.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_updated %}
The timestamp when the collateral management loan breakdown record was last updated.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_client_id %}
The unique identifier of the client associated with the collateral management loan breakdown.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_financier_name %}
The name of the financier providing the loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_financier_accounttype %}
The account type of the financier providing the loan ('AFEX Investment Limited .', 'ASP Finance .', 'AFEX Investment Ltd.').
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_financier_phone %}
The contact phone number of the financier providing the loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_reference_id %}
A unique reference ID for the collateral management loan breakdown.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_loan_type %}
The type of loan provided for the collateral management ('ASP', 'Collateral').
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_deleted_at %}
The timestamp indicating when the collateral management loan breakdown was deleted.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_is_deleted %}
A flag indicating whether the collateral management loan breakdown is deleted.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_loan_value %}
The value of the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_tranche_name %}
The name of the loan tranche ('Trade Finance', 'Tranche 2', 'ABCP', 'Non-Collateral', 'Tranche 3', 'Tranche 1', 'Tranche-1', 'ASP Loan', 'ASP', 'REPO', 'Tranche-2').
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_tranche_value %}
The value of the loan tranche.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_tranche_tenure %}
The tenure of the loan tranche.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_is_released %}
A flag indicating whether the loan tranche is released.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_loan_tenure %}
The tenure of the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_bundle_fields %}
Fields related to loan bundle information.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_interest_rate %}
The interest rate associated with the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_is_ovs_synced %}
A flag indicating whether the loan is synced with OVS (Online Verification System).
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_is_released_at %}
The timestamp indicating when the loan tranche is released.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_loan_bundle_id %}
The unique identifier of the loan bundle associated with the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_is_below_margin %}
A flag indicating whether the loan is below margin.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_loan_start_date %}
The start date of the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_is_ovs_synced_at %}
The timestamp indicating when the loan is synced with OVS.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_date_below_margin %}
The timestamp indicating when the loan is below margin.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_initial_collateral_value %}
The initial value of the collateral.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_equity %}
The equity amount related to the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_data_capture %}
The data capture related to the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_logistic_fee %}
The logistic fee associated with the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_insurance_crg %}
The insurance CRG (Credit Risk Guarantee) associated with the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_interest_value %}
The interest value of the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_insurance_yield %}
The insurance yield associated with the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_repayment_value %}
The repayment value of the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_transaction_fee %}
The transaction fee associated with the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_actual_loan_value %}
The actual value of the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_cash_contribution %}
The cash contribution related to the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_value_chain_management %}
The value chain management related to the collateral management loan.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_units %}
The number of units associated with the collateral management loan breakdown.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_volume %}
The volume associated with the collateral management loan breakdown.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_security_location %}
The location of the security associated with the collateral management loan breakdown.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_security_location_state %}
The state of the security location associated with the collateral management loan breakdown.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_security_id %}
The unique identifier of the security associated with the collateral management loan breakdown.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_total_units %}
The total number of units associated with the collateral management loan breakdown.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_commodity_name %}
The name of the commodity associated with the collateral management loan breakdown.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_commodity_code %}
The code of the commodity associated with the collateral management loan breakdown.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_total_volume %}
The total volume associated with the collateral management loan breakdown.
{% enddocs %}

{% docs cx_fact_collateralmgtloan_breakdown_current_price %}
The current price associated with the collateral management loan breakdown.
{% enddocs %}
