version: 2

sources:
  - name: workbench2
    description : workbench2 data
    tables:
      - name: inventory_goodsreceiptline
      - name: inventory_goodsreceiptlineclient
      - name: inventory_item
      - name: crm_client
      - name: workbench_farmer

  - name: workbench
    description : workbench data
    tables:
      - name: crm_farmer
        description: contains farmer records
      - name: crm_client
        description: contains client records
      - name: workbench_cooperative
        description: contains tweet records
      - name: workbench_warehouse
      - name: workbench_cell
        description: contrains user records
      - name: crm_farmer_crop_type
        description: contains tweet records
      - name: inventory_item
        description: contrains user records
      - name: workbench_product
        description: contains tweet records
      - name: tenant_tenant
        description: contrains user records
      - name: location_location
        description: contains tweet records
      - name: location_baselocation
        description: contrains user records
      - name: location_state
        description: contains tweet records
      - name: location_region
        description: contrains user records
      - name: location_lga
      - name: location_country
      - name: loan_loanline
      - name: loan_loan
      - name: loan_loanbundle
      - name: project_project
      - name: goods_receipt_goodsreceiptline
      - name: crm_clientbankinformation
      - name: bank_bank
      - name: account_user
      - name: crm_farmlocation
      - name: crm_client_client_type
      - name: crm_clienttype
      - name: location_ward