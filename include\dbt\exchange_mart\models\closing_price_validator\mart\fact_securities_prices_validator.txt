{{ config (materialized = 'table') }}

with 
securities_with_location as (
	select distinct security_code, 
			case when substring(security_code, 2) = 'COC' then 'CCO'
			else substring(security_code, 2) 
			end commodity_code,
			case when security_type = 'OTC' then 'n/a'
			else location
			end location,
			CASE WHEN security_code LIKE '%CSN%' THEN 'Cashew Nuts'
					WHEN (security_code LIKE '%COC%' OR security_code LIKE '%CCO%') THEN 'Cocoa'
					WHEN security_code LIKE '%GNG%' THEN 'Ginger'
					WHEN security_code LIKE '%MAZ%' THEN 'Maize'
					WHEN security_code LIKE '%PRL%' THEN 'Paddy Rice'
					WHEN (security_code LIKE '%SSM%' OR  security_code LIKE '%SSC%') THEN 'Sesame'
					WHEN security_code LIKE '%SGM%' THEN 'Sorghum'
					WHEN security_code LIKE '%SBS%' THEN 'Soyabean'
					WHEN security_code LIKE '%WHT%' THEN 'Wheat'
			ELSE security_code
			END consolidated_commodity_name
	
	from {{ source ('ovs', 'databank_historicalprice')}}
		
	where security_type in ('Dawa', 'Spot', 'OTC') and (security_code like '%CSN%' or security_code like '%COC%' or security_code like '%CCO%' or 
							security_code like '%GNG%' or security_code like '%MAZ%' or security_code like '%PRL%' or 
							security_code like '%SSM%' or security_code like '%SSC%' or security_code like '%SGM%' or 
							security_code like '%SBS%' or security_code like '%WHT%')
	),
	
security_closing_price as (
	select sec_date.date_actual, sec_with_loc.*, loc.state, 
			com_price.commodity_acp_fixed_weight,
			coalesce(log_grid."Logistics Differential per kg", 0) logistics_differential,
			(com_price.commodity_acp_fixed_weight - coalesce(log_grid."Logistics Differential per kg", 0)) security_acp

	from {{ ref ('stg_date') }} sec_date
	cross join securities_with_location sec_with_loc
	left join {{ ref ('fact_commodities_prices_validator')}} com_price
		on sec_with_loc.commodity_code = com_price.commodity_code
		and sec_date.date_actual = com_price.date_actual
	left join {{ source ('ovs', 'crm_location') }} loc
		on sec_with_loc.location = loc.name
	left join {{ ref ('stg_logistics_grid') }} log_grid
		on sec_with_loc.consolidated_commodity_name = log_grid."Commodity"
		and loc.state = log_grid."Destination Location"
    )

select *
from security_closing_price